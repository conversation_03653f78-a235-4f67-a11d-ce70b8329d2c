Ok that’s all good , but we have to carefully add the feature to our current app while maintaining our current functionality , just refine what I’m telling as a new feature prd, , that just does that so our current workflow is /record , and then it appears in the consulations list in /dashboard , and then they open the consulation modal pop up and click generate summary after adding any images if necessary , so updated flow should be , same initial flow , docotor does /record , (add a new input  text field also that the doctor can add any text if necessary(make it optional not required in supabase)  ), then it once they click submit ,it updates in the dashboard , and then , when the click and open the new consulation , and the pop up appears ,  ,, so right now we add a additional drop down in the consultation modal pop up(add it in the header , towards the right side , )   which  lets , the doctors select either the current outpatient , or discharge or surgery option ,and then let’s add a text field in addition to the current fields also ( let this be there but it’s not required by supabase (same like the text field in the /record )) , which lets the user add any additional text they want to send to the agent also , and so the agent now processes the text ( giving it same priority as audio ) in addition to the current implementation . And then instead of adding supabase templates , let’s just add templates in our codebase backend for our agent prompt templates , which links to whatever they selected so the outpatient uses the current soap concise template while the discharge and surgery uses their own respective bullet points to paragraph  enhancement while also keeping the documentation formatting , the medical terms names , the medicine names , all accurate and any ambiguity can be alerted to the user with bold text or red text or something or anything the agent is not sure of , , and every other functionality works the same , let’s hope for a seamless integration , this integration will be handled by a ai coding agent working in the codebase  and clearly details what I’m telling you , , do not change existing working functionality .