#!/usr/bin/env python3

import os
import subprocess
import sys
from pathlib import Path

def create_directories():
    """Create all necessary directories"""
    directories = [
        'favicons',
        'app-icons', 
        'social',
        'high-res',
        'apple-touch',
        'android',
        'windows',
        'web'
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"📁 Created directory: {dir_name}")

def generate_with_rsvg():
    """Try to use rsvg-convert if available"""
    try:
        subprocess.run(['rsvg-convert', '--version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def generate_with_imagemagick():
    """Try to use ImageMagick convert if available"""
    try:
        subprocess.run(['convert', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def generate_with_inkscape():
    """Try to use Inkscape if available"""
    try:
        subprocess.run(['inkscape', '--version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def generate_png_files():
    """Generate PNG files using available tools"""
    
    # All the sizes we need
    logo_sizes = {
        # Favicons
        'favicons/favicon-16x16.png': 16,
        'favicons/favicon-32x32.png': 32,
        'favicons/favicon-48x48.png': 48,
        'favicons/favicon-64x64.png': 64,
        
        # App icons (PWA/Mobile)
        'app-icons/icon-72x72.png': 72,
        'app-icons/icon-96x96.png': 96,
        'app-icons/icon-128x128.png': 128,
        'app-icons/icon-144x144.png': 144,
        'app-icons/icon-152x152.png': 152,
        'app-icons/icon-192x192.png': 192,
        'app-icons/icon-256x256.png': 256,
        'app-icons/icon-384x384.png': 384,
        'app-icons/icon-512x512.png': 512,
        
        # Apple Touch Icons
        'apple-touch/apple-touch-icon-57x57.png': 57,
        'apple-touch/apple-touch-icon-60x60.png': 60,
        'apple-touch/apple-touch-icon-72x72.png': 72,
        'apple-touch/apple-touch-icon-76x76.png': 76,
        'apple-touch/apple-touch-icon-114x114.png': 114,
        'apple-touch/apple-touch-icon-120x120.png': 120,
        'apple-touch/apple-touch-icon-144x144.png': 144,
        'apple-touch/apple-touch-icon-152x152.png': 152,
        'apple-touch/apple-touch-icon-167x167.png': 167,
        'apple-touch/apple-touch-icon-180x180.png': 180,
        
        # Android Icons
        'android/android-chrome-36x36.png': 36,
        'android/android-chrome-48x48.png': 48,
        'android/android-chrome-72x72.png': 72,
        'android/android-chrome-96x96.png': 96,
        'android/android-chrome-144x144.png': 144,
        'android/android-chrome-192x192.png': 192,
        'android/android-chrome-256x256.png': 256,
        'android/android-chrome-384x384.png': 384,
        'android/android-chrome-512x512.png': 512,
        
        # Windows Tiles
        'windows/mstile-70x70.png': 70,
        'windows/mstile-144x144.png': 144,
        'windows/mstile-150x150.png': 150,
        'windows/mstile-310x150.png': 310,  # Will be 310x310 for square
        'windows/mstile-310x310.png': 310,
        
        # Social Media
        'social/social-square-400x400.png': 400,
        'social/social-square-800x800.png': 800,
        
        # High Resolution
        'high-res/logo-1024x1024.png': 1024,
        'high-res/logo-2048x2048.png': 2048,
        'high-res/logo-4096x4096.png': 4096,
        
        # Web sizes
        'web/logo-small-64x64.png': 64,
        'web/logo-medium-128x128.png': 128,
        'web/logo-large-256x256.png': 256,
    }
    
    svg_file = "celer-ai-logo.svg"
    if not os.path.exists(svg_file):
        print(f"❌ {svg_file} not found")
        return False
    
    # Try different conversion tools
    converter = None
    if generate_with_rsvg():
        converter = 'rsvg'
        print("✅ Using rsvg-convert")
    elif generate_with_imagemagick():
        converter = 'imagemagick'
        print("✅ Using ImageMagick")
    elif generate_with_inkscape():
        converter = 'inkscape'
        print("✅ Using Inkscape")
    else:
        print("❌ No SVG conversion tool found")
        print("Please install one of: rsvg-convert, ImageMagick, or Inkscape")
        return False
    
    print(f"\n🔄 Generating {len(logo_sizes)} PNG files...")
    
    success_count = 0
    for output_path, size in logo_sizes.items():
        try:
            if converter == 'rsvg':
                cmd = ['rsvg-convert', '-w', str(size), '-h', str(size), svg_file, '-o', output_path]
            elif converter == 'imagemagick':
                cmd = ['convert', svg_file, '-resize', f'{size}x{size}', output_path]
            elif converter == 'inkscape':
                cmd = ['inkscape', svg_file, '--export-png', output_path, '--export-width', str(size), '--export-height', str(size)]
            
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"✅ Generated: {output_path} ({size}x{size})")
            success_count += 1
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to generate {output_path}: {e}")
        except Exception as e:
            print(f"❌ Error generating {output_path}: {e}")
    
    # Generate Open Graph image (1200x630)
    try:
        og_output = 'social/og-image-1200x630.png'
        if converter == 'rsvg':
            cmd = ['rsvg-convert', '-w', '1200', '-h', '630', svg_file, '-o', og_output]
        elif converter == 'imagemagick':
            cmd = ['convert', svg_file, '-resize', '1200x630!', og_output]
        elif converter == 'inkscape':
            cmd = ['inkscape', svg_file, '--export-png', og_output, '--export-width', '1200', '--export-height', '630']
        
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"✅ Generated: {og_output} (1200x630)")
        success_count += 1
        
    except Exception as e:
        print(f"❌ Failed to generate OG image: {e}")
    
    print(f"\n📊 Generation Summary:")
    print(f"   Successfully generated: {success_count}/{len(logo_sizes) + 1} files")
    
    return success_count > 0

def main():
    print("🎨 Celer AI Complete Logo Generator")
    print("===================================")
    
    create_directories()
    
    if generate_png_files():
        print("\n✅ Logo generation complete! 🚀")
        print("📁 All logo files are organized in their respective directories")
        return 0
    else:
        print("\n❌ Logo generation failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
