#!/usr/bin/env python3

import os
import subprocess
import sys

def install_cairosvg():
    """Install cairosvg if not available"""
    try:
        import cairosvg
        return True
    except ImportError:
        print("📦 Installing cairosvg...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "cairosvg"])
            import cairosvg
            return True
        except Exception as e:
            print(f"❌ Failed to install cairosvg: {e}")
            return False

def generate_logo_sizes():
    """Generate the most important logo sizes"""
    try:
        import cairosvg
    except ImportError:
        print("❌ cairosvg not available")
        return False

    # Create directories
    os.makedirs("generated", exist_ok=True)
    
    # Key sizes we need
    sizes = {
        "favicon-32x32.png": 32,
        "icon-192x192.png": 192,
        "icon-512x512.png": 512,
        "logo-1024x1024.png": 1024,
    }
    
    svg_file = "celer-ai-logo.svg"
    if not os.path.exists(svg_file):
        print(f"❌ {svg_file} not found")
        return False
    
    print(f"🎨 Generating logos from {svg_file}...")
    
    success_count = 0
    for filename, size in sizes.items():
        output_path = f"generated/{filename}"
        try:
            cairosvg.svg2png(
                url=svg_file,
                write_to=output_path,
                output_width=size,
                output_height=size
            )
            print(f"✅ Generated: {output_path} ({size}x{size})")
            success_count += 1
        except Exception as e:
            print(f"❌ Failed to generate {output_path}: {e}")
    
    print(f"\n📊 Generated {success_count}/{len(sizes)} files")
    return success_count > 0

def main():
    print("🎨 Celer AI Logo Generator")
    print("=========================")
    
    if not install_cairosvg():
        print("❌ Cannot proceed without cairosvg")
        return 1
    
    if generate_logo_sizes():
        print("✅ Logo generation complete! 🚀")
        print("📁 Check the 'generated' folder for your logo files")
        return 0
    else:
        print("❌ Logo generation failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
