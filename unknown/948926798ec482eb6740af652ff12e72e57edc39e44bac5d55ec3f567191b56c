# Celer AI Logo Assets

This directory contains the complete logo system for Celer AI, including the master SVG file and all necessary PNG variations.

## 🎨 Master Logo File

**`celer-ai-logo.svg`** - The master logo file that matches your navbar design:
- Teal to emerald gradient background (`#14b8a6` to `#059669`)
- White Lucide stethoscope icon (scale 13)
- Orange pinging dot in top-right corner (`#fb923c`, radius 20)
- Rounded corners (64px radius)
- Animated ping effect

## 📁 Directory Structure

```
logo/
├── celer-ai-logo.svg          # Master SVG file
├── favicons/                  # Favicon sizes (16x16, 32x32, 48x48, 64x64)
├── app-icons/                 # PWA/Mobile app icons (72x72 to 512x512)
├── apple-touch/               # Apple touch icons (57x57 to 180x180)
├── android/                   # Android Chrome icons (36x36 to 512x512)
├── windows/                   # Windows tile icons (70x70 to 310x310)
├── social/                    # Social media images (400x400, 800x800, 1200x630)
├── high-res/                  # High resolution (1024x1024 to 4096x4096)
└── web/                       # Web usage sizes (64x64 to 256x256)
```

## 🔧 Generating PNG Files

### Option 1: Using ImageMagick (Recommended)
```bash
# Install ImageMagick
brew install imagemagick  # macOS
# or
sudo apt-get install imagemagick  # Ubuntu/Debian

# Run the generator
python3 generate_all_sizes.py
```

### Option 2: Manual Generation with ImageMagick
```bash
# Favicons
convert celer-ai-logo.svg -resize 16x16 favicons/favicon-16x16.png
convert celer-ai-logo.svg -resize 32x32 favicons/favicon-32x32.png
convert celer-ai-logo.svg -resize 48x48 favicons/favicon-48x48.png

# App Icons
convert celer-ai-logo.svg -resize 192x192 app-icons/icon-192x192.png
convert celer-ai-logo.svg -resize 512x512 app-icons/icon-512x512.png

# Apple Touch Icons
convert celer-ai-logo.svg -resize 180x180 apple-touch/apple-touch-icon-180x180.png

# High Resolution
convert celer-ai-logo.svg -resize 1024x1024 high-res/logo-1024x1024.png
```

### Option 3: Using Inkscape
```bash
# Install Inkscape
brew install inkscape  # macOS

# Generate files
inkscape celer-ai-logo.svg --export-png=app-icons/icon-192x192.png --export-width=192 --export-height=192
```

### Option 4: Online Conversion
1. Go to https://convertio.co/svg-png/ or similar
2. Upload `celer-ai-logo.svg`
3. Set desired dimensions
4. Download and organize into appropriate folders

## 📱 Usage in Your App

### 1. Update Favicon
Copy to `doctor-recep-app/public/`:
```html
<link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicons/favicon-16x16.png">
```

### 2. Update PWA Manifest
Update `doctor-recep-app/public/manifest.json`:
```json
{
  "icons": [
    {
      "src": "/app-icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/app-icons/icon-512x512.png",
      "sizes": "512x512", 
      "type": "image/png",
      "purpose": "maskable any"
    }
  ]
}
```

### 3. Update Apple Touch Icons
```html
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch/apple-touch-icon-180x180.png">
<link rel="apple-touch-icon" sizes="152x152" href="/apple-touch/apple-touch-icon-152x152.png">
<link rel="apple-touch-icon" sizes="120x120" href="/apple-touch/apple-touch-icon-120x120.png">
```

### 4. Update Open Graph
```html
<meta property="og:image" content="/social/og-image-1200x630.png">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
```

## 🎯 Complete Size List

### Favicons
- 16x16, 32x32, 48x48, 64x64

### PWA/Mobile App Icons  
- 72x72, 96x96, 128x128, 144x144, 152x152, 192x192, 256x256, 384x384, 512x512

### Apple Touch Icons
- 57x57, 60x60, 72x72, 76x76, 114x114, 120x120, 144x144, 152x152, 167x167, 180x180

### Android Chrome Icons
- 36x36, 48x48, 72x72, 96x96, 144x144, 192x192, 256x256, 384x384, 512x512

### Windows Tiles
- 70x70, 144x144, 150x150, 310x150, 310x310

### Social Media
- 400x400 (square), 800x800 (square), 1200x630 (Open Graph)

### High Resolution
- 1024x1024, 2048x2048, 4096x4096

### Web Usage
- 64x64, 128x128, 256x256

## 🚀 Quick Start

1. Generate PNG files using one of the methods above
2. Copy generated files to `doctor-recep-app/public/` in appropriate subdirectories
3. Update `manifest.json` and HTML head tags
4. Test PWA installation and favicon display

## ✅ Verification Checklist

- [ ] Favicon appears in browser tab
- [ ] PWA install prompt shows correct icon
- [ ] Apple touch icon appears on iOS home screen
- [ ] Open Graph image displays in social shares
- [ ] All sizes are crisp and clear
- [ ] Animated ping effect works in SVG version
