#!/usr/bin/env python3

import os
import subprocess
import sys
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    try:
        # Try to import cairosvg for SVG to PNG conversion
        import cairosvg
        return True
    except ImportError:
        print("❌ cairosvg not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "cairosvg"])
            import cairosvg
            return True
        except Exception as e:
            print(f"❌ Failed to install cairosvg: {e}")
            return False

def create_directories():
    """Create necessary directories"""
    directories = ['favicons', 'app-icons', 'social', 'high-res', 'apple-touch']
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"📁 Created directory: {dir_name}")

def generate_png_from_svg(svg_path, output_path, width, height=None):
    """Generate PNG from SVG using cairosvg"""
    try:
        import cairosvg
        
        if height is None:
            height = width
            
        cairosvg.svg2png(
            url=svg_path,
            write_to=output_path,
            output_width=width,
            output_height=height
        )
        return True
    except Exception as e:
        print(f"❌ Failed to generate {output_path}: {e}")
        return False

def main():
    print("🎨 Celer AI Logo Generator (Python)")
    print("===================================")
    
    # Check if SVG source exists
    svg_path = "celer-ai-logo.svg"
    if not os.path.exists(svg_path):
        print(f"❌ SVG source file not found: {svg_path}")
        return 1
    
    print("✅ Found SVG source file")
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Cannot proceed without cairosvg")
        return 1
    
    # Create directories
    create_directories()
    
    # Define logo sizes
    logo_sizes = {
        # Favicon sizes
        'favicons/favicon-16x16.png': 16,
        'favicons/favicon-32x32.png': 32,
        'favicons/favicon-48x48.png': 48,
        
        # App icons (PWA/Mobile)
        'app-icons/icon-72x72.png': 72,
        'app-icons/icon-96x96.png': 96,
        'app-icons/icon-128x128.png': 128,
        'app-icons/icon-144x144.png': 144,
        'app-icons/icon-152x152.png': 152,
        'app-icons/icon-192x192.png': 192,
        'app-icons/icon-384x384.png': 384,
        'app-icons/icon-512x512.png': 512,
        
        # Social media
        'social/social-square-400x400.png': 400,
        
        # High resolution
        'high-res/logo-1024x1024.png': 1024,
        'high-res/logo-2048x2048.png': 2048,
        
        # Apple touch icons
        'apple-touch/apple-touch-icon-180x180.png': 180,
        'apple-touch/apple-touch-icon-167x167.png': 167,
        'apple-touch/apple-touch-icon-152x152.png': 152,
        'apple-touch/apple-touch-icon-120x120.png': 120,
    }
    
    print(f"\n🔄 Generating {len(logo_sizes)} PNG files...")
    
    success_count = 0
    for output_path, size in logo_sizes.items():
        if generate_png_from_svg(svg_path, output_path, size):
            print(f"✅ Generated: {output_path} ({size}x{size})")
            success_count += 1
        else:
            print(f"❌ Failed: {output_path}")
    
    # Generate Open Graph image (special aspect ratio)
    print("\n🔄 Generating Open Graph image...")
    if generate_png_from_svg(svg_path, 'social/og-image-1200x630.png', 1200, 630):
        print("✅ Generated: social/og-image-1200x630.png (1200x630)")
        success_count += 1
    
    print(f"\n📊 Generation Summary:")
    print(f"   Successfully generated: {success_count}/{len(logo_sizes) + 1} files")
    
    if success_count > 0:
        print("\n🎯 Next Steps:")
        print("   1. Copy the generated icons to your app's public directory")
        print("   2. Update your manifest.json with the new icon paths")
        print("   3. Update your HTML head with favicon links")
        print("   4. Replace any hardcoded logo references in your code")
        print("\n✅ Logo generation complete! 🚀")
    
    return 0 if success_count > 0 else 1

if __name__ == "__main__":
    sys.exit(main())
