.
├── audio.md
├── backend.log
├── doctor-recep-app
│   ├── context.md
│   ├── database
│   │   ├── create-functions.sql
│   │   ├── fix-discount-calculation.sql
│   │   ├── fix-referral-functions.sql
│   │   ├── fix-rls-policies.sql
│   │   ├── fix-rls-uuid-error.sql
│   │   ├── fixed-functions.sql
│   │   ├── migrations
│   │   │   ├── 001_add_quota_and_admin_system.sql
│   │   │   ├── 002_add_referral_system.sql
│   │   │   ├── 003_add_billing_system.sql
│   │   │   ├── 004_add_consultation_types_and_notes.sql
│   │   │   └── 005_expand_consultation_types.sql
│   │   ├── schema.sql
│   │   └── update-patient-numbering.sql
│   ├── deploy-frontend.sh
│   ├── DEPLOYMENT.md
│   ├── eslint.config.mjs
│   ├── IMPLEMENTATION_SUMMARY.md
│   ├── next-env.d.ts
│   ├── next.config.js
│   ├── package-lock.json
│   ├── package.json
│   ├── PORTS_CONFIG.md
│   ├── postcss.config.mjs
│   ├── public
│   │   ├── celer-ai-logo.svg
│   │   ├── favicon.ico
│   │   ├── file.svg
│   │   ├── globe.svg
│   │   ├── icons
│   │   │   ├── apple-touch-icon-120x120.png
│   │   │   ├── apple-touch-icon-152x152.png
│   │   │   ├── apple-touch-icon-180x180.png
│   │   │   ├── celer-ai-logo.svg
│   │   │   ├── favicon-16x16.png
│   │   │   ├── favicon-32x32.png
│   │   │   ├── favicon-48x48.png
│   │   │   ├── icon-128x128.png
│   │   │   ├── icon-144x144.png
│   │   │   ├── icon-152x152.png
│   │   │   ├── icon-192x192.png
│   │   │   ├── icon-384x384.png
│   │   │   ├── icon-512x512.png
│   │   │   ├── icon-72x72.png
│   │   │   ├── icon-96x96.png
│   │   │   └── logo-1024x1024.png
│   │   ├── manifest.json
│   │   ├── next.svg
│   │   ├── vercel.svg
│   │   └── window.svg
│   ├── python-backend
│   │   ├── cloudbuild.yaml
│   │   ├── deploy.sh
│   │   ├── Dockerfile
│   │   ├── main.py
│   │   ├── prompts.json
│   │   ├── README.md
│   │   ├── requirements.txt
│   │   └── start.sh
│   ├── QUOTA_ADMIN_SETUP.md
│   ├── r2-cors-config.json
│   ├── r2-cors-final.json
│   ├── r2-cors-simple.json
│   ├── README.md
│   ├── scripts
│   │   ├── analyze-database-performance.js
│   │   ├── check-and-fix-doctors.js
│   │   ├── check-complete-payment.js
│   │   ├── check-db-functions.js
│   │   ├── check-discount-statuses.js
│   │   ├── check-discount-sync.js
│   │   ├── check-existing-files.js
│   │   ├── check-url-status.js
│   │   ├── create-admin.js
│   │   ├── create-database-functions.js
│   │   ├── create-referral-functions.js
│   │   ├── create-test-admin.js
│   │   ├── create-test-doctor.js
│   │   ├── debug-billing-stats.js
│   │   ├── discount-fix-sql.sql
│   │   ├── execute-discount-fix.js
│   │   ├── fix-db-functions-properly.sql
│   │   ├── fix-discount-issues.js
│   │   ├── fix-discount-sql.js
│   │   ├── inspect-supabase-config.js
│   │   ├── migrate-supabase-to-r2.js
│   │   ├── reset-admin-password.js
│   │   ├── test-current-behavior.js
│   │   ├── test-frontend-calculations.js
│   │   ├── test-functions.js
│   │   ├── test-r2-connection.js
│   │   ├── test-referral-functions.js
│   │   ├── update-discount-calculation.js
│   │   ├── verify-migration-setup.js
│   │   ├── verify-multitenant.js
│   │   └── verify-r2-migration.js
│   ├── src
│   │   ├── app
│   │   │   ├── admin
│   │   │   │   ├── dashboard
│   │   │   │   │   └── page.tsx
│   │   │   │   └── login
│   │   │   │       └── page.tsx
│   │   │   ├── api
│   │   │   │   ├── contact-founder
│   │   │   │   │   └── route.ts
│   │   │   │   └── generate-summary-stream
│   │   │   │       └── route.ts
│   │   │   ├── dashboard
│   │   │   │   └── page.tsx
│   │   │   ├── globals.css
│   │   │   ├── info
│   │   │   │   └── page.tsx
│   │   │   ├── layout.tsx
│   │   │   ├── login
│   │   │   │   └── page.tsx
│   │   │   ├── mobile
│   │   │   │   └── page.tsx
│   │   │   ├── page.tsx
│   │   │   ├── privacy
│   │   │   │   └── page.tsx
│   │   │   ├── settings
│   │   │   │   └── page.tsx
│   │   │   ├── signup
│   │   │   │   └── page.tsx
│   │   │   ├── templates
│   │   │   │   └── page.tsx
│   │   │   └── terms
│   │   │       └── page.tsx
│   │   ├── components
│   │   │   ├── admin
│   │   │   │   ├── admin-auth-wrapper.tsx
│   │   │   │   ├── admin-dashboard-header.tsx
│   │   │   │   ├── admin-dashboard-stats.tsx
│   │   │   │   ├── admin-login-form.tsx
│   │   │   │   ├── billing-management.tsx
│   │   │   │   └── doctors-table.tsx
│   │   │   ├── analytics
│   │   │   │   ├── consultation-modal.tsx
│   │   │   │   ├── consultations-list.tsx
│   │   │   │   ├── dashboard-stats.tsx
│   │   │   │   ├── quota-card.tsx
│   │   │   │   ├── quota-warning-modal.tsx
│   │   │   │   ├── referral-card.tsx
│   │   │   │   └── referral-stats.tsx
│   │   │   ├── auth
│   │   │   │   ├── login-form.tsx
│   │   │   │   └── signup-form.tsx
│   │   │   ├── mobile
│   │   │   │   ├── audio-recorder.tsx
│   │   │   │   ├── image-capture.tsx
│   │   │   │   ├── recording-interface.tsx
│   │   │   │   └── submission-form.tsx
│   │   │   ├── pwa
│   │   │   │   └── pwa-install-prompt.tsx
│   │   │   ├── recording
│   │   │   │   ├── consultations-sidebar.tsx
│   │   │   │   ├── content-editable-editor.tsx
│   │   │   │   ├── new-navbar.tsx
│   │   │   │   ├── new-recording-interface.tsx
│   │   │   │   ├── recording-main-area.tsx
│   │   │   │   ├── rich-text-editor.tsx
│   │   │   │   └── streamlined-recording-area.tsx
│   │   │   ├── settings
│   │   │   │   ├── password-change-modal.tsx
│   │   │   │   └── settings-form.tsx
│   │   │   ├── shared
│   │   │   │   ├── dashboard-client.tsx
│   │   │   │   ├── dashboard-header.tsx
│   │   │   │   └── referred-welcome-modal.tsx
│   │   │   ├── templates
│   │   │   │   ├── coming-soon-overlay.tsx
│   │   │   │   ├── template-editor.tsx
│   │   │   │   ├── templates-interface.tsx
│   │   │   │   └── templates-sidebar.tsx
│   │   │   └── ui
│   │   │       ├── badge.tsx
│   │   │       ├── button.tsx
│   │   │       ├── card.tsx
│   │   │       ├── confirmation-dialog.tsx
│   │   │       ├── input.tsx
│   │   │       ├── loading-wrapper.tsx
│   │   │       ├── payment-details-modal.tsx
│   │   │       ├── plan-selection-modal.tsx
│   │   │       └── separator.tsx
│   │   └── lib
│   │       ├── actions
│   │       │   ├── admin-auth.ts
│   │       │   ├── admin.ts
│   │       │   ├── auth.ts
│   │       │   ├── billing.ts
│   │       │   ├── consultations.ts
│   │       │   ├── contact-requests.ts
│   │       │   ├── referrals.ts
│   │       │   └── settings.ts
│   │       ├── auth
│   │       │   ├── admin-dal.ts
│   │       │   ├── admin-session.ts
│   │       │   ├── dal.ts
│   │       │   └── session.ts
│   │       ├── storage.ts
│   │       ├── supabase
│   │       │   ├── client.ts
│   │       │   └── server.ts
│   │       ├── types.ts
│   │       ├── utils.ts
│   │       └── validations.ts
│   ├── start-all.sh
│   ├── start-frontend.sh
│   ├── tests
│   │   ├── ai-prompt-test.js
│   │   ├── auth-test.js
│   │   ├── browser-login-test.js
│   │   ├── complete-e2e-test.js
│   │   ├── complete-test-results.json
│   │   ├── database-test.js
│   │   ├── e2e-example-files-test.js
│   │   ├── e2e-test.js
│   │   ├── final-verification.js
│   │   ├── gemini-files-api.test.py
│   │   ├── quota-system-test.js
│   │   ├── run-all-tests.js
│   │   ├── session-test.js
│   │   ├── test-results.json
│   │   └── user-check.js
│   ├── tsconfig.json
│   ├── tsconfig.tsbuildinfo
│   └── vercel.json
├── exampl.py
├── example
│   ├── 1666774601-x-ray-left-thigh-ap-lat.webp
│   ├── IM-0001-0001.jpeg
│   └── recording_1748372480414.webm
├── example_mainpy.txt
├── frontend.log
├── gemini_docs.md
├── logo
│   ├── android
│   ├── app-icons
│   ├── apple-touch
│   ├── apple-touch-icon-180x180.png
│   ├── celer-ai-logo-final.svg
│   ├── celer-ai-logo.svg
│   ├── favicon-16x16.png
│   ├── favicon-32x32.png
│   ├── favicon-48x48.png
│   ├── favicons
│   ├── generate_all_sizes.py
│   ├── generate_logos.py
│   ├── generate-logos.js
│   ├── generate-with-sharp.js
│   ├── high-res
│   ├── icon-128x128.png
│   ├── icon-144x144.png
│   ├── icon-152x152.png
│   ├── icon-192x192.png
│   ├── icon-384x384.png
│   ├── icon-512x512.png
│   ├── icon-72x72.png
│   ├── icon-96x96.png
│   ├── logo-1024x1024.png
│   ├── QUICK_SETUP.md
│   ├── README.md
│   ├── simple_generate.py
│   ├── social
│   ├── stethoscope.svg
│   ├── svg-to-png-converter.html
│   ├── web
│   └── windows
├── NewFeature.md
├── package-lock.json
├── package.json
├── past_interactions.md
├── product_description.md
├── production_blueprint.md
├── project_tree.txt
├── seperate.md
├── task-level.md
└── working.txt

51 directories, 234 files
