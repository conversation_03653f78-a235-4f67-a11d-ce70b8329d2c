Of course. The external configuration approach is an excellent pattern for making your application more flexible and easier to maintain. It separates your prompt "logic" from your application "code."

Let's walk through how to do this step-by-step.

### The Big Picture: How It Works

1.  **Create a Configuration File:** We will move the entire `CONSULTATION_TEMPLATES` dictionary into a separate file named `prompts.json`.
2.  **Load at Startup:** When the FastAPI application starts, it will read this `prompts.json` file once and load the contents into memory.
3.  **Validate the Config:** To prevent errors from a badly formatted JSON file, we will use Pydantic to validate that the loaded configuration has the correct structure and fields.
4.  **Use the Loaded Config:** The `generate_prompt` function will no longer use the hardcoded dictionary. Instead, it will refer to the configuration that was loaded at startup.

This means you can change your prompts by just editing the `prompts.json` file and restarting the application, without ever touching the `main.py` code again.

---

### Step 1: Create the `prompts.json` File

First, create a new file named `prompts.json` in the same directory as your `main.py`. Copy the `CONSULTATION_TEMPLATES` dictionary from your Python code and paste it into this file, converting it to valid JSON format.

It will look like this:

**`prompts.json`:**```json
{
  "outpatient": {
    "format": "structured EMR",
    "style": "consultation summary",
    "ai_instruction": "You are a medical assistant AI. Convert the following consultation notes into a structured EMR in the format below. Your response must adhere to Clinical Documentation Integrity (CDI) principles, ensuring it is complete, accurate, and consistent. Assign the appropriate ICD-10 code to the diagnosis.",
    "template_structure": "\nConsultation Summary:\n  Patient Details:\n    - Name: [Full Name or Initials if unknown]\n    - Age: [in years]\n    - Gender: [Male / Female / Other]\n    - Date of Consultation: [DD-MM-YYYY]\n    - Time: [HH:MM AM/PM]\n\n  Chief Complaints:\n    - [Symptom 1: duration]\n    - [Symptom 2: duration]\n    - ...\n\n  History of Present Illness:\n    - [Detailed narrative of symptom onset, progression, any self-medication, relevant context]\n\n  Past Medical History:\n    - [e.g., Diabetes (5 yrs), Hypertension (3 yrs), No known allergies]\n    - [Include surgical history or prior major illnesses]\n\n  Examination Findings:\n    - Vitals: BP [__], Pulse [__], Temp [__], SPO2 [__]\n    - General Examination: [e.g., Patient alert, oriented, mild pallor]\n    - Systemic Exam: \n        - Respiratory: [e.g., Clear breath sounds]\n        - Cardiovascular: [e.g., Normal heart sounds]\n        - Abdomen: [e.g., Soft, non-tender]\n        - Neuro: [e.g., Normal reflexes]\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - [Primary diagnosis with its ICD-10 Code, e.g., Acute viral pharyngitis (J02.9)]\n    - [Differential if applicable]\n\n  Investigations Ordered:\n    - [Test 1]\n    - [Test 2]\n    - [Mention old reports if referred]\n\n  Prescription:\n    - [Drug Name] – [Dose] – [Frequency] – [Duration]\n    - [Any supplements / injections / inhalers]\n    - [Advice: dietary, lifestyle, red flags]\n\n  Follow-Up Plan:\n    - [e.g., Review after 3 days / When reports ready / Emergency if symptoms worsen]\n\n  Notes:\n    - [Optional remarks: referral to specialist, limitations of diagnosis, patient refusal, consent taken, etc.]\n\n  Doctor ID:\n    - [Dr. Name / ID / Signature token]\n",
    "instructions": "\nCreate a structured EMR consultation summary, ensuring it meets Clinical Documentation Integrity (CDI) standards.\n- Focus on current symptoms and immediate treatment\n- Keep prescriptions clear and dosage-specific\n- Assign an ICD-10 code to the provisional diagnosis\n- Include follow-up instructions\n- Maintain professional medical terminology\n",
    "sections": ["patient_details", "chief_complaints", "history_present_illness", "past_medical_history", "examination_findings", "provisional_diagnosis", "investigations_ordered", "prescription", "follow_up_plan", "notes", "doctor_id"]
  },
  "discharge": {
    "format": "discharge summary",
    "style": "structured hospital discharge",
    "ai_instruction": "You are a medical assistant AI trained to generate discharge summaries for Indian hospitals. Convert the following clinical data into a structured discharge summary. Your response must adhere to Clinical Documentation Integrity (CDI) principles. Assign the appropriate ICD-10 code to the final diagnosis. Use clear, clinical, grammatically correct English and write in long-form where appropriate. Assume the reader is another doctor.",
    "template_structure": "\nDISCHARGE SUMMARY\n\nPatient Details\nName: [Full Name]\nAge / Sex: [## / M/F/O]\nHospital / IP No.: [########]\nAdmission Date: [DD-MM-YYYY]\nDischarge Date: [DD-MM-YYYY]\nConsultant / Department: [Dr. XYZ / General Medicine]\n\nPresenting Complaints\n[Primary complaints with duration — e.g., Fever and cough for 3 days]\n\nHistory of Present Illness\n[Chronological narrative of symptoms, progression, prior treatment]\n\nPast Medical / Surgical History\n[DM/HTN/CKD/CAD or previous surgeries if any]\n\nAllergies\n[Drug / Food / Environmental — if known]\n\nPersonal / Family History\n[Smoking / Alcohol / Genetic conditions — optional]\n\nExamination Findings at Admission\n[Vitals, systemic findings, any abnormal signs]\n\nInvestigations\n[List relevant lab results, imaging with interpretation]\nEg: CBC – WNL, Chest X-ray – consolidation in right lower lobe\n\nFinal Diagnosis (with ICD-10 Code):\n[Eg: Community-acquired pneumonia (J18.9) with Type 2 Diabetes Mellitus (E11.9)]\n\nHospital Course / Treatment Given\nSummary of key events day-wise or phase-wise (ICU, ward)\n[Antibiotics started, response noted, physiotherapy given, etc.]\n\nSurgery Performed (if any)\nName, date, brief operative note (refer to Op Summary)\n\nCondition at Discharge\n[Vitals stable / afebrile / ambulatory / tolerating orally]\n\nMedications on Discharge\nName | Dose | Frequency | Duration\nTab Amoxicillin 500 mg – 1-0-1 for 5 days\nTab Paracetamol 650 mg – SOS\n\nAdvice on Discharge\nDiet: [Normal / Diabetic / Low-salt]\nActivity: [Bed rest / Gradual mobilization]\nFollow-up: [With Dr. XYZ on DD-MM-YYYY or if symptoms worsen]\nRed flags: [Fever, bleeding, breathlessness]\n\nPrognosis / Outcome\n[Recovered / Improving / Palliative / Referred / Expired]\n\nDoctor's Name & Signature\n[Dr. Full Name]\n[Designation & Registration No.]\n",
    "instructions": "\nConvert notes into a comprehensive, paragraph-based discharge summary that meets Clinical Documentation Integrity (CDI) standards.\n- Organize information into clear sections\n- Expand abbreviated notes into full sentences\n- Assign an ICD-10 code to the final diagnosis\n- Ensure continuity of care instructions are detailed\n- Include all medications with complete dosing schedules\n- Highlight any follow-up requirements or warning signs\n",
    "sections": ["patient_details", "presenting_complaints", "history_present_illness", "past_medical_surgical_history", "allergies", "personal_family_history", "examination_findings_admission", "investigations", "provisional_final_diagnosis", "hospital_course_treatment", "surgery_performed", "condition_discharge", "medications_discharge", "advice_discharge", "prognosis_outcome", "doctor_signature"]
  },
  "surgery": {
    "format": "operative summary",
    "style": "structured operative note",
    "ai_instruction": "You are a medical assistant AI. Convert the following operative notes into a structured operative summary. The report must adhere to Clinical Documentation Integrity (CDI) principles. For the 'Operative Procedure' and 'Intraoperative Findings' sections, use the detailed, granular, and structured descriptive style characteristic of SNOMED CT. For the final 'Post-operative Diagnosis', assign the appropriate ICD-10 code.",
    "template_structure": "\nOperative Note:\n\nPatient Details:\nName: [Full Name or Initials]\nAge / Sex: [Years, M/F/O]\nHospital Number / IP: [XXXXXXX]\n\nDate and Time of Surgery:\n[DD-MM-YYYY, HH:MM – HH:MM]\n\nIndications for Surgery:\n[Brief history leading to surgery]\n\nPre-operative Diagnosis:\n[Diagnosis before the surgery]\n\nPost-operative Diagnosis (with ICD-10 Code):\n[Final diagnosis after the surgery, with its ICD-10 code]\n\nConsent:\n[Written informed consent obtained, including risks, benefits, alternatives]\n\nType of Anesthesia:\n[Type and any complications during induction]\n\nPositioning and Preparation:\n[Position of patient, area prepped, draping]\n\nOperative Procedure:\n(Describe with SNOMED CT-style granularity)\nIncision: Site, type\nExploration: Findings (e.g., inflamed appendix, adhesions)\nSteps Taken: Step-by-step procedure with instruments, techniques, safety checks\nHemostasis: Achieved using ___\nIrrigation / Suction: If applicable\nClosure: Layer-wise details (fascia, subcutaneous, skin), suture material\nDrain Placement: If any\n\nIntraoperative Findings:\n(Describe with SNOMED CT-style granularity)\n[Detailed list of anatomical/pathological findings]\n\nIntraoperative Complications:\n[If any: bleeding, adhesion, anatomical variation]\n\nPost-op Plan:\n[Monitoring, antibiotics, nutrition, mobilization, labs]\n\nCondition at End of Procedure:\n[Stable / vitals normal / shifted to ICU]\n\nSpecimen Sent for HPE:\n[Yes / No; details]\n\nSignatures:\n[Operating Surgeon]\n[Assistant Surgeon]\n[Anesthetist]\n",
    "instructions": "\nTransform surgical notes into a detailed operative report that meets Clinical Documentation Integrity (CDI) standards.\n- Convert procedure notes into a narrative, using SNOMED CT principles for high-detail descriptions of the procedure and findings.\n- Include pre-operative, intra-operative, and post-operative details.\n- Assign an ICD-10 code to the final post-operative diagnosis.\n- Detail any complications or significant events.\n- Include post-operative care instructions.\n",
    "sections": ["patient_details", "date_time_surgery", "indications_surgery", "preoperative_diagnosis", "postoperative_diagnosis", "consent", "anesthesia_type", "positioning_preparation", "operative_procedure", "intraoperative_findings", "intraoperative_complications", "postop_plan", "condition_end_procedure", "specimen_hpe", "signatures"]
  }
}
```
*Notice that `\n` newlines are preserved inside the JSON strings.*

---

### Step 2: Modify the `main.py` File

Now, we'll make the necessary changes to the Python code. I have marked all new and modified sections.

**Summary of Changes:**

1.  **Import `Request`:** Needed to access app state in endpoint handlers.
2.  **Add Pydantic Models:** New models (`PromptTemplate`, `PromptConfig`) will validate the structure of `prompts.json`.
3.  **Remove Hardcoded Dictionary:** The `CONSULTATION_TEMPLATES` variable is deleted.
4.  **Modify `lifespan` Manager:** The startup logic will now load and validate the `prompts.json` file and store it in `app.state`.
5.  **Modify Endpoint Handlers:** The `generate_summary` and `generate_summary_stream` endpoints will now pass the loaded configuration to the `generate_prompt` function.
6.  **Modify `generate_prompt`:** This function will now accept the prompt configuration as an argument instead of using a global dictionary.

Here is the complete, modified `main.py`:

```python
"""
Celer AI System - Python Backend
Using FastAPI and Google Gemini 2.5 Flash Preview with Base64 Inline Data
"""

import os
import logging
import mimetypes
import asyncio
from typing import List, Optional, Tuple, Any, Dict # Added Dict for prompt config type
# ### ADDED: Import Request to access app state in handlers
from fastapi import FastAPI, HTTPException, Request 
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
# ### ADDED: Import ValidationError for robust error handling
from pydantic import BaseModel, ValidationError
from dotenv import load_dotenv
import httpx
import tempfile
from datetime import datetime
from urllib.parse import urlparse
import io
from PIL import Image
from google import genai
from google.genai import types 
import json 
from contextlib import asynccontextmanager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# ### ADDED: Pydantic models for validating the prompts.json configuration
class PromptTemplate(BaseModel):
    format: str
    style: str
    ai_instruction: str
    template_structure: str
    instructions: str
    sections: List[str]

class PromptConfig(BaseModel):
    outpatient: PromptTemplate
    discharge: PromptTemplate
    surgery: PromptTemplate

# ### ADDED: Function to load and validate the configuration file
def load_prompt_config(path: str) -> Optional[PromptConfig]:
    """Loads and validates the prompt configuration from a JSON file."""
    try:
        with open(path, 'r') as f:
            data = json.load(f)
        config = PromptConfig.model_validate(data)
        logger.info(f"✅ Successfully loaded and validated prompt configuration from {path}")
        return config
    except FileNotFoundError:
        logger.error(f"❌ CONFIGURATION ERROR: The prompt file '{path}' was not found.")
        return None
    except ValidationError as e:
        logger.error(f"❌ CONFIGURATION ERROR: The prompt file '{path}' has invalid data. Details: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ CONFIGURATION ERROR: An unexpected error occurred while loading '{path}'. Details: {e}")
        return None

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 Application starting up...")
    # ### MODIFIED: Load prompt configuration on startup
    app.state.prompt_config = load_prompt_config("prompts.json")
    if not app.state.prompt_config:
        # This will prevent the app from running in a broken state
        raise RuntimeError("Failed to load prompt configuration. Application cannot start.")
    
    yield
    # Shutdown (if needed)
    pass

# Initialize FastAPI app
app = FastAPI(
    title="Celer AI API",
    description="AI-powered consultation summary system using Gemini 2.5 Flash Preview with Base64 Inline Data",
    version="2.0.0",
    lifespan=lifespan # ### MODIFIED: Use the updated lifespan manager
)

# Configure CORS - keep it simple and working
app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:3004")],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Context manager for Gemini client
class GeminiClientManager:
    """Context manager for Gemini client"""
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None

    async def __aenter__(self):
        try:
            self.client = genai.Client(api_key=self.api_key)
            logger.info("✅ Gemini client initialized successfully")
            return self.client
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise

    async def __aexit__(self, exc_type, exc_value, traceback):
        if self.client:
            logger.info("✅ Gemini client manager exiting.")


# Initialize Gemini client manager
gemini_manager = GeminiClientManager(api_key=os.getenv("GEMINI_API_KEY"))

# Pydantic models
class TemplateConfig(BaseModel):
    prescription_format: str = "standard"
    language: str = "english"
    tone: str = "professional"
    sections: List[str] = ["symptoms", "diagnosis", "prescription", "advice", "follow_up"]

# ### REMOVED: The hardcoded CONSULTATION_TEMPLATES dictionary is no longer needed.
# CONSULTATION_TEMPLATES = { ... }

class GenerateSummaryRequest(BaseModel):
    primary_audio_url: str
    additional_audio_urls: Optional[List[str]] = []
    image_urls: Optional[List[str]] = []
    template_config: Optional[TemplateConfig] = TemplateConfig()
    submitted_by: str = "doctor"
    consultation_type: str = "outpatient"
    doctor_notes: Optional[str] = None
    additional_notes: Optional[str] = None
    patient_name: Optional[str] = None
    created_at: Optional[str] = None

class GenerateSummaryResponse(BaseModel):
    summary: str
    model: str
    timestamp: str
    files_processed: dict

class GenerateSummaryStreamRequest(BaseModel): 
    primary_audio_url: str
    additional_audio_urls: Optional[List[str]] = []
    image_urls: Optional[List[str]] = []
    template_config: Optional[TemplateConfig] = TemplateConfig()
    submitted_by: str = "doctor"
    consultation_type: str = "outpatient"
    doctor_notes: Optional[str] = None
    additional_notes: Optional[str] = None
    patient_name: Optional[str] = None
    created_at: Optional[str] = None

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    client_connected = False
    try:
        temp_client = genai.Client(api_key=gemini_manager.api_key)
        if temp_client:
            client_connected = True
    except Exception:
        client_connected = False

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "gemini_client": "connected" if client_connected else "disconnected (initialization check)",
        "model": "gemini-2.5-flash-preview-05-20"
    }

# ### MODIFIED: generate_prompt now accepts the loaded config as an argument
def generate_prompt(
    prompt_templates: Dict[str, PromptTemplate], 
    template_config: TemplateConfig, 
    submitted_by: str, 
    consultation_type: str = "outpatient", 
    doctor_notes: Optional[str] = None, 
    additional_notes: Optional[str] = None, 
    patient_name: Optional[str] = None, 
    created_at: Optional[str] = None
) -> str:
    """Generate AI prompt based on template configuration and consultation type"""
    context_note = (
        "This consultation was recorded by the doctor during patient visit."
        if submitted_by == "doctor"
        else "This consultation is being reviewed by the receptionist for final summary."
    )

    # ### MODIFIED: Get template from the passed-in dictionary
    consultation_template_model = prompt_templates.get(consultation_type, prompt_templates["outpatient"])
    # Convert Pydantic model back to dict for attribute access, or access attributes directly
    consultation_template = consultation_template_model.model_dump()
    
    if template_config.sections and len(template_config.sections) > 0:
        sections_text = ", ".join(template_config.sections)
    else:
        sections_text = ", ".join(consultation_template["sections"])
    
    text_sources = []
    if doctor_notes:
        text_sources.append(f"Doctor's Notes: {doctor_notes}")
    if additional_notes:
        text_sources.append(f"Additional Notes: {additional_notes}")
    
    text_context = ""
    if text_sources:
        text_context = f"""

**TEXT SOURCES PROVIDED:**
{chr(10).join(text_sources)}

IMPORTANT: Give equal priority to the text sources above and the audio recordings. Both contain valuable medical information that should be integrated into the final summary.
"""

    ai_instruction = consultation_template["ai_instruction"]
    format_structure = consultation_template["template_structure"]

    patient_context = f"\nPatient Name: {patient_name}" if patient_name else ""

    date_context = ""
    if created_at:
        try:
            from datetime import datetime
            parsed_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            formatted_date = parsed_date.strftime("%B %d, %Y at %I:%M %p")
            date_context = f"\nConsultation Date: {formatted_date}"
        except:
            date_context = f"\nConsultation Date: {created_at}"

    return f"""
{ai_instruction}

Context: {context_note}{patient_context}{date_context}
Consultation Type: {consultation_type.upper()}
Format Style: {consultation_template["style"]}

{consultation_template["instructions"]}

**OUTPUT FORMAT:**
{format_structure}

CRITICAL ACCURACY REQUIREMENTS:
- Only include information explicitly mentioned in audio recordings or provided text notes
- **HIGHLIGHT ANY AMBIGUITIES**: Use **bold text** or mention uncertainty for any unclear medical terms, dosages, or instructions
- Preserve exact medication names and dosages as stated
- Flag any incomplete or unclear information with clear indicators
- Do not add assumptions, differential diagnoses, or recommendations not explicitly stated{text_context}

Requirements:
- Language: {template_config.language}
- Tone: {template_config.tone}
- Format: {consultation_template["format"]}
- Include sections: {sections_text}

Processing Instructions:
1. **PRIMARY SOURCES**: Process audio recording(s) and any provided text notes with equal priority
2. Extract key medical information from all sources (audio + text)
3. **SECONDARY**: If images are provided, analyze them for visual findings (handwritten notes, prescriptions, medical images)
4. Integrate information from ALL sources (audio + text + images) for complete context
5. Keep summary factual and based only on provided information
6. Use appropriate medical terminology for Indian healthcare context
7. **ACCURACY CHECK**: Highlight any unclear medication names, dosages, or instructions with **bold text**
8. If any information seems incomplete or ambiguous, clearly indicate this in the summary
9. For {consultation_type} consultations: {consultation_template["instructions"]}

Please provide a comprehensive {consultation_template["format"]} based on all provided sources (audio, text, and images). Ensure medical accuracy and highlight any uncertainties clearly.
    """.strip()

# ... (All utility functions like get_file_extension_from_url, download_file_from_url, etc. remain unchanged) ...
def get_file_extension_from_url(url: str) -> str:
    """Extract file extension from URL"""
    parsed_url = urlparse(url)
    path = parsed_url.path
    if '.' in path:
        return path.split('.')[-1].lower()
    return ""

def get_mime_type_from_content_type(content_type: str) -> str:
    """Extract MIME type from content-type header"""
    if content_type:
        return content_type.split(';')[0].strip()
    return ""

async def detect_mime_type(url: str, content_type: str) -> str:
    """Detect MIME type from URL and content-type header"""
    if content_type:
        mime_type = get_mime_type_from_content_type(content_type)
        if mime_type:
            return mime_type
    
    url_extension = get_file_extension_from_url(url)
    if url_extension:
        guessed_type_tuple = await asyncio.to_thread(mimetypes.guess_type, f"file.{url_extension}")
        guessed_type = guessed_type_tuple[0] if guessed_type_tuple else None
        if guessed_type:
            return guessed_type
        
        extension_map = {
            'mp3': 'audio/mpeg', 'wav': 'audio/wav', 'm4a': 'audio/mp4',
            'webm': 'audio/webm', 'ogg': 'audio/ogg', 'aac': 'audio/aac',
            'flac': 'audio/flac', 'jpg': 'image/jpeg', 'jpeg': 'image/jpeg',
            'png': 'image/png', 'webp': 'image/webp', 'heic': 'image/heic'
        }
        return extension_map.get(url_extension, 'application/octet-stream')
    
    return 'application/octet-stream'

async def download_file_from_url(url: str) -> tuple[bytes, str]:
    """Download file from URL and return bytes with detected MIME type"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client_http:
            response = await client_http.get(url)
            response.raise_for_status()
            content_type = response.headers.get("content-type", "")
            mime_type = await detect_mime_type(url, content_type)
            logger.info(f"📥 Downloaded file: {url} (MIME: {mime_type}, Size: {len(response.content)} bytes)")
            return response.content, mime_type
    except Exception as e:
        logger.error(f"Failed to download file from {url}: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to download file from {url}: {str(e)}")

async def convert_audio_to_wav(file_bytes: bytes) -> tuple[bytes, str]:
    """Convert audio to WAV format with 16kHz mono"""
    def _blocking_ffmpeg_operations():
        tmp_in_name, tmp_out_name = None, None
        try:
            with tempfile.NamedTemporaryFile(suffix='.tmp', delete=False) as tmp_in:
                tmp_in_name = tmp_in.name
                tmp_in.write(file_bytes)
                tmp_in.flush()

            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_out:
                tmp_out_name = tmp_out.name

            import subprocess
            import shutil

            ffmpeg_path = shutil.which('ffmpeg')
            if not ffmpeg_path:
                for path in ['/opt/homebrew/bin/ffmpeg', '/usr/local/bin/ffmpeg', '/usr/bin/ffmpeg']:
                    if os.path.exists(path):
                        ffmpeg_path = path
                        break

            if not ffmpeg_path:
                raise RuntimeError("FFmpeg executable not found")

            cmd = [
                ffmpeg_path, '-i', tmp_in_name,
                '-acodec', 'pcm_s16le', '-ac', '1', '-ar', '16000',
                '-y', tmp_out_name
            ]

            process = subprocess.run(cmd, capture_output=True, text=True)
            if process.returncode != 0:
                raise RuntimeError(f"FFmpeg failed: {process.stderr}")

            with open(tmp_out_name, 'rb') as f:
                wav_bytes = f.read()
            return wav_bytes, 'audio/wav'
        finally:
            if tmp_in_name:
                try: os.unlink(tmp_in_name)
                except OSError: pass
            if tmp_out_name:
                try: os.unlink(tmp_out_name)
                except OSError: pass
                    
    return await asyncio.to_thread(_blocking_ffmpeg_operations)

async def convert_image_to_png(file_bytes: bytes, max_size: int = 1024) -> tuple[bytes, str]:
    """Convert image to PNG format and resize if needed"""
    def _blocking_pil_operations():
        img = Image.open(io.BytesIO(file_bytes))
        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')
        width, height = img.size
        if width > max_size or height > max_size:
            ratio = min(max_size/width, max_size/height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        output = io.BytesIO()
        img.save(output, format='PNG', optimize=True)
        png_bytes = output.getvalue()
        return png_bytes, 'image/png'
        
    return await asyncio.to_thread(_blocking_pil_operations)

async def process_single_audio_file(audio_url: str, audio_file_identifier: str) -> Tuple[Optional[types.Part], str, str, Optional[str]]:
    logger.info(f"📤 Processing {audio_file_identifier} audio file: {audio_url}")
    try:
        file_bytes, _ = await download_file_from_url(audio_url)
        wav_bytes, wav_mime_type = await convert_audio_to_wav(file_bytes)
        audio_part = types.Part.from_bytes(data=wav_bytes, mime_type=wav_mime_type)
        logger.info(f"✅ {audio_file_identifier.replace('_', ' ').capitalize()} audio processed successfully (MIME: {wav_mime_type}, Size: {len(wav_bytes)} bytes)")
        return audio_part, audio_file_identifier, audio_url, None
    except Exception as e:
        error_msg = f"Failed to process {audio_file_identifier} audio {audio_url}: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return None, audio_file_identifier, audio_url, error_msg

async def process_single_image_file(image_url: str, image_file_identifier: str) -> Tuple[Optional[types.Part], str, str, Optional[str]]:
    logger.info(f"📤 Processing {image_file_identifier} image file: {image_url}")
    try:
        file_bytes, _ = await download_file_from_url(image_url)
        png_bytes, png_mime_type = await convert_image_to_png(file_bytes)
        image_part = types.Part.from_bytes(data=png_bytes, mime_type=png_mime_type)
        logger.info(f"✅ {image_file_identifier.replace('_', ' ').capitalize()} image processed successfully (MIME: {png_mime_type}, Size: {len(png_bytes)} bytes)")
        return image_part, image_file_identifier, image_url, None
    except Exception as e:
        error_msg = f"Failed to process {image_file_identifier} image {image_url}: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return None, image_file_identifier, image_url, error_msg

@app.post("/api/generate-summary", response_model=GenerateSummaryResponse)
# ### MODIFIED: Add `request: Request` to the function signature
async def generate_summary(req: Request, request: GenerateSummaryRequest):
    """Generate AI summary using Gemini 2.5 Flash Preview with Base64 Inline Data"""
    GEMINI_MODEL_ID = "gemini-2.5-flash-preview-05-20"

    async with gemini_manager as client:
        if not client:
             raise HTTPException(status_code=503, detail="Gemini client not available")

        logger.info(f"🎯 Processing consultation with {GEMINI_MODEL_ID} (Base64 Inline Data)...")

        try:
            # ### MODIFIED: Pass the loaded config from app.state
            prompt_config_dict = {k: v for k, v in req.app.state.prompt_config}
            prompt = generate_prompt(
                prompt_templates=prompt_config_dict,
                template_config=request.template_config, 
                submitted_by=request.submitted_by, 
                consultation_type=request.consultation_type, 
                doctor_notes=request.doctor_notes, 
                additional_notes=request.additional_notes, 
                patient_name=request.patient_name, 
                created_at=request.created_at
            )
            contents: List[Any] = [prompt]
            files_processed = {"audio": 0, "images": 0, "errors": []}
            
            all_file_processing_tasks = []

            all_audio_urls = [request.primary_audio_url] + (request.additional_audio_urls or [])
            logger.info(f"🎵 Preparing {len(all_audio_urls)} audio file task(s)...")
            for i, audio_url in enumerate(all_audio_urls):
                file_identifier = f"primary_audio" if i == 0 else f"additional_audio_{i}"
                all_file_processing_tasks.append(
                    process_single_audio_file(audio_url, file_identifier)
                )

            image_urls = request.image_urls or []
            logger.info(f"🖼️ Preparing {len(image_urls)} image file task(s)...")
            for i, image_url in enumerate(image_urls):
                file_identifier = f"image_{i}"
                all_file_processing_tasks.append(
                    process_single_image_file(image_url, file_identifier)
                )
            
            if all_file_processing_tasks:
                logger.info(f"🚀 Launching processing for {len(all_file_processing_tasks)} files concurrently...")
                all_results = await asyncio.gather(*all_file_processing_tasks)

                for result_tuple in all_results:
                    part, identifier, url, error_message = result_tuple
                    
                    if error_message:
                        files_processed["errors"].append({"file": url, "identifier": identifier, "error": error_message})
                    elif part:
                        contents.append(part)
                        if "audio" in identifier:
                            files_processed["audio"] += 1
                        elif "image" in identifier:
                            files_processed["images"] += 1
                    else:
                        unknown_error_msg = f"Unknown issue processing {identifier} from {url} - no part and no error message."
                        logger.error(unknown_error_msg)
                        files_processed["errors"].append({"file": url, "identifier": identifier, "error": unknown_error_msg})

            total_files_successfully_processed = files_processed["audio"] + files_processed["images"]
            
            if not any(isinstance(c, types.Part) for c in contents if c != prompt) and not (request.doctor_notes or request.additional_notes):
                raise HTTPException(status_code=400, detail="No content (audio, image, or text notes) was available to generate a summary.")

            gemini_contents: List[types.Part | str] = [c for c in contents if isinstance(c, (types.Part, str))]
            
            response = await client.aio.models.generate_content(
                model=GEMINI_MODEL_ID,
                contents=gemini_contents
            )

            summary_text = response.text
            return GenerateSummaryResponse(
                summary=summary_text,
                model=GEMINI_MODEL_ID,
                timestamp=datetime.now().isoformat(),
                files_processed=files_processed
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Error generating summary: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Failed to generate summary: {str(e)}")

@app.post("/api/generate-summary-stream")
# ### MODIFIED: Add `req: Request` to the function signature
async def generate_summary_stream(req: Request, request: GenerateSummaryStreamRequest):
    """Generate AI summary using Gemini 2.5 Flash with streaming response"""
    GEMINI_MODEL_ID = "gemini-2.5-flash-preview-05-20"

    if not request.primary_audio_url or not request.primary_audio_url.startswith(('http://', 'https://')):
        async def error_generator():
            yield f"data: {json.dumps({'type': 'error', 'message': 'Primary audio URL is required and must be a valid URL.'})}\n\n"
        return StreamingResponse(error_generator(), media_type="text/plain")

    async def stream_generator():
        async with gemini_manager as client:
            if not client:
                yield f"data: {json.dumps({'type': 'error', 'message': 'Gemini client not available'})}\n\n"
                return

            try:
                # ### MODIFIED: Pass the loaded config from app.state
                prompt_config_dict = {k: v for k, v in req.app.state.prompt_config}
                prompt = generate_prompt(
                    prompt_templates=prompt_config_dict,
                    template_config=request.template_config, 
                    submitted_by=request.submitted_by, 
                    consultation_type=request.consultation_type, 
                    doctor_notes=request.doctor_notes, 
                    additional_notes=request.additional_notes, 
                    patient_name=request.patient_name, 
                    created_at=request.created_at
                )
                contents: List[Any] = [prompt]
                files_processed = {"audio": 0, "images": 0, "errors": []}
                
                # ... (rest of the file processing logic is the same) ...
                all_file_processing_tasks = []

                all_audio_urls = [request.primary_audio_url] + (request.additional_audio_urls or [])
                for i, audio_url in enumerate(all_audio_urls):
                    file_identifier = f"primary_audio" if i == 0 else f"additional_audio_{i}"
                    all_file_processing_tasks.append(
                        process_single_audio_file(audio_url, file_identifier)
                    )

                image_urls = request.image_urls or []
                for i, image_url in enumerate(image_urls):
                    file_identifier = f"image_{i}"
                    all_file_processing_tasks.append(
                        process_single_image_file(image_url, file_identifier)
                    )
                
                if all_file_processing_tasks:
                    all_results = await asyncio.gather(*all_file_processing_tasks)
                    for part, identifier, url, error_message in all_results:
                        if error_message:
                            files_processed["errors"].append({"file": url, "identifier": identifier, "error": error_message})
                        elif part:
                            contents.append(part)
                            if "audio" in identifier: files_processed["audio"] += 1
                            elif "image" in identifier: files_processed["images"] += 1

                if not any(isinstance(c, types.Part) for c in contents if c != prompt) and not (request.doctor_notes or request.additional_notes):
                    yield f"data: {json.dumps({'type': 'error', 'message': 'No content available to generate summary.'})}\n\n"
                    return

                gemini_contents: List[types.Part | str] = [c for c in contents if isinstance(c, (types.Part, str))]

                metadata = {"type": "metadata", "model": GEMINI_MODEL_ID, "timestamp": datetime.now().isoformat(), "files_processed": files_processed}
                yield f"data: {json.dumps(metadata)}\n\n"

                stream_response = client.aio.models.generate_content_stream(model=GEMINI_MODEL_ID, contents=gemini_contents)
                
                async for chunk in stream_response:
                    if chunk.text:
                        yield f"data: {json.dumps({'type': 'chunk', 'text': chunk.text})}\n\n"
                        await asyncio.sleep(0.001)

                yield f"data: {json.dumps({'type': 'complete'})}\n\n"

            except Exception as e:
                logger.error(f"❌ Error generating streaming summary: {e}", exc_info=True)
                yield f"data: {json.dumps({'type': 'error', 'message': f'Failed to generate summary: {str(e)}'})}\n\n"
    
    return StreamingResponse(
        stream_generator(),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache", "X-Accel-Buffering": "no"}
    )

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception caught by handler: {exc}", exc_info=True)
    status_code = 500
    detail = str(exc)
    if isinstance(exc, HTTPException):
        status_code = exc.status_code
        detail = exc.detail
        
    return JSONResponse(
        status_code=status_code,
        content={"error": "Internal server error", "detail": detail}
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("PORT", 8080)), log_level="info")
```