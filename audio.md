# Audio Workflow Documentation - <PERSON>ler AI Medical Consultation System

## System Overview

**Celer AI** is a medical consultation documentation system that records doctor-patient consultations and generates AI-powered medical summaries. The system consists of:

- **Frontend**: Next.js 14 with TypeScript, React, Tailwind CSS
- **Backend**: Python FastAPI with Google Gemini 2.5 Flash AI
- **Database**: Supabase (PostgreSQL)
- **Storage**: Cloudflare R2 (object storage)
- **AI Provider**: Google Gemini 2.5 Flash Preview

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Python API     │    │   Google        │
│   (Next.js)     │◄──►│   (FastAPI)      │◄──►│   Gemini AI     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │
         ▼                        ▼
┌─────────────────┐    ┌──────────────────┐
│   Supabase      │    │   Cloudflare R2  │
│   (Database)    │    │   (File Storage) │
└─────────────────┘    └──────────────────┘
```

## Complete Audio Workflow

### 1. Audio Recording (Frontend)

#### Recording Components
- **Main Interface**: `src/components/recording/streamlined-recording-area.tsx`
- **Mobile Interface**: `src/components/mobile/audio-recorder.tsx`
- **Core Logic**: `src/components/recording/recording-main-area.tsx`

#### Recording Process
```javascript
// Audio capture with quality settings
const stream = await navigator.mediaDevices.getUserMedia({
  audio: {
    echoCancellation: true,
    noiseSuppression: true,
    sampleRate: 44100  // Mobile only
  }
})

// Format detection (best supported)
const supportedFormats = [
  'audio/webm;codecs=opus',  // Preferred
  'audio/webm',
  'audio/mp4', 
  'audio/mpeg'
]
```

#### Recording States
- **Start**: Creates MediaRecorder, begins capture
- **Pause/Resume**: Supports pause/resume functionality
- **Stop**: Generates Blob, creates File object
- **Duration Tracking**: Real-time timer display

#### File Generation
```javascript
// Creates timestamped file
const file = new File([blob], `recording_${Date.now()}.${extension}`, {
  type: mimeType
})
```

### 2. Audio Preview & Playback

#### Preview Implementation
```javascript
// Local blob preview
const audioUrl = URL.createObjectURL(audioBlob)

// Stored file playback
<audio src={consultation.primary_audio_url} />

// Playback controls
const toggleAudioPlayback = (audioId) => {
  const audio = audioRefs.current[audioId]
  if (audioPlaying === audioId) {
    audio.pause()
  } else {
    audio.play()
  }
}
```

#### Audio Types Supported
- **Primary Audio**: Main consultation recording (required)
- **Additional Audio**: Supplementary recordings (optional)
- **Multiple Playback**: Can handle multiple audio files per consultation

### 3. File Storage System

#### Storage Configuration (`src/lib/storage.ts`)
```javascript
export const STORAGE_CONFIG = {
  BUCKET_NAME: 'celerai-storage',
  PUBLIC_URL: 'https://celerai.tallyup.pro',
  AUDIO_PREFIX: 'consultation-audio',
  IMAGE_PREFIX: 'consultation-images',
  MAX_FILE_SIZE: 100 * 1024 * 1024,      // 100MB per file
  MAX_TOTAL_SIZE: 200 * 1024 * 1024,     // 200MB per consultation
  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg']
}
```

#### File Organization Structure
```
consultation-audio/
├── {doctorId}/
│   ├── {consultationId}/
│   │   ├── recording_1234567890.webm     (primary)
│   │   ├── additional_audio_1234567891.webm
│   │   └── additional_audio_1234567892.webm
```

#### Upload Process (`src/lib/storage.ts`)
```javascript
export async function uploadFile(file, doctorId, consultationId, type) {
  // 1. Validate file type and size
  const validation = validateFile(file, type)
  
  // 2. Generate storage path
  const filePath = generateStoragePath(doctorId, consultationId, file.name, type)
  
  // 3. Upload to Cloudflare R2
  const uploadCommand = new PutObjectCommand({
    Bucket: STORAGE_CONFIG.BUCKET_NAME,
    Key: filePath,
    Body: new Uint8Array(fileBuffer),
    ContentType: file.type,
    CacheControl: 'public, max-age=3600'
  })
  
  // 4. Return public URL
  return { success: true, url: `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}` }
}
```

### 4. Database Schema

#### Consultations Table
```sql
CREATE TABLE consultations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  
  -- Audio Storage URLs
  primary_audio_url TEXT NOT NULL,                    -- Required main recording
  additional_audio_urls JSONB DEFAULT '[]'::jsonb,   -- Optional additional recordings
  image_urls JSONB DEFAULT '[]'::jsonb,              -- Optional images
  
  -- Consultation Metadata
  consultation_type TEXT DEFAULT 'outpatient',       -- outpatient, discharge, surgery, radiology, dermatology, cardiology_echo, ivf_cycle, pathology
  patient_name TEXT,
  patient_number INTEGER,
  submitted_by TEXT CHECK (submitted_by IN ('doctor','receptionist')),
  
  -- Notes and Content
  doctor_notes TEXT,                                  -- Notes during recording
  additional_notes TEXT,                              -- Additional context
  ai_generated_note TEXT,                            -- AI summary
  edited_note TEXT,                                  -- Doctor-edited summary
  
  -- Status and Metadata
  status TEXT DEFAULT 'pending',                     -- pending, generated, approved
  total_file_size_bytes INTEGER,
  file_retention_until TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Consultation Creation (`src/lib/actions/consultations.ts`)
```javascript
export async function createConsultationWithFiles(
  audioFile,           // Primary audio (required)
  imageFiles,          // Images (optional)
  additionalAudioFiles, // Additional audio (optional)
  submittedBy,         // 'doctor' | 'receptionist'
  doctorNotes,         // Optional notes
  consultationType,    // Template type
  patientName          // Patient identifier
) {
  // 1. Validate total file size
  const totalSizeValidation = validateTotalSize(allFiles)
  
  // 2. Generate consultation ID
  const consultationId = crypto.randomUUID()
  
  // 3. Upload all files to R2
  const audioUploadResult = await uploadFile(audioFile, userId, consultationId, 'audio')
  const imageUploadResults = await uploadMultipleFiles(imageFiles, userId, consultationId, 'image')
  
  // 4. Create database record
  const consultation = await supabase.from('consultations').insert({
    id: consultationId,
    doctor_id: userId,
    primary_audio_url: audioUploadResult.url,
    additional_audio_urls: additionalAudioUrls,
    image_urls: imageUrls,
    consultation_type: consultationType,
    patient_name: patientName,
    submitted_by: submittedBy,
    status: 'pending'
  })
}
```

### 5. Backend Audio Processing

#### Python Backend (`python-backend/main.py`)
- **Framework**: FastAPI with async/await
- **AI Model**: Google Gemini 2.5 Flash Preview
- **Audio Processing**: FFmpeg for format conversion
- **Image Processing**: PIL for image optimization

#### Audio Processing Pipeline
```python
async def process_single_audio_file(audio_url, audio_file_identifier):
    # 1. Download from R2 URL
    file_bytes, mime_type = await download_file_from_url(audio_url)
    
    # 2. Convert to WAV (16kHz mono) using FFmpeg
    wav_bytes, wav_mime_type = await convert_audio_to_wav(file_bytes)
    
    # 3. Create Gemini Part object
    audio_part = types.Part.from_bytes(data=wav_bytes, mime_type=wav_mime_type)
    
    return audio_part
```

#### FFmpeg Conversion
```python
async def convert_audio_to_wav(file_bytes):
    cmd = [
        ffmpeg_path,
        '-i', input_file,
        '-acodec', 'pcm_s16le',  # 16-bit PCM
        '-ac', '1',              # Mono
        '-ar', '16000',          # 16kHz sample rate
        '-y',                    # Overwrite output
        output_file
    ]
    # Optimized for AI speech recognition
```

### 6. AI Generation Workflow

#### Frontend Trigger (`streamlined-recording-area.tsx`)
```javascript
const handleGenerate = async () => {
  // 1. Save consultation first (if new)
  if (!selectedConsultation && audioBlob) {
    const result = await handleSubmitConsultation()
    if (!result.success) return
  }
  
  // 2. Call streaming API
  const response = await fetch('/api/generate-summary-stream', {
    method: 'POST',
    body: JSON.stringify({
      primary_audio_url: consultation.primary_audio_url,
      additional_audio_urls: consultation.additional_audio_urls || [],
      image_urls: consultation.image_urls || [],
      consultation_type: selectedTemplate,
      patient_name: consultation.patient_name,
      doctor_notes: consultation.doctor_notes,
      additional_notes: additionalNotes
    })
  })
  
  // 3. Process streaming response
  const reader = response.body.getReader()
  while (true) {
    const { done, value } = await reader.read()
    if (done) break
    
    // Parse SSE chunks and update UI
    const chunk = decoder.decode(value)
    // Real-time display of AI generation
  }
}
```

#### Backend AI Processing (`python-backend/main.py`)
```python
@app.post("/api/generate-summary-stream")
async def generate_summary_stream(request: GenerateSummaryStreamRequest):
    # 1. Download and process all files
    all_audio_urls = [request.primary_audio_url] + (request.additional_audio_urls or [])
    
    # Process audio files concurrently
    audio_tasks = [process_single_audio_file(url, f"audio_{i}") for i, url in enumerate(all_audio_urls)]
    image_tasks = [process_single_image_file(url, f"image_{i}") for i, url in enumerate(request.image_urls or [])]
    
    all_results = await asyncio.gather(*audio_tasks, *image_tasks)
    
    # 2. Generate consultation-type specific prompt
    prompt = generate_prompt(
        consultation_type=request.consultation_type,
        doctor_notes=request.doctor_notes,
        additional_notes=request.additional_notes,
        patient_name=request.patient_name
    )
    
    # 3. Send to Gemini with streaming
    contents = [prompt] + [part for part, _, _, _ in all_results if part]
    
    stream_response = await client.aio.models.generate_content_stream(
        model="gemini-2.5-flash-lite-preview-06-17",
        contents=contents,
        config=types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(thinking_budget=8000)
        )
    )
    
    # 4. Stream response back to frontend
    async for chunk in stream_response:
        if chunk.text:
            yield f"data: {json.dumps({'type': 'chunk', 'text': chunk.text})}\n\n"
```

### 7. Consultation Templates

#### Supported Types
- **outpatient**: Regular consultation visits
- **discharge**: Hospital discharge summaries  
- **surgery**: Operative notes
- **radiology**: Imaging interpretation reports
- **dermatology**: SOAP format dermatology notes
- **cardiology_echo**: Echocardiogram reports
- **ivf_cycle**: Reproductive medicine summaries
- **pathology**: Histopathology reports

#### Template Configuration (`python-backend/prompts.json`)
```json
{
  "outpatient": {
    "format": "structured medical note",
    "style": "professional clinical documentation",
    "ai_instruction": "Generate a comprehensive outpatient consultation summary...",
    "template_structure": "## CONSULTATION SUMMARY\n\n**Patient:** {patient_name}\n**Date:** {date}\n\n### CHIEF COMPLAINT\n### HISTORY OF PRESENT ILLNESS\n### PHYSICAL EXAMINATION\n### ASSESSMENT\n### PLAN",
    "sections": ["chief_complaint", "history", "examination", "assessment", "plan"]
  }
}
```

### 8. API Routes

#### Frontend API Proxy (`src/app/api/generate-summary-stream/route.ts`)
```javascript
export async function POST(request: NextRequest) {
  const body = await request.json()
  
  // Forward to Python backend
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body)
  })
  
  // Stream response back to client
  return new Response(response.body, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}
```

### 9. Error Handling & Validation

#### File Validation (`src/lib/storage.ts`)
```javascript
export function validateFile(file: File, type: 'audio' | 'image') {
  const allowedTypes = type === 'audio' 
    ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES 
    : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES
    
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: `Invalid file type: ${file.type}` }
  }
  
  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {
    return { valid: false, error: `File too large: ${file.size} bytes` }
  }
  
  return { valid: true }
}
```

#### Backend Error Handling
- **File Download Failures**: Retry logic and fallback handling
- **FFmpeg Conversion Errors**: Format detection and alternative codecs
- **AI API Failures**: Graceful degradation and error reporting
- **Storage Failures**: Transaction rollback and cleanup

### 10. Performance Optimizations

#### Concurrent Processing
- **File Processing**: Parallel audio/image processing
- **Streaming**: Real-time AI response streaming
- **Caching**: R2 public URLs with cache headers

#### Resource Management
- **Memory**: Temporary file cleanup
- **Storage**: Automatic file retention policies
- **API**: Rate limiting and quota management

### 11. Security & Privacy

#### Access Control
- **Authentication**: Supabase Auth with session verification
- **Authorization**: Doctor-specific file access
- **File URLs**: Signed URLs for temporary access

#### Data Protection
- **Encryption**: Files encrypted at rest in R2
- **Retention**: Automatic cleanup after retention period
- **Audit**: Usage logging for compliance

## Key Technical Decisions

1. **Cloudflare R2 over Supabase Storage**: Better performance and cost
2. **FFmpeg Conversion**: Standardized audio format for AI processing
3. **Streaming Responses**: Real-time user feedback during AI generation
4. **Multiple Audio Support**: Flexibility for complex consultations
5. **Template System**: Specialized prompts for different medical specialties

## Development Setup

### Environment Variables
```bash
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:3005
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key

# Backend (.env)
GEMINI_API_KEY=your_gemini_key
R2_ACCOUNT_ID=your_r2_account
R2_ACCESS_KEY_ID=your_r2_key
R2_SECRET_ACCESS_KEY=your_r2_secret
R2_BUCKET_NAME=celerai-storage
```

### Dependencies
```bash
# Frontend
npm install next react typescript tailwindcss framer-motion lucide-react

# Backend  
pip install fastapi uvicorn google-generativeai httpx pillow python-multipart
```

This documentation provides a complete understanding of the audio workflow in the Celer AI medical consultation system, from recording to AI-powered summary generation.
