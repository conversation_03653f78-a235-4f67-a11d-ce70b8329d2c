--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 16.9 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA auth;


ALTER SCHEMA auth OWNER TO supabase_admin;

--
-- Name: pg_cron; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_cron WITH SCHEMA pg_catalog;


--
-- Name: EXTENSION pg_cron; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_cron IS 'Job scheduler for PostgreSQL';


--
-- Name: extensions; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA extensions;


ALTER SCHEMA extensions OWNER TO postgres;

--
-- Name: graphql; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql;


ALTER SCHEMA graphql OWNER TO supabase_admin;

--
-- Name: graphql_public; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql_public;


ALTER SCHEMA graphql_public OWNER TO supabase_admin;

--
-- Name: pg_net; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_net WITH SCHEMA public;


--
-- Name: EXTENSION pg_net; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_net IS 'Async HTTP';


--
-- Name: pgbouncer; Type: SCHEMA; Schema: -; Owner: pgbouncer
--

CREATE SCHEMA pgbouncer;


ALTER SCHEMA pgbouncer OWNER TO pgbouncer;

--
-- Name: realtime; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA realtime;


ALTER SCHEMA realtime OWNER TO supabase_admin;

--
-- Name: storage; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA storage;


ALTER SCHEMA storage OWNER TO supabase_admin;

--
-- Name: vault; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA vault;


ALTER SCHEMA vault OWNER TO supabase_admin;

--
-- Name: http; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS http WITH SCHEMA public;


--
-- Name: EXTENSION http; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION http IS 'HTTP client for PostgreSQL, allows web page retrieval inside the database.';


--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


ALTER TYPE auth.aal_level OWNER TO supabase_auth_admin;

--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


ALTER TYPE auth.code_challenge_method OWNER TO supabase_auth_admin;

--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


ALTER TYPE auth.factor_status OWNER TO supabase_auth_admin;

--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


ALTER TYPE auth.factor_type OWNER TO supabase_auth_admin;

--
-- Name: one_time_token_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.one_time_token_type AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


ALTER TYPE auth.one_time_token_type OWNER TO supabase_auth_admin;

--
-- Name: consultation_type_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.consultation_type_enum AS ENUM (
    'outpatient',
    'discharge',
    'surgery',
    'radiology',
    'dermatology',
    'cardiology_echo',
    'ivf_cycle',
    'pathology'
);


ALTER TYPE public.consultation_type_enum OWNER TO postgres;

--
-- Name: action; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.action AS ENUM (
    'INSERT',
    'UPDATE',
    'DELETE',
    'TRUNCATE',
    'ERROR'
);


ALTER TYPE realtime.action OWNER TO supabase_admin;

--
-- Name: equality_op; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.equality_op AS ENUM (
    'eq',
    'neq',
    'lt',
    'lte',
    'gt',
    'gte',
    'in'
);


ALTER TYPE realtime.equality_op OWNER TO supabase_admin;

--
-- Name: user_defined_filter; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.user_defined_filter AS (
	column_name text,
	op realtime.equality_op,
	value text
);


ALTER TYPE realtime.user_defined_filter OWNER TO supabase_admin;

--
-- Name: wal_column; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_column AS (
	name text,
	type_name text,
	type_oid oid,
	value jsonb,
	is_pkey boolean,
	is_selectable boolean
);


ALTER TYPE realtime.wal_column OWNER TO supabase_admin;

--
-- Name: wal_rls; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_rls AS (
	wal jsonb,
	is_rls_enabled boolean,
	subscription_ids uuid[],
	errors text[]
);


ALTER TYPE realtime.wal_rls OWNER TO supabase_admin;

--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


ALTER FUNCTION auth.email() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


ALTER FUNCTION auth.jwt() OWNER TO supabase_auth_admin;

--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


ALTER FUNCTION auth.role() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


ALTER FUNCTION auth.uid() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: grant_pg_cron_access(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.grant_pg_cron_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_cron'
  )
  THEN
    grant usage on schema cron to postgres with grant option;

    alter default privileges in schema cron grant all on tables to postgres with grant option;
    alter default privileges in schema cron grant all on functions to postgres with grant option;
    alter default privileges in schema cron grant all on sequences to postgres with grant option;

    alter default privileges for user supabase_admin in schema cron grant all
        on sequences to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on tables to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on functions to postgres with grant option;

    grant all privileges on all tables in schema cron to postgres with grant option;
    revoke all on table cron.job from postgres;
    grant select on table cron.job to postgres with grant option;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_cron_access() OWNER TO supabase_admin;

--
-- Name: FUNCTION grant_pg_cron_access(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.grant_pg_cron_access() IS 'Grants access to pg_cron';


--
-- Name: grant_pg_graphql_access(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.grant_pg_graphql_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
    func_is_graphql_resolve bool;
BEGIN
    func_is_graphql_resolve = (
        SELECT n.proname = 'resolve'
        FROM pg_event_trigger_ddl_commands() AS ev
        LEFT JOIN pg_catalog.pg_proc AS n
        ON ev.objid = n.oid
    );

    IF func_is_graphql_resolve
    THEN
        -- Update public wrapper to pass all arguments through to the pg_graphql resolve func
        DROP FUNCTION IF EXISTS graphql_public.graphql;
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language sql
        as $$
            select graphql.resolve(
                query := query,
                variables := coalesce(variables, '{}'),
                "operationName" := "operationName",
                extensions := extensions
            );
        $$;

        -- This hook executes when `graphql.resolve` is created. That is not necessarily the last
        -- function in the extension so we need to grant permissions on existing entities AND
        -- update default permissions to any others that are created after `graphql.resolve`
        grant usage on schema graphql to postgres, anon, authenticated, service_role;
        grant select on all tables in schema graphql to postgres, anon, authenticated, service_role;
        grant execute on all functions in schema graphql to postgres, anon, authenticated, service_role;
        grant all on all sequences in schema graphql to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on tables to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on functions to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on sequences to postgres, anon, authenticated, service_role;

        -- Allow postgres role to allow granting usage on graphql and graphql_public schemas to custom roles
        grant usage on schema graphql_public to postgres with grant option;
        grant usage on schema graphql to postgres with grant option;
    END IF;

END;
$_$;


ALTER FUNCTION extensions.grant_pg_graphql_access() OWNER TO supabase_admin;

--
-- Name: FUNCTION grant_pg_graphql_access(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.grant_pg_graphql_access() IS 'Grants access to pg_graphql';


--
-- Name: grant_pg_net_access(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.grant_pg_net_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_net'
  )
  THEN
    IF NOT EXISTS (
      SELECT 1
      FROM pg_roles
      WHERE rolname = 'supabase_functions_admin'
    )
    THEN
      CREATE USER supabase_functions_admin NOINHERIT CREATEROLE LOGIN NOREPLICATION;
    END IF;

    GRANT USAGE ON SCHEMA net TO supabase_functions_admin, postgres, anon, authenticated, service_role;

    IF EXISTS (
      SELECT FROM pg_extension
      WHERE extname = 'pg_net'
      -- all versions in use on existing projects as of 2025-02-20
      -- version 0.12.0 onwards don't need these applied
      AND extversion IN ('0.2', '0.6', '0.7', '0.7.1', '0.8', '0.10.0', '0.11.0')
    ) THEN
      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;

      REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
      REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;

      GRANT EXECUTE ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
      GRANT EXECUTE ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
    END IF;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_net_access() OWNER TO supabase_admin;

--
-- Name: FUNCTION grant_pg_net_access(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.grant_pg_net_access() IS 'Grants access to pg_net';


--
-- Name: pgrst_ddl_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_ddl_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  cmd record;
BEGIN
  FOR cmd IN SELECT * FROM pg_event_trigger_ddl_commands()
  LOOP
    IF cmd.command_tag IN (
      'CREATE SCHEMA', 'ALTER SCHEMA'
    , 'CREATE TABLE', 'CREATE TABLE AS', 'SELECT INTO', 'ALTER TABLE'
    , 'CREATE FOREIGN TABLE', 'ALTER FOREIGN TABLE'
    , 'CREATE VIEW', 'ALTER VIEW'
    , 'CREATE MATERIALIZED VIEW', 'ALTER MATERIALIZED VIEW'
    , 'CREATE FUNCTION', 'ALTER FUNCTION'
    , 'CREATE TRIGGER'
    , 'CREATE TYPE', 'ALTER TYPE'
    , 'CREATE RULE'
    , 'COMMENT'
    )
    -- don't notify in case of CREATE TEMP table or other objects created on pg_temp
    AND cmd.schema_name is distinct from 'pg_temp'
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_ddl_watch() OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_drop_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  obj record;
BEGIN
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects()
  LOOP
    IF obj.object_type IN (
      'schema'
    , 'table'
    , 'foreign table'
    , 'view'
    , 'materialized view'
    , 'function'
    , 'trigger'
    , 'type'
    , 'rule'
    )
    AND obj.is_temporary IS false -- no pg_temp objects
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_drop_watch() OWNER TO supabase_admin;

--
-- Name: set_graphql_placeholder(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.set_graphql_placeholder() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
    DECLARE
    graphql_is_dropped bool;
    BEGIN
    graphql_is_dropped = (
        SELECT ev.schema_name = 'graphql_public'
        FROM pg_event_trigger_dropped_objects() AS ev
        WHERE ev.schema_name = 'graphql_public'
    );

    IF graphql_is_dropped
    THEN
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language plpgsql
        as $$
            DECLARE
                server_version float;
            BEGIN
                server_version = (SELECT (SPLIT_PART((select version()), ' ', 2))::float);

                IF server_version >= 14 THEN
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql extension is not enabled.'
                            )
                        )
                    );
                ELSE
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql is only available on projects running Postgres 14 onwards.'
                            )
                        )
                    );
                END IF;
            END;
        $$;
    END IF;

    END;
$_$;


ALTER FUNCTION extensions.set_graphql_placeholder() OWNER TO supabase_admin;

--
-- Name: FUNCTION set_graphql_placeholder(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.set_graphql_placeholder() IS 'Reintroduces placeholder function for graphql_public.graphql';


--
-- Name: get_auth(text); Type: FUNCTION; Schema: pgbouncer; Owner: supabase_admin
--

CREATE FUNCTION pgbouncer.get_auth(p_usename text) RETURNS TABLE(username text, password text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
begin
    raise debug 'PgBouncer auth request: %', p_usename;

    return query
    select 
        rolname::text, 
        case when rolvaliduntil < now() 
            then null 
            else rolpassword::text 
        end 
    from pg_authid 
    where rolname=$1 and rolcanlogin;
end;
$_$;


ALTER FUNCTION pgbouncer.get_auth(p_usename text) OWNER TO supabase_admin;

--
-- Name: apply_referral_discount(uuid, numeric); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.apply_referral_discount(transaction_id uuid, discount_amount numeric) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    transaction_record RECORD;
    doctor_available_discount DECIMAL(10,2);
    applied_amount DECIMAL(10,2);
BEGIN
    SELECT * INTO transaction_record
    FROM billing_transactions 
    WHERE id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    SELECT available_discount_amount INTO doctor_available_discount
    FROM doctors
    WHERE id = transaction_record.doctor_id;
    
    applied_amount := LEAST(discount_amount, doctor_available_discount, transaction_record.amount);
    
    UPDATE billing_transactions
    SET discount_amount = applied_amount,
        final_amount = amount - applied_amount
    WHERE id = transaction_id;
    
    UPDATE doctors
    SET available_discount_amount = available_discount_amount - applied_amount
    WHERE id = transaction_record.doctor_id;
    
    UPDATE referral_discounts
    SET status = 'applied',
        applied_to_transaction_id = transaction_id,
        applied_at = NOW()
    WHERE doctor_id = transaction_record.doctor_id 
    AND status = 'pending'
    AND discount_amount <= applied_amount;
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.apply_referral_discount(transaction_id uuid, discount_amount numeric) OWNER TO postgres;

--
-- Name: check_and_update_quota(uuid); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.check_and_update_quota(doctor_uuid uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    current_quota_used INT;
    monthly_quota_limit INT;
BEGIN
    -- Get current quota info
    SELECT quota_used, monthly_quota
    INTO current_quota_used, monthly_quota_limit
    FROM doctors
    WHERE id = doctor_uuid AND approved = true;

    -- Check if doctor exists and is approved
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- THE FAULTY "just-in-time" RESET LOGIC HAS BEEN REMOVED FROM HERE.
    -- The quota reset is now handled correctly by the complete_payment function.

    -- Check if quota is available
    IF current_quota_used >= monthly_quota_limit THEN
        RETURN FALSE;
    END IF;

    -- Update quota usage
    UPDATE doctors
    SET quota_used = quota_used + 1
    WHERE id = doctor_uuid;

    -- Log quota usage
    INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
    VALUES (doctor_uuid, 'ai_generation', current_quota_used, current_quota_used + 1,
            json_build_object('timestamp', NOW())::jsonb);

    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.check_and_update_quota(doctor_uuid uuid) OWNER TO postgres;

--
-- Name: complete_payment(uuid); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.complete_payment(transaction_id uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    transaction_record RECORD;
    is_first_payment BOOLEAN;
    referral_analytics_id UUID;
    plan_quota INT; -- Variable to hold the quota from the plan
BEGIN
    -- Get transaction details, including the plan_id
    SELECT
        bt.*,
        d.referred_by,
        CASE
            WHEN d.referred_by IS NOT NULL THEN (
                SELECT COUNT(*) = 0
                FROM billing_transactions bt2
                WHERE bt2.doctor_id = bt.doctor_id AND bt2.payment_status = 'paid' AND bt2.id != bt.id
            )
            ELSE FALSE
        END as is_first_payment
    INTO transaction_record
    FROM billing_transactions bt
    JOIN doctors d ON bt.doctor_id = d.id
    WHERE bt.id = transaction_id;

    IF NOT FOUND THEN
        RAISE NOTICE 'Transaction % not found', transaction_id;
        RETURN FALSE;
    END IF;

    -- Get the quota from the billing plan associated with this transaction
    SELECT quota_limit INTO plan_quota
    FROM billing_plans
    WHERE id = transaction_record.plan_id;

    -- Update transaction status
    UPDATE billing_transactions
    SET payment_status = 'paid',
        payment_date = NOW(),
        updated_at = NOW()
    WHERE id = transaction_id;

    -- ** THIS IS THE CORE FIX **
    -- Update doctor's status, quota, and reset date all at once, perfectly aligned.
    UPDATE doctors
    SET
        billing_status = 'active',
        last_payment_date = NOW(),
        next_billing_date = transaction_record.billing_period_end,
        -- Set their monthly quota based on the plan they just paid for
        monthly_quota = plan_quota,
        -- Reset their used quota to 0
        quota_used = 0,
        -- Align the next quota reset with the end of their billing period
        quota_reset_at = transaction_record.billing_period_end,
        -- Ensure their current plan is updated
        current_plan_id = transaction_record.plan_id
    WHERE id = transaction_record.doctor_id;

    RAISE NOTICE 'Payment completed for transaction %, doctor %',
        transaction_id, transaction_record.doctor_id;

    -- The referral logic below remains unchanged.
    IF transaction_record.referred_by IS NOT NULL AND is_first_payment THEN
        RAISE NOTICE 'First payment for referred doctor %, processing referral conversion',
            transaction_record.doctor_id;

        SELECT id INTO referral_analytics_id
        FROM referral_analytics
        WHERE referred_doctor_id = transaction_record.doctor_id
        AND status = 'pending'
        LIMIT 1;

        PERFORM handle_referral_conversion(transaction_record.doctor_id);

        IF referral_analytics_id IS NOT NULL THEN
            PERFORM create_referral_discount(
                transaction_record.referred_by,
                referral_analytics_id,
                transaction_record.final_amount
            );
            RAISE NOTICE 'Created referral discount for referrer % based on amount %',
                transaction_record.referred_by, transaction_record.final_amount;
        END IF;

    ELSIF transaction_record.referred_by IS NOT NULL THEN
        RAISE NOTICE 'Subsequent payment for referred doctor % - no referral bonus',
            transaction_record.doctor_id;
    END IF;

    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.complete_payment(transaction_id uuid) OWNER TO postgres;

--
-- Name: create_referral_discount(uuid, uuid, numeric); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_referral_discount(referrer_doctor_id uuid, referral_analytics_id uuid, referred_amount numeric) RETURNS uuid
    LANGUAGE plpgsql
    AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount of the actual transaction amount
    discount_amount := referred_amount * 0.20;
    
    -- Create referral discount record
    INSERT INTO referral_discounts (
        doctor_id,
        referral_analytics_id,
        discount_amount,
        original_amount,
        discount_percentage,
        valid_until,
        status
    ) VALUES (
        referrer_doctor_id,
        referral_analytics_id,
        discount_amount,
        referred_amount,
        20.00,
        NOW() + INTERVAL '12 months',
        'applied'
    ) RETURNING id INTO discount_id;
    
    -- Update referrer's available discount in doctors table
    UPDATE doctors 
    SET available_discount_amount = available_discount_amount + discount_amount,
        referral_discount_earned = referral_discount_earned + discount_amount
    WHERE id = referrer_doctor_id;
    
    RAISE NOTICE 'Created referral discount: ₹% for referrer %', discount_amount, referrer_doctor_id;
    
    RETURN discount_id;
END;
$$;


ALTER FUNCTION public.create_referral_discount(referrer_doctor_id uuid, referral_analytics_id uuid, referred_amount numeric) OWNER TO postgres;

--
-- Name: fix_doctor_discount_amounts(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.fix_doctor_discount_amounts() RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    doctor_record RECORD;
    total_earned DECIMAL(10,2);
    total_used DECIMAL(10,2);
    correct_available DECIMAL(10,2);
BEGIN
    RAISE NOTICE 'Fixing doctor discount amounts based on actual applied discounts...';
    
    -- Loop through all doctors who should have discounts
    FOR doctor_record IN 
        SELECT DISTINCT d.id, d.name
        FROM doctors d
        JOIN referral_discounts rd ON rd.doctor_id = d.id
        WHERE rd.status = 'applied'
    LOOP
        -- Calculate total earned from applied discounts
        SELECT COALESCE(SUM(discount_amount), 0) INTO total_earned
        FROM referral_discounts
        WHERE doctor_id = doctor_record.id AND status = 'applied';
        
        -- Calculate total used from transactions
        SELECT COALESCE(SUM(bt.discount_amount), 0) INTO total_used
        FROM billing_transactions bt
        WHERE bt.doctor_id = doctor_record.id AND bt.discount_amount > 0;
        
        -- Calculate correct available amount
        correct_available := total_earned - total_used;
        
        -- Update doctor record
        UPDATE doctors
        SET referral_discount_earned = total_earned,
            available_discount_amount = correct_available
        WHERE id = doctor_record.id;
        
        RAISE NOTICE 'Fixed doctor %: earned ₹%, available ₹%', 
            doctor_record.name, total_earned, correct_available;
    END LOOP;
    
    RAISE NOTICE 'Doctor discount amounts fix completed';
END;
$$;


ALTER FUNCTION public.fix_doctor_discount_amounts() OWNER TO postgres;

--
-- Name: generate_referral_code(text, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.generate_referral_code(doctor_name text, doctor_email text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    base_code TEXT;
    final_code TEXT;
    counter INT := 0;
BEGIN
    base_code := LOWER(
        REGEXP_REPLACE(
            SUBSTRING(doctor_name FROM 1 FOR 8) || 
            SUBSTRING(MD5(doctor_email) FROM 1 FOR 4),
            '[^a-z0-9]', '', 'g'
        )
    );
    
    final_code := base_code;
    
    WHILE EXISTS (SELECT 1 FROM doctors WHERE referral_code = final_code) LOOP
        counter := counter + 1;
        final_code := base_code || counter::TEXT;
    END LOOP;
    
    RETURN final_code;
END;
$$;


ALTER FUNCTION public.generate_referral_code(doctor_name text, doctor_email text) OWNER TO postgres;

--
-- Name: handle_referral_conversion(uuid); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.handle_referral_conversion(referred_doctor_uuid uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    referrer_uuid UUID;
    discount_amount DECIMAL(10,2);
    referred_amount DECIMAL(10,2);
BEGIN
    -- Get referrer information
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;

    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Get the last payment amount of the referred doctor
    SELECT final_amount INTO referred_amount
    FROM billing_transactions
    WHERE doctor_id = referred_doctor_uuid
      AND payment_status = 'paid'
    ORDER BY payment_date DESC
    LIMIT 1;

    -- Calculate 20% discount of the actual payment amount
    discount_amount := COALESCE(referred_amount * 0.20, 0);

    -- Only proceed if there's an amount to process
    IF discount_amount > 0 THEN
        -- Update conversion date for referred doctor
        UPDATE doctors
        SET conversion_date = NOW()
        WHERE id = referred_doctor_uuid AND conversion_date IS NULL;

        -- Update referral analytics
        UPDATE referral_analytics
        SET status = 'converted',
            conversion_date = NOW(),
            discount_earned = discount_amount
        WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';

        -- Update referrer's successful referral count ONLY
        -- The discount amount itself is handled by the create_referral_discount function
        UPDATE doctors
        SET successful_referrals = successful_referrals + 1
        WHERE id = referrer_uuid;

        RAISE NOTICE 'Referral conversion processed for referrer %', referrer_uuid;
    END IF;

    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.handle_referral_conversion(referred_doctor_uuid uuid) OWNER TO postgres;

--
-- Name: notify_new_doctor(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.notify_new_doctor() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  RAISE NOTICE 'TRIGGER FIRED: notify_new_doctor for doctor: %', NEW.name;

  BEGIN
    RAISE NOTICE 'Executing fire-and-forget HTTP request...';

    -- CORRECTED: Perform the action without trying to capture a result.
    -- The function returns VOID, so we don't use SELECT...INTO.
    PERFORM net.http_post(
      url := 'https://tzjelqzwdgidsjqhmvkr.supabase.co/functions/v1/notify-new-doctor',
      headers := json_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6amVscXp3ZGdpZHNqcWhtdmtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjg2OTAsImV4cCI6MjA2MzkwNDY5MH0.MVkBlC_QI1IT7licw9CzLoS9yyo-QGXR7EFyXYZDyZc'
      )::jsonb,
      body := row_to_json(NEW)::jsonb,
      timeout_milliseconds := 10000
    );

    -- If the PERFORM statement above did not raise an exception, it was queued successfully.
    RAISE NOTICE 'SUCCESS: Notification request was dispatched without error.';

  EXCEPTION WHEN OTHERS THEN
    -- This block catches errors if the http_post function itself fails.
    RAISE WARNING 'ERROR: The net.http_post function failed. Reason: %', SQLERRM;
  END;
  
  RAISE NOTICE 'TRIGGER COMPLETED for doctor: %', NEW.name;
  RETURN NEW;
END;
$$;


ALTER FUNCTION public.notify_new_doctor() OWNER TO postgres;

--
-- Name: pause_expired_accounts(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.pause_expired_accounts() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE doctors
    SET
        billing_status = 'paused',
        -- This is the key change: sets their usage to their limit, effectively blocking them.
        quota_used = monthly_quota
    WHERE
        billing_status IN ('trial', 'active')
        AND (
            (billing_status = 'trial' AND trial_ends_at < NOW())
            OR
            (billing_status = 'active' AND next_billing_date < NOW())
        );
END;
$$;


ALTER FUNCTION public.pause_expired_accounts() OWNER TO postgres;

--
-- Name: reset_all_quotas(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.reset_all_quotas() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    reset_count INT := 0;
    doctor_record RECORD;
BEGIN
    FOR doctor_record IN 
        SELECT id, quota_used 
        FROM doctors 
        WHERE quota_reset_at <= NOW() AND approved = true
    LOOP
        UPDATE doctors 
        SET quota_used = 0,
            quota_reset_at = DATE_TRUNC('month', NOW()) + INTERVAL '1 month'
        WHERE id = doctor_record.id;
        
        -- Log quota reset
        INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
        VALUES (doctor_record.id, 'quota_reset', doctor_record.quota_used, 0, '{"reason": "monthly_batch_reset"}'::jsonb);
        
        reset_count := reset_count + 1;
    END LOOP;
    
    RETURN reset_count;
END;
$$;


ALTER FUNCTION public.reset_all_quotas() OWNER TO postgres;

--
-- Name: set_patient_number(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.set_patient_number() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF NEW.patient_number IS NULL THEN
        SELECT COALESCE(MAX(patient_number), 0) + 1
        INTO NEW.patient_number
        FROM consultations
        WHERE doctor_id = NEW.doctor_id
        AND DATE(created_at) = DATE(NOW());
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.set_patient_number() OWNER TO postgres;

--
-- Name: set_referral_code(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.set_referral_code() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF NEW.referral_code IS NULL THEN
        NEW.referral_code := generate_referral_code(NEW.name, NEW.email);
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.set_referral_code() OWNER TO postgres;

--
-- Name: update_doctor_quota(uuid, integer, uuid); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_doctor_quota(doctor_uuid uuid, new_quota integer, admin_uuid uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    old_quota INT;
BEGIN
    -- Get current quota
    SELECT monthly_quota INTO old_quota
    FROM doctors
    WHERE id = doctor_uuid;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Update quota
    UPDATE doctors 
    SET monthly_quota = new_quota
    WHERE id = doctor_uuid;
    
    -- Log quota update (FIXED JSON)
    INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
    VALUES (doctor_uuid, 'quota_update', old_quota, new_quota, 
            json_build_object('admin_id', admin_uuid, 'timestamp', NOW())::jsonb);
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.update_doctor_quota(doctor_uuid uuid, new_quota integer, admin_uuid uuid) OWNER TO postgres;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO postgres;

--
-- Name: apply_rls(jsonb, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer DEFAULT (1024 * 1024)) RETURNS SETOF realtime.wal_rls
    LANGUAGE plpgsql
    AS $$
declare
-- Regclass of the table e.g. public.notes
entity_ regclass = (quote_ident(wal ->> 'schema') || '.' || quote_ident(wal ->> 'table'))::regclass;

-- I, U, D, T: insert, update ...
action realtime.action = (
    case wal ->> 'action'
        when 'I' then 'INSERT'
        when 'U' then 'UPDATE'
        when 'D' then 'DELETE'
        else 'ERROR'
    end
);

-- Is row level security enabled for the table
is_rls_enabled bool = relrowsecurity from pg_class where oid = entity_;

subscriptions realtime.subscription[] = array_agg(subs)
    from
        realtime.subscription subs
    where
        subs.entity = entity_;

-- Subscription vars
roles regrole[] = array_agg(distinct us.claims_role::text)
    from
        unnest(subscriptions) us;

working_role regrole;
claimed_role regrole;
claims jsonb;

subscription_id uuid;
subscription_has_access bool;
visible_to_subscription_ids uuid[] = '{}';

-- structured info for wal's columns
columns realtime.wal_column[];
-- previous identity values for update/delete
old_columns realtime.wal_column[];

error_record_exceeds_max_size boolean = octet_length(wal::text) > max_record_bytes;

-- Primary jsonb output for record
output jsonb;

begin
perform set_config('role', null, true);

columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'columns') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

old_columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'identity') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

for working_role in select * from unnest(roles) loop

    -- Update `is_selectable` for columns and old_columns
    columns =
        array_agg(
            (
                c.name,
                c.type_name,
                c.type_oid,
                c.value,
                c.is_pkey,
                pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
            )::realtime.wal_column
        )
        from
            unnest(columns) c;

    old_columns =
            array_agg(
                (
                    c.name,
                    c.type_name,
                    c.type_oid,
                    c.value,
                    c.is_pkey,
                    pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                )::realtime.wal_column
            )
            from
                unnest(old_columns) c;

    if action <> 'DELETE' and count(1) = 0 from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            -- subscriptions is already filtered by entity
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 400: Bad Request, no primary key']
        )::realtime.wal_rls;

    -- The claims role does not have SELECT permission to the primary key of entity
    elsif action <> 'DELETE' and sum(c.is_selectable::int) <> count(1) from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 401: Unauthorized']
        )::realtime.wal_rls;

    else
        output = jsonb_build_object(
            'schema', wal ->> 'schema',
            'table', wal ->> 'table',
            'type', action,
            'commit_timestamp', to_char(
                ((wal ->> 'timestamp')::timestamptz at time zone 'utc'),
                'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'
            ),
            'columns', (
                select
                    jsonb_agg(
                        jsonb_build_object(
                            'name', pa.attname,
                            'type', pt.typname
                        )
                        order by pa.attnum asc
                    )
                from
                    pg_attribute pa
                    join pg_type pt
                        on pa.atttypid = pt.oid
                where
                    attrelid = entity_
                    and attnum > 0
                    and pg_catalog.has_column_privilege(working_role, entity_, pa.attname, 'SELECT')
            )
        )
        -- Add "record" key for insert and update
        || case
            when action in ('INSERT', 'UPDATE') then
                jsonb_build_object(
                    'record',
                    (
                        select
                            jsonb_object_agg(
                                -- if unchanged toast, get column name and value from old record
                                coalesce((c).name, (oc).name),
                                case
                                    when (c).name is null then (oc).value
                                    else (c).value
                                end
                            )
                        from
                            unnest(columns) c
                            full outer join unnest(old_columns) oc
                                on (c).name = (oc).name
                        where
                            coalesce((c).is_selectable, (oc).is_selectable)
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                    )
                )
            else '{}'::jsonb
        end
        -- Add "old_record" key for update and delete
        || case
            when action = 'UPDATE' then
                jsonb_build_object(
                        'old_record',
                        (
                            select jsonb_object_agg((c).name, (c).value)
                            from unnest(old_columns) c
                            where
                                (c).is_selectable
                                and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                        )
                    )
            when action = 'DELETE' then
                jsonb_build_object(
                    'old_record',
                    (
                        select jsonb_object_agg((c).name, (c).value)
                        from unnest(old_columns) c
                        where
                            (c).is_selectable
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                            and ( not is_rls_enabled or (c).is_pkey ) -- if RLS enabled, we can't secure deletes so filter to pkey
                    )
                )
            else '{}'::jsonb
        end;

        -- Create the prepared statement
        if is_rls_enabled and action <> 'DELETE' then
            if (select 1 from pg_prepared_statements where name = 'walrus_rls_stmt' limit 1) > 0 then
                deallocate walrus_rls_stmt;
            end if;
            execute realtime.build_prepared_statement_sql('walrus_rls_stmt', entity_, columns);
        end if;

        visible_to_subscription_ids = '{}';

        for subscription_id, claims in (
                select
                    subs.subscription_id,
                    subs.claims
                from
                    unnest(subscriptions) subs
                where
                    subs.entity = entity_
                    and subs.claims_role = working_role
                    and (
                        realtime.is_visible_through_filters(columns, subs.filters)
                        or (
                          action = 'DELETE'
                          and realtime.is_visible_through_filters(old_columns, subs.filters)
                        )
                    )
        ) loop

            if not is_rls_enabled or action = 'DELETE' then
                visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
            else
                -- Check if RLS allows the role to see the record
                perform
                    -- Trim leading and trailing quotes from working_role because set_config
                    -- doesn't recognize the role as valid if they are included
                    set_config('role', trim(both '"' from working_role::text), true),
                    set_config('request.jwt.claims', claims::text, true);

                execute 'execute walrus_rls_stmt' into subscription_has_access;

                if subscription_has_access then
                    visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                end if;
            end if;
        end loop;

        perform set_config('role', null, true);

        return next (
            output,
            is_rls_enabled,
            visible_to_subscription_ids,
            case
                when error_record_exceeds_max_size then array['Error 413: Payload Too Large']
                else '{}'
            end
        )::realtime.wal_rls;

    end if;
end loop;

perform set_config('role', null, true);
end;
$$;


ALTER FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: broadcast_changes(text, text, text, text, text, record, record, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text DEFAULT 'ROW'::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    -- Declare a variable to hold the JSONB representation of the row
    row_data jsonb := '{}'::jsonb;
BEGIN
    IF level = 'STATEMENT' THEN
        RAISE EXCEPTION 'function can only be triggered for each row, not for each statement';
    END IF;
    -- Check the operation type and handle accordingly
    IF operation = 'INSERT' OR operation = 'UPDATE' OR operation = 'DELETE' THEN
        row_data := jsonb_build_object('old_record', OLD, 'record', NEW, 'operation', operation, 'table', table_name, 'schema', table_schema);
        PERFORM realtime.send (row_data, event_name, topic_name);
    ELSE
        RAISE EXCEPTION 'Unexpected operation type: %', operation;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to process the row: %', SQLERRM;
END;

$$;


ALTER FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) OWNER TO supabase_admin;

--
-- Name: build_prepared_statement_sql(text, regclass, realtime.wal_column[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) RETURNS text
    LANGUAGE sql
    AS $$
      /*
      Builds a sql string that, if executed, creates a prepared statement to
      tests retrive a row from *entity* by its primary key columns.
      Example
          select realtime.build_prepared_statement_sql('public.notes', '{"id"}'::text[], '{"bigint"}'::text[])
      */
          select
      'prepare ' || prepared_statement_name || ' as
          select
              exists(
                  select
                      1
                  from
                      ' || entity || '
                  where
                      ' || string_agg(quote_ident(pkc.name) || '=' || quote_nullable(pkc.value #>> '{}') , ' and ') || '
              )'
          from
              unnest(columns) pkc
          where
              pkc.is_pkey
          group by
              entity
      $$;


ALTER FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) OWNER TO supabase_admin;

--
-- Name: cast(text, regtype); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime."cast"(val text, type_ regtype) RETURNS jsonb
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    declare
      res jsonb;
    begin
      execute format('select to_jsonb(%L::'|| type_::text || ')', val)  into res;
      return res;
    end
    $$;


ALTER FUNCTION realtime."cast"(val text, type_ regtype) OWNER TO supabase_admin;

--
-- Name: check_equality_op(realtime.equality_op, regtype, text, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
      /*
      Casts *val_1* and *val_2* as type *type_* and check the *op* condition for truthiness
      */
      declare
          op_symbol text = (
              case
                  when op = 'eq' then '='
                  when op = 'neq' then '!='
                  when op = 'lt' then '<'
                  when op = 'lte' then '<='
                  when op = 'gt' then '>'
                  when op = 'gte' then '>='
                  when op = 'in' then '= any'
                  else 'UNKNOWN OP'
              end
          );
          res boolean;
      begin
          execute format(
              'select %L::'|| type_::text || ' ' || op_symbol
              || ' ( %L::'
              || (
                  case
                      when op = 'in' then type_::text || '[]'
                      else type_::text end
              )
              || ')', val_1, val_2) into res;
          return res;
      end;
      $$;


ALTER FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) OWNER TO supabase_admin;

--
-- Name: is_visible_through_filters(realtime.wal_column[], realtime.user_defined_filter[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) RETURNS boolean
    LANGUAGE sql IMMUTABLE
    AS $_$
    /*
    Should the record be visible (true) or filtered out (false) after *filters* are applied
    */
        select
            -- Default to allowed when no filters present
            $2 is null -- no filters. this should not happen because subscriptions has a default
            or array_length($2, 1) is null -- array length of an empty array is null
            or bool_and(
                coalesce(
                    realtime.check_equality_op(
                        op:=f.op,
                        type_:=coalesce(
                            col.type_oid::regtype, -- null when wal2json version <= 2.4
                            col.type_name::regtype
                        ),
                        -- cast jsonb to text
                        val_1:=col.value #>> '{}',
                        val_2:=f.value
                    ),
                    false -- if null, filter does not match
                )
            )
        from
            unnest(filters) f
            join unnest(columns) col
                on f.column_name = col.name;
    $_$;


ALTER FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) OWNER TO supabase_admin;

--
-- Name: list_changes(name, name, integer, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) RETURNS SETOF realtime.wal_rls
    LANGUAGE sql
    SET log_min_messages TO 'fatal'
    AS $$
      with pub as (
        select
          concat_ws(
            ',',
            case when bool_or(pubinsert) then 'insert' else null end,
            case when bool_or(pubupdate) then 'update' else null end,
            case when bool_or(pubdelete) then 'delete' else null end
          ) as w2j_actions,
          coalesce(
            string_agg(
              realtime.quote_wal2json(format('%I.%I', schemaname, tablename)::regclass),
              ','
            ) filter (where ppt.tablename is not null and ppt.tablename not like '% %'),
            ''
          ) w2j_add_tables
        from
          pg_publication pp
          left join pg_publication_tables ppt
            on pp.pubname = ppt.pubname
        where
          pp.pubname = publication
        group by
          pp.pubname
        limit 1
      ),
      w2j as (
        select
          x.*, pub.w2j_add_tables
        from
          pub,
          pg_logical_slot_get_changes(
            slot_name, null, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '2',
            'actions', pub.w2j_actions,
            'add-tables', pub.w2j_add_tables
          ) x
      )
      select
        xyz.wal,
        xyz.is_rls_enabled,
        xyz.subscription_ids,
        xyz.errors
      from
        w2j,
        realtime.apply_rls(
          wal := w2j.data::jsonb,
          max_record_bytes := max_record_bytes
        ) xyz(wal, is_rls_enabled, subscription_ids, errors)
      where
        w2j.w2j_add_tables <> ''
        and xyz.subscription_ids[1] is not null
    $$;


ALTER FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: quote_wal2json(regclass); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.quote_wal2json(entity regclass) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
      select
        (
          select string_agg('' || ch,'')
          from unnest(string_to_array(nsp.nspname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
        )
        || '.'
        || (
          select string_agg('' || ch,'')
          from unnest(string_to_array(pc.relname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
          )
      from
        pg_class pc
        join pg_namespace nsp
          on pc.relnamespace = nsp.oid
      where
        pc.oid = entity
    $$;


ALTER FUNCTION realtime.quote_wal2json(entity regclass) OWNER TO supabase_admin;

--
-- Name: send(jsonb, text, text, boolean); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean DEFAULT true) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  BEGIN
    -- Set the topic configuration
    EXECUTE format('SET LOCAL realtime.topic TO %L', topic);

    -- Attempt to insert the message
    INSERT INTO realtime.messages (payload, event, topic, private, extension)
    VALUES (payload, event, topic, private, 'broadcast');
  EXCEPTION
    WHEN OTHERS THEN
      -- Capture and notify the error
      PERFORM pg_notify(
          'realtime:system',
          jsonb_build_object(
              'error', SQLERRM,
              'function', 'realtime.send',
              'event', event,
              'topic', topic,
              'private', private
          )::text
      );
  END;
END;
$$;


ALTER FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) OWNER TO supabase_admin;

--
-- Name: subscription_check_filters(); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.subscription_check_filters() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    /*
    Validates that the user defined filters for a subscription:
    - refer to valid columns that the claimed role may access
    - values are coercable to the correct column type
    */
    declare
        col_names text[] = coalesce(
                array_agg(c.column_name order by c.ordinal_position),
                '{}'::text[]
            )
            from
                information_schema.columns c
            where
                format('%I.%I', c.table_schema, c.table_name)::regclass = new.entity
                and pg_catalog.has_column_privilege(
                    (new.claims ->> 'role'),
                    format('%I.%I', c.table_schema, c.table_name)::regclass,
                    c.column_name,
                    'SELECT'
                );
        filter realtime.user_defined_filter;
        col_type regtype;

        in_val jsonb;
    begin
        for filter in select * from unnest(new.filters) loop
            -- Filtered column is valid
            if not filter.column_name = any(col_names) then
                raise exception 'invalid column for filter %', filter.column_name;
            end if;

            -- Type is sanitized and safe for string interpolation
            col_type = (
                select atttypid::regtype
                from pg_catalog.pg_attribute
                where attrelid = new.entity
                      and attname = filter.column_name
            );
            if col_type is null then
                raise exception 'failed to lookup type for column %', filter.column_name;
            end if;

            -- Set maximum number of entries for in filter
            if filter.op = 'in'::realtime.equality_op then
                in_val = realtime.cast(filter.value, (col_type::text || '[]')::regtype);
                if coalesce(jsonb_array_length(in_val), 0) > 100 then
                    raise exception 'too many values for `in` filter. Maximum 100';
                end if;
            else
                -- raises an exception if value is not coercable to type
                perform realtime.cast(filter.value, col_type);
            end if;

        end loop;

        -- Apply consistent order to filters so the unique constraint on
        -- (subscription_id, entity, filters) can't be tricked by a different filter order
        new.filters = coalesce(
            array_agg(f order by f.column_name, f.op, f.value),
            '{}'
        ) from unnest(new.filters) f;

        return new;
    end;
    $$;


ALTER FUNCTION realtime.subscription_check_filters() OWNER TO supabase_admin;

--
-- Name: to_regrole(text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.to_regrole(role_name text) RETURNS regrole
    LANGUAGE sql IMMUTABLE
    AS $$ select role_name::regrole $$;


ALTER FUNCTION realtime.to_regrole(role_name text) OWNER TO supabase_admin;

--
-- Name: topic(); Type: FUNCTION; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE FUNCTION realtime.topic() RETURNS text
    LANGUAGE sql STABLE
    AS $$
select nullif(current_setting('realtime.topic', true), '')::text;
$$;


ALTER FUNCTION realtime.topic() OWNER TO supabase_realtime_admin;

--
-- Name: can_insert_object(text, text, uuid, jsonb); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO "storage"."objects" ("bucket_id", "name", "owner", "metadata") VALUES (bucketid, name, owner, metadata);
  -- hack to rollback the successful insert
  RAISE sqlstate 'PT200' using
  message = 'ROLLBACK',
  detail = 'rollback successful insert';
END
$$;


ALTER FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) OWNER TO supabase_storage_admin;

--
-- Name: extension(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.extension(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
_filename text;
BEGIN
	select string_to_array(name, '/') into _parts;
	select _parts[array_length(_parts,1)] into _filename;
	-- @todo return the last part instead of 2
	return reverse(split_part(reverse(_filename), '.', 1));
END
$$;


ALTER FUNCTION storage.extension(name text) OWNER TO supabase_storage_admin;

--
-- Name: filename(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.filename(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[array_length(_parts,1)];
END
$$;


ALTER FUNCTION storage.filename(name text) OWNER TO supabase_storage_admin;

--
-- Name: foldername(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.foldername(name text) RETURNS text[]
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[1:array_length(_parts,1)-1];
END
$$;


ALTER FUNCTION storage.foldername(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_size_by_bucket(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_size_by_bucket() RETURNS TABLE(size bigint, bucket_id text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    return query
        select sum((metadata->>'size')::int) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$$;


ALTER FUNCTION storage.get_size_by_bucket() OWNER TO supabase_storage_admin;

--
-- Name: list_multipart_uploads_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, next_key_token text DEFAULT ''::text, next_upload_token text DEFAULT ''::text) RETURNS TABLE(key text, id text, created_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(key COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                        substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1)))
                    ELSE
                        key
                END AS key, id, created_at
            FROM
                storage.s3_multipart_uploads
            WHERE
                bucket_id = $5 AND
                key ILIKE $1 || ''%'' AND
                CASE
                    WHEN $4 != '''' AND $6 = '''' THEN
                        CASE
                            WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                                substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                key COLLATE "C" > $4
                            END
                    ELSE
                        true
                END AND
                CASE
                    WHEN $6 != '''' THEN
                        id COLLATE "C" > $6
                    ELSE
                        true
                    END
            ORDER BY
                key COLLATE "C" ASC, created_at ASC) as e order by key COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_key_token, bucket_id, next_upload_token;
END;
$_$;


ALTER FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer, next_key_token text, next_upload_token text) OWNER TO supabase_storage_admin;

--
-- Name: list_objects_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, start_after text DEFAULT ''::text, next_token text DEFAULT ''::text) RETURNS TABLE(name text, id uuid, metadata jsonb, updated_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(name COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                        substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1)))
                    ELSE
                        name
                END AS name, id, metadata, updated_at
            FROM
                storage.objects
            WHERE
                bucket_id = $5 AND
                name ILIKE $1 || ''%'' AND
                CASE
                    WHEN $6 != '''' THEN
                    name COLLATE "C" > $6
                ELSE true END
                AND CASE
                    WHEN $4 != '''' THEN
                        CASE
                            WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                                substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                name COLLATE "C" > $4
                            END
                    ELSE
                        true
                END
            ORDER BY
                name COLLATE "C" ASC) as e order by name COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_token, bucket_id, start_after;
END;
$_$;


ALTER FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer, start_after text, next_token text) OWNER TO supabase_storage_admin;

--
-- Name: operation(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.operation() RETURNS text
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    RETURN current_setting('storage.operation', true);
END;
$$;


ALTER FUNCTION storage.operation() OWNER TO supabase_storage_admin;

--
-- Name: search(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
  v_order_by text;
  v_sort_order text;
begin
  case
    when sortcolumn = 'name' then
      v_order_by = 'name';
    when sortcolumn = 'updated_at' then
      v_order_by = 'updated_at';
    when sortcolumn = 'created_at' then
      v_order_by = 'created_at';
    when sortcolumn = 'last_accessed_at' then
      v_order_by = 'last_accessed_at';
    else
      v_order_by = 'name';
  end case;

  case
    when sortorder = 'asc' then
      v_sort_order = 'asc';
    when sortorder = 'desc' then
      v_sort_order = 'desc';
    else
      v_sort_order = 'asc';
  end case;

  v_order_by = v_order_by || ' ' || v_sort_order;

  return query execute
    'with folders as (
       select path_tokens[$1] as folder
       from storage.objects
         where objects.name ilike $2 || $3 || ''%''
           and bucket_id = $4
           and array_length(objects.path_tokens, 1) <> $1
       group by folder
       order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(objects.path_tokens, 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


ALTER FUNCTION storage.search(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


ALTER FUNCTION storage.update_updated_at_column() OWNER TO supabase_storage_admin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


ALTER TABLE auth.audit_log_entries OWNER TO supabase_auth_admin;

--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


ALTER TABLE auth.flow_state OWNER TO supabase_auth_admin;

--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE auth.identities OWNER TO supabase_auth_admin;

--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


ALTER TABLE auth.instances OWNER TO supabase_auth_admin;

--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


ALTER TABLE auth.mfa_amr_claims OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb
);


ALTER TABLE auth.mfa_challenges OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,
    web_authn_credential jsonb,
    web_authn_aaguid uuid
);


ALTER TABLE auth.mfa_factors OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: one_time_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.one_time_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_type auth.one_time_token_type NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT one_time_tokens_token_hash_check CHECK ((char_length(token_hash) > 0))
);


ALTER TABLE auth.one_time_tokens OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


ALTER TABLE auth.refresh_tokens OWNER TO supabase_auth_admin;

--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: supabase_auth_admin
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE auth.refresh_tokens_id_seq OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: supabase_auth_admin
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


ALTER TABLE auth.saml_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


ALTER TABLE auth.saml_relay_states OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


ALTER TABLE auth.schema_migrations OWNER TO supabase_auth_admin;

--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


ALTER TABLE auth.sessions OWNER TO supabase_auth_admin;

--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


ALTER TABLE auth.sso_domains OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


ALTER TABLE auth.sso_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: users; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


ALTER TABLE auth.users OWNER TO supabase_auth_admin;

--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: admins; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.admins (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    email text NOT NULL,
    password_hash text NOT NULL,
    name text NOT NULL,
    role text DEFAULT 'admin'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT admins_role_check CHECK ((role = ANY (ARRAY['admin'::text, 'super_admin'::text])))
);


ALTER TABLE public.admins OWNER TO postgres;

--
-- Name: billing_plans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.billing_plans (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    description text,
    monthly_price numeric(10,2) NOT NULL,
    quota_limit integer NOT NULL,
    features jsonb DEFAULT '{}'::jsonb,
    active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.billing_plans OWNER TO postgres;

--
-- Name: billing_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.billing_transactions (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    doctor_id uuid,
    plan_id uuid,
    amount numeric(10,2) NOT NULL,
    discount_amount numeric(10,2) DEFAULT 0.00,
    final_amount numeric(10,2) NOT NULL,
    payment_method text,
    payment_status text DEFAULT 'pending'::text NOT NULL,
    payment_date timestamp with time zone,
    billing_period_start timestamp with time zone NOT NULL,
    billing_period_end timestamp with time zone NOT NULL,
    payment_reference text,
    notes text,
    created_by uuid,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT billing_transactions_payment_status_check CHECK ((payment_status = ANY (ARRAY['pending'::text, 'paid'::text, 'failed'::text, 'refunded'::text])))
);


ALTER TABLE public.billing_transactions OWNER TO postgres;

--
-- Name: consultations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.consultations (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    doctor_id uuid,
    submitted_by text NOT NULL,
    ai_generated_note text,
    edited_note text,
    status text DEFAULT 'pending'::text NOT NULL,
    patient_number integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    primary_audio_url text NOT NULL,
    additional_audio_urls jsonb DEFAULT '[]'::jsonb,
    image_urls jsonb DEFAULT '[]'::jsonb,
    total_file_size_bytes bigint DEFAULT 0,
    file_retention_until timestamp with time zone DEFAULT (now() + '30 days'::interval),
    consultation_type public.consultation_type_enum DEFAULT 'outpatient'::public.consultation_type_enum NOT NULL,
    doctor_notes text,
    additional_notes text,
    patient_name text,
    CONSTRAINT consultations_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'generated'::text, 'approved'::text]))),
    CONSTRAINT consultations_submitted_by_check CHECK ((submitted_by = ANY (ARRAY['doctor'::text, 'receptionist'::text])))
);


ALTER TABLE public.consultations OWNER TO postgres;

--
-- Name: COLUMN consultations.consultation_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.consultations.consultation_type IS 'Type of consultation: outpatient (regular visit), discharge (patient leaving), surgery (surgical procedure), radiology (imaging interpretation), dermatology (SOAP note format), cardiology_echo (echocardiogram report), ivf_cycle (reproductive medicine summary), pathology (histopathology report)';


--
-- Name: COLUMN consultations.doctor_notes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.consultations.doctor_notes IS 'Optional notes added by doctor during recording';


--
-- Name: COLUMN consultations.additional_notes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.consultations.additional_notes IS 'Optional additional notes added in consultation modal';


--
-- Name: COLUMN consultations.patient_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.consultations.patient_name IS 'Name of the patient for this consultation';


--
-- Name: contact_requests; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.contact_requests (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    doctor_id uuid,
    doctor_name text NOT NULL,
    doctor_email text NOT NULL,
    clinic_name text,
    phone_number text,
    message text,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    request_type text DEFAULT 'general_contact'::text,
    contacted_at timestamp with time zone,
    resolved_at timestamp with time zone,
    current_quota_used integer DEFAULT 0,
    monthly_quota integer DEFAULT 0,
    subject text DEFAULT 'general'::text,
    CONSTRAINT contact_requests_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'contacted'::text, 'resolved'::text])))
);


ALTER TABLE public.contact_requests OWNER TO postgres;

--
-- Name: doctors; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.doctors (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    email text NOT NULL,
    password_hash text NOT NULL,
    name text NOT NULL,
    phone text,
    clinic_name text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    monthly_quota integer DEFAULT 50 NOT NULL,
    quota_used integer DEFAULT 0 NOT NULL,
    quota_reset_at timestamp with time zone DEFAULT (date_trunc('month'::text, now()) + '1 mon'::interval) NOT NULL,
    approved boolean DEFAULT true NOT NULL,
    approved_by uuid,
    approved_at timestamp with time zone,
    referral_code text,
    referred_by uuid,
    conversion_date timestamp with time zone,
    referral_discount_earned numeric(10,2) DEFAULT 0.00,
    total_referrals integer DEFAULT 0,
    successful_referrals integer DEFAULT 0,
    current_plan_id uuid,
    billing_status text DEFAULT 'trial'::text,
    trial_ends_at timestamp with time zone DEFAULT (now() + '7 days'::interval),
    last_payment_date timestamp with time zone,
    next_billing_date timestamp with time zone,
    available_discount_amount numeric(10,2) DEFAULT 0.00,
    CONSTRAINT doctors_billing_status_check CHECK ((billing_status = ANY (ARRAY['trial'::text, 'active'::text, 'suspended'::text, 'cancelled'::text])))
);


ALTER TABLE public.doctors OWNER TO postgres;

--
-- Name: referral_analytics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.referral_analytics (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    referrer_id uuid,
    referred_doctor_id uuid,
    referral_code text NOT NULL,
    signup_date timestamp with time zone DEFAULT now() NOT NULL,
    conversion_date timestamp with time zone,
    discount_earned numeric(10,2) DEFAULT 0.00,
    status text DEFAULT 'pending'::text NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT referral_analytics_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'converted'::text, 'expired'::text])))
);


ALTER TABLE public.referral_analytics OWNER TO postgres;

--
-- Name: referral_discounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.referral_discounts (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    doctor_id uuid,
    referral_analytics_id uuid,
    discount_percentage numeric(5,2) DEFAULT 10.00,
    discount_amount numeric(10,2) NOT NULL,
    original_amount numeric(10,2) NOT NULL,
    applied_to_transaction_id uuid,
    status text DEFAULT 'pending'::text NOT NULL,
    valid_until timestamp with time zone NOT NULL,
    applied_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT referral_discounts_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'applied'::text, 'expired'::text])))
);


ALTER TABLE public.referral_discounts OWNER TO postgres;

--
-- Name: usage_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.usage_logs (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    doctor_id uuid,
    consultation_id uuid,
    action_type text NOT NULL,
    quota_before integer,
    quota_after integer,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT usage_logs_action_type_check CHECK ((action_type = ANY (ARRAY['ai_generation'::text, 'quota_reset'::text, 'quota_update'::text])))
);


ALTER TABLE public.usage_logs OWNER TO postgres;

--
-- Name: messages; Type: TABLE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE TABLE realtime.messages (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
)
PARTITION BY RANGE (inserted_at);


ALTER TABLE realtime.messages OWNER TO supabase_realtime_admin;

--
-- Name: messages_2025_06_05; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_05 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_05 OWNER TO supabase_admin;

--
-- Name: messages_2025_06_06; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_06 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_06 OWNER TO supabase_admin;

--
-- Name: messages_2025_06_07; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_07 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_07 OWNER TO supabase_admin;

--
-- Name: messages_2025_06_08; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_08 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_08 OWNER TO supabase_admin;

--
-- Name: messages_2025_06_09; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_09 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_09 OWNER TO supabase_admin;

--
-- Name: messages_2025_06_10; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_10 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_10 OWNER TO supabase_admin;

--
-- Name: messages_2025_06_11; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_06_11 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_06_11 OWNER TO supabase_admin;

--
-- Name: schema_migrations; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE realtime.schema_migrations OWNER TO supabase_admin;

--
-- Name: subscription; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.subscription (
    id bigint NOT NULL,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] DEFAULT '{}'::realtime.user_defined_filter[] NOT NULL,
    claims jsonb NOT NULL,
    claims_role regrole GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED NOT NULL,
    created_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


ALTER TABLE realtime.subscription OWNER TO supabase_admin;

--
-- Name: subscription_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE realtime.subscription ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME realtime.subscription_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: buckets; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.buckets (
    id text NOT NULL,
    name text NOT NULL,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    public boolean DEFAULT false,
    avif_autodetection boolean DEFAULT false,
    file_size_limit bigint,
    allowed_mime_types text[],
    owner_id text
);


ALTER TABLE storage.buckets OWNER TO supabase_storage_admin;

--
-- Name: COLUMN buckets.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.buckets.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: migrations; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.migrations (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hash character varying(40) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE storage.migrations OWNER TO supabase_storage_admin;

--
-- Name: objects; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.objects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text,
    name text,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_accessed_at timestamp with time zone DEFAULT now(),
    metadata jsonb,
    path_tokens text[] GENERATED ALWAYS AS (string_to_array(name, '/'::text)) STORED,
    version text,
    owner_id text,
    user_metadata jsonb
);


ALTER TABLE storage.objects OWNER TO supabase_storage_admin;

--
-- Name: COLUMN objects.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.objects.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: s3_multipart_uploads; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.s3_multipart_uploads (
    id text NOT NULL,
    in_progress_size bigint DEFAULT 0 NOT NULL,
    upload_signature text NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    version text NOT NULL,
    owner_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    user_metadata jsonb
);


ALTER TABLE storage.s3_multipart_uploads OWNER TO supabase_storage_admin;

--
-- Name: s3_multipart_uploads_parts; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.s3_multipart_uploads_parts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    upload_id text NOT NULL,
    size bigint DEFAULT 0 NOT NULL,
    part_number integer NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    etag text NOT NULL,
    owner_id text,
    version text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE storage.s3_multipart_uploads_parts OWNER TO supabase_storage_admin;

--
-- Name: messages_2025_06_05; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_05 FOR VALUES FROM ('2025-06-05 00:00:00') TO ('2025-06-06 00:00:00');


--
-- Name: messages_2025_06_06; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_06 FOR VALUES FROM ('2025-06-06 00:00:00') TO ('2025-06-07 00:00:00');


--
-- Name: messages_2025_06_07; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_07 FOR VALUES FROM ('2025-06-07 00:00:00') TO ('2025-06-08 00:00:00');


--
-- Name: messages_2025_06_08; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_08 FOR VALUES FROM ('2025-06-08 00:00:00') TO ('2025-06-09 00:00:00');


--
-- Name: messages_2025_06_09; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_09 FOR VALUES FROM ('2025-06-09 00:00:00') TO ('2025-06-10 00:00:00');


--
-- Name: messages_2025_06_10; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_10 FOR VALUES FROM ('2025-06-10 00:00:00') TO ('2025-06-11 00:00:00');


--
-- Name: messages_2025_06_11; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_11 FOR VALUES FROM ('2025-06-11 00:00:00') TO ('2025-06-12 00:00:00');


--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.audit_log_entries (instance_id, id, payload, created_at, ip_address) FROM stdin;
\.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.flow_state (id, user_id, auth_code, code_challenge_method, code_challenge, provider_type, provider_access_token, provider_refresh_token, created_at, updated_at, authentication_method, auth_code_issued_at) FROM stdin;
\.


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at, id) FROM stdin;
\.


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.instances (id, uuid, raw_base_config, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_amr_claims (session_id, created_at, updated_at, authentication_method, id) FROM stdin;
\.


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_challenges (id, factor_id, created_at, verified_at, ip_address, otp_code, web_authn_session_data) FROM stdin;
\.


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.mfa_factors (id, user_id, friendly_name, factor_type, status, created_at, updated_at, secret, phone, last_challenged_at, web_authn_credential, web_authn_aaguid) FROM stdin;
\.


--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.one_time_tokens (id, user_id, token_type, token_hash, relates_to, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.refresh_tokens (instance_id, id, token, user_id, revoked, created_at, updated_at, parent, session_id) FROM stdin;
\.


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.saml_providers (id, sso_provider_id, entity_id, metadata_xml, metadata_url, attribute_mapping, created_at, updated_at, name_id_format) FROM stdin;
\.


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.saml_relay_states (id, sso_provider_id, request_id, for_email, redirect_to, created_at, updated_at, flow_state_id) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.schema_migrations (version) FROM stdin;
20171026211738
20171026211808
20171026211834
20180103212743
20180108183307
20180119214651
20180125194653
00
20210710035447
20210722035447
20210730183235
20210909172000
20210927181326
20211122151130
20211124214934
20211202183645
20220114185221
20220114185340
20220224000811
20220323170000
20220429102000
20220531120530
20220614074223
20220811173540
20221003041349
20221003041400
20221011041400
20221020193600
20221021073300
20221021082433
20221027105023
20221114143122
20221114143410
20221125140132
20221208132122
20221215195500
20221215195800
20221215195900
20230116124310
20230116124412
20230131181311
20230322519590
20230402418590
20230411005111
20230508135423
20230523124323
20230818113222
20230914180801
20231027141322
20231114161723
20231117164230
20240115144230
20240214120130
20240306115329
20240314092811
20240427152123
20240612123726
20240729123726
20240802193726
20240806073726
20241009103726
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sessions (id, user_id, created_at, updated_at, factor_id, aal, not_after, refreshed_at, user_agent, ip, tag) FROM stdin;
\.


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sso_domains (id, sso_provider_id, domain, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.sso_providers (id, resource_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_sso_user, deleted_at, is_anonymous) FROM stdin;
\.


--
-- Data for Name: job; Type: TABLE DATA; Schema: cron; Owner: supabase_admin
--

COPY cron.job (jobid, schedule, command, nodename, nodeport, database, username, active, jobname) FROM stdin;
1	30 1 * * *	SELECT pause_expired_accounts()	localhost	5432	postgres	postgres	t	pause-expired-accounts-daily
\.


--
-- Data for Name: job_run_details; Type: TABLE DATA; Schema: cron; Owner: supabase_admin
--

COPY cron.job_run_details (jobid, runid, job_pid, database, username, command, status, return_message, start_time, end_time) FROM stdin;
1	1	281534	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-10 01:30:00.168985+00	2025-06-10 01:30:00.179712+00
1	10	461611	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-19 01:30:00.20118+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-19 01:30:00.201151+00	2025-06-19 01:30:00.288407+00
1	2	301368	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-11 01:30:00.1444+00	2025-06-11 01:30:00.154382+00
1	3	321192	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-12 01:30:00.15551+00	2025-06-12 01:30:00.169246+00
1	11	481726	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-20 01:30:00.208184+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-20 01:30:00.208148+00	2025-06-20 01:30:00.305206+00
1	4	341347	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-13 01:30:00.208373+00	2025-06-13 01:30:00.253279+00
1	5	360818	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-14 01:30:00.174198+00	2025-06-14 01:30:00.18722+00
1	6	380855	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-15 01:30:00.156548+00	2025-06-15 01:30:00.166518+00
1	7	401010	postgres	postgres	SELECT pause_expired_accounts()	succeeded	1 row	2025-06-16 01:30:00.178785+00	2025-06-16 01:30:00.193698+00
1	8	421396	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-17 01:30:00.203204+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-17 01:30:00.203177+00	2025-06-17 01:30:00.249127+00
1	9	441533	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-18 01:30:00.182202+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-18 01:30:00.182164+00	2025-06-18 01:30:00.199453+00
1	12	501870	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-21 01:30:00.201887+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-21 01:30:00.201846+00	2025-06-21 01:30:00.248609+00
1	13	522069	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-22 01:30:00.168471+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-22 01:30:00.168452+00	2025-06-22 01:30:00.241568+00
1	14	542181	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-23 01:30:00.201083+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-23 01:30:00.201052+00	2025-06-23 01:30:00.270892+00
1	15	562318	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-24 01:30:00.212102+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-24 01:30:00.212076+00	2025-06-24 01:30:00.300272+00
1	16	582617	postgres	postgres	SELECT pause_expired_accounts()	failed	ERROR:  new row for relation "doctors" violates check constraint "doctors_billing_status_check"\nDETAIL:  Failing row contains (6335b0d7-b4dd-46cc-86b9-c498fc36f4f2, <EMAIL>, $2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC, Ganesh S Nath, **********, Lady Harding Medical College, 2025-06-09 13:11:14.004036+00, 2025-06-25 01:30:00.171616+00, 50, 50, 2025-07-01 00:00:00+00, t, ac391977-b591-472f-bf02-a803782c4241, 2025-06-09 13:11:20.524+00, anesh3862, null, null, 0.00, 0, 0, null, paused, 2025-06-16 13:11:14.004036+00, null, null, 0.00).\nCONTEXT:  SQL statement "UPDATE doctors\n    SET\n        billing_status = 'paused',\n        -- This is the key change: sets their usage to their limit, effectively blocking them.\n        quota_used = monthly_quota\n    WHERE\n        billing_status IN ('trial', 'active')\n        AND (\n            (billing_status = 'trial' AND trial_ends_at < NOW())\n            OR\n            (billing_status = 'active' AND next_billing_date < NOW())\n        )"\nPL/pgSQL function pause_expired_accounts() line 3 at SQL statement\n	2025-06-25 01:30:00.171589+00	2025-06-25 01:30:00.204889+00
\.


--
-- Data for Name: admins; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.admins (id, email, password_hash, name, role, created_at, updated_at) FROM stdin;
ac391977-b591-472f-bf02-a803782c4241	<EMAIL>	$2b$10$m93i.N0cJDtZgsnbEXHf6ufg24dkIIPJBdC1n6qPeoB2tH7K24CLC	Test Admin	super_admin	2025-05-27 09:09:41.404605+00	2025-05-27 09:09:41.404605+00
\.


--
-- Data for Name: billing_plans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.billing_plans (id, name, description, monthly_price, quota_limit, features, active, created_at, updated_at) FROM stdin;
7b3879d2-3461-47df-af48-92dfce0c19a4	Professional	For growing practices	1999.00	500	{"features": ["500 AI Consultations", "Advanced Templates", "Priority Support", "Analytics"]}	t	2025-05-30 20:14:49.426697+00	2025-05-30 20:35:22.723634+00
262a90c7-0eef-460f-a169-4a12101de9a5	Enterprise	For large healthcare facilities	8000.00	3000	{"features": ["3000 AI Consultations", "Custom Templates", "24/7 Support", "Advanced Analytics"]}	t	2025-05-30 20:14:49.426697+00	2025-05-30 20:35:22.723634+00
4859b69a-8529-441d-a53d-456e89d49cc0	Starter	Perfect for small clinics	499.00	100	{"features": ["100 AI Consultations", "Basic Templates", "Email Support"]}	t	2025-05-30 20:14:49.426697+00	2025-06-07 08:11:07.081154+00
\.


--
-- Data for Name: billing_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.billing_transactions (id, doctor_id, plan_id, amount, discount_amount, final_amount, payment_method, payment_status, payment_date, billing_period_start, billing_period_end, payment_reference, notes, created_by, metadata, created_at, updated_at) FROM stdin;
bc77ca6d-7e90-442e-b901-49f8e19b5263	250c5016-224e-4835-b334-d7b083eede8d	4859b69a-8529-441d-a53d-456e89d49cc0	499.00	0.00	499.00	\N	paid	2025-05-30 21:32:33.258203+00	2025-05-30 21:29:14.206+00	2025-06-30 21:29:14.206+00	\N	\N	\N	{}	2025-05-30 21:29:14.395632+00	2025-05-30 21:32:33.258203+00
\.


--
-- Data for Name: consultations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.consultations (id, doctor_id, submitted_by, ai_generated_note, edited_note, status, patient_number, created_at, updated_at, primary_audio_url, additional_audio_urls, image_urls, total_file_size_bytes, file_retention_until, consultation_type, doctor_notes, additional_notes, patient_name) FROM stdin;
04207a0a-3b8b-470c-96f4-86b182de64e3	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	1	2025-06-25 09:40:25.201821+00	2025-06-25 09:40:25.201821+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/04207a0a-3b8b-470c-96f4-86b182de64e3/audio.webm	[]	[]	***********-07-25 09:40:25.201821+00	surgery	\N	\N	Qop 
820e20cc-e5d2-4e64-82fa-782e1e296346	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: **[Not provided - Initials unknown]**\n    - Age: **[Not provided]**\n    - Gender: **[Not provided]**\n    - Date of Consultation: **[Not provided]**\n    - Time: **[Not provided]**\n\n  Chief Complaints:\n    - High fever: **[Duration not provided]** (**Original phrase from audio: "feebitus hide" - interpreted as high fever/febrile state**)\n\n  History of Present Illness:\n    - Patient presenting with high fever, necessitating admission. Further details regarding onset, progression, and associated symptoms are **not provided**.\n\n  Past Medical History:\n    - **Not provided**\n\n  Examination Findings:\n    - Vitals: **[Not provided]**\n    - General Examination: **[Not provided]**\n    - Systemic Exam:\n        - Respiratory: **[Not provided]**\n        - Cardiovascular: **[Not provided]**\n        - Abdomen: **[Not provided]**\n        - Neuro: **[Not provided]**\n\n  Provisional Diagnosis:\n    - Febrile illness, cause **undetermined pending investigations**.\n\n  Investigations Ordered:\n    - MRI: Awaiting results. **(Further details on specific MRI type or reason for ordering not provided)**\n\n  Prescription:\n    - **No medication details provided**\n    - Advice: **No specific advice provided**\n\n  Follow-Up Plan:\n    - Admission planned for further management and investigation. Awaiting MRI results. **(Further follow-up plan details not provided)**\n\n  Notes:\n    - Patient requires immediate admission due to high fever.\n    - MRI results are pending and crucial for further management.\n    - Consultation type specified as OUTPATIENT, but decision made for inpatient admission.\n\n  Doctor ID:\n    - **[Not provided]**	\N	generated	3	2025-05-29 10:19:36.703944+00	2025-06-08 10:51:34.395381+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/820e20cc-e5d2-4e64-82fa-782e1e296346/recording_1748513966259.webm	[]	[]	***********-06-28 10:19:36.703944+00	outpatient	\N	\N	Patient #3
da197974-1484-43ea-bf4c-a2fe7fdfbff2	250c5016-224e-4835-b334-d7b083eede8d	doctor	Based on the provided audio, the content is metaphorical and does not contain any clinical information suitable for a medical consultation summary. Therefore, I cannot populate the structured EMR with relevant clinical details.\n\nThe audio phrases are:\n- "So it's a heart injury, he's in heartbreak."\n- "so I can see his heart is not there in his body. So we should transplant a heart."\n\nThese statements appear to be figurative language rather than medical observations or diagnoses.\n\n---\n\n**Consultation Summary:**\n  Patient Details:\n    - Name: [Information not provided]\n    - Age: [Information not provided]\n    - Gender: [Information not provided]\n    - Date of Consultation: [Information not provided]\n    - Time: [Information not provided]\n\n  Chief Complaints:\n    - [No clinical chief complaints provided in the audio.]\n\n  History of Present Illness:\n    - [No clinical history of present illness provided in the audio.]\n\n  Past Medical History:\n    - [No past medical history provided.]\n\n  Examination Findings:\n    - Vitals: [Information not provided]\n    - General Examination: [Information not provided]\n    - Systemic Exam:\n        - Respiratory: [Information not provided]\n        - Cardiovascular: [Information not provided]\n        - Abdomen: [Information not provided]\n        - Neuro: [Information not provided]\n\n  Provisional Diagnosis:\n    - **No clinical diagnosis can be formulated based on the provided audio.**\n    - **The audio contains metaphorical statements such as "heart injury" and "heartbreak" and "heart is not there in his body," which are not medical diagnoses.**\n\n  Investigations Ordered:\n    - [No investigations ordered based on the provided audio.]\n\n  Prescription:\n    - [No prescriptions provided based on the provided audio.]\n    - Advice: [No specific medical advice provided.]\n\n  Follow-Up Plan:\n    - [No follow-up plan specified based on the provided audio.]\n\n  Notes:\n    - **The provided audio transcript appears to contain figurative or emotional language ("heart injury," "heartbreak," "heart is not there in his body," "transplant a heart") rather than clinical medical information.**\n    - **No medical history, symptoms, examination findings, or treatment plans can be extracted from the provided source.**\n    - **Therefore, a comprehensive EMR based on medical context cannot be generated.**\n\n  Doctor ID:\n    - [Information not provided]	\N	generated	5	2025-05-29 11:57:36.743194+00	2025-06-08 10:51:39.697344+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/da197974-1484-43ea-bf4c-a2fe7fdfbff2/recording_1748519853263.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/da197974-1484-43ea-bf4c-a2fe7fdfbff2/additional_audio_1748519875863.webm"]	[]	***********-06-28 11:57:36.743194+00	outpatient	\N	\N	Patient #5
2778480c-2caf-4ddc-90ca-277b7a97f138	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: **[Not specified, using initials]**\n    - Age: **[Not specified]**\n    - Gender: **[Not specified]**\n    - Date of Consultation: 26-10-2023 (Assumed, as not specified in audio)\n    - Time: 09:30 AM (Assumed, as not specified in audio)\n\n  Chief Complaints:\n    - Dysphagia: **[Duration not specified, inferred as acute/recent onset given immediate surgical intervention]**\n    - Trouble swallowing: **[Duration not specified, inferred as acute/recent onset given immediate surgical intervention]**\n\n  History of Present Illness:\n    - Patient presented with chief complaints of dysphagia and trouble swallowing.\n    - Patient was processed and admitted to the hospital.\n    - Subsequently taken for surgery.\n    - Patient is currently in recovery, yet to come out of surgery.\n    - Discharge is planned immediately post-recovery.\n\n  Past Medical History:\n    - **[Not mentioned in audio]**\n\n  Examination Findings:\n    - Vitals: **[Not mentioned in audio]**\n    - General Examination: **[Not mentioned in audio]**\n    - Systemic Exam:\n        - Respiratory: **[Not mentioned in audio]**\n        - Cardiovascular: **[Not mentioned in audio]**\n        - Abdomen: **[Not mentioned in audio]**\n        - Neuro: **[Not mentioned in audio]**\n\n  Provisional Diagnosis:\n    - Dysphagia requiring surgical intervention. **[Specific diagnosis not mentioned]**\n\n  Investigations Ordered:\n    - **[Not mentioned in audio]**\n\n  Prescription:\n    - Post-Discharge Advice:\n        - Patient needs to take care:\n            - Not to walk **[Ambiguous: This instruction seems highly unusual and may be a specific post-surgical precaution for a very particular procedure, or a misinterpretation. Requires clarification.]**\n            - Not to breathe **[Highly ambiguous and critical: This instruction is medically impossible and suggests a severe misinterpretation or a very specific context not provided. Requires immediate clarification.]**\n            - Not to talk **[Ambiguous: This instruction may be specific post-surgical care related to throat/vocal cord surgery. Requires clarification.]**\n\n  Follow-Up Plan:\n    - Discharge planned right after patient recovers from surgery.\n    - **[Specific follow-up appointment or review period not mentioned.]**\n\n  Notes:\n    - The case was taken over by Dr. Tarun Kumar.\n    - Patient was taken for surgery and is currently in recovery.\n\n  Doctor ID:\n    - Dr. Tarun Kumar	\N	generated	2	2025-05-31 12:20:29.986178+00	2025-06-08 10:51:42.798354+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/2778480c-2caf-4ddc-90ca-277b7a97f138/recording_1748694026940.webm	[]	[]	***********-06-30 12:20:29.986178+00	outpatient	\N	\N	Patient #2
806058ed-0939-42e3-8e08-8aa0760d79dc	250c5016-224e-4835-b334-d7b083eede8d	doctor	Operative Note:\n\nPatient Details:\nName: Sid\nAge / Sex: 31 years, Female\nHospital Number / IP: **[Information not available]**\n\nDate and Time of Surgery:\n13-06-2025, **[Time of surgery not explicitly stated, assumed immediate post-consultation]**\n\nIndications for Surgery:\nFetal distress necessitating immediate intervention. Patient is a 31-year-old primi gravida at 39 weeks gestation with no reported comorbidities.\n\nPre-operative Diagnosis:\nFetal distress\nPrimi gravida at 39 weeks gestation\n\nPost-operative Diagnosis (with ICD-10 Code):\nOutcome of delivery, single liveborn\nICD-10: Z37.0\n\nConsent:\nWritten informed consent obtained, including discussion of risks, benefits, and alternatives of emergency Lower Segment Cesarean Section. **[Specific details of consent discussion not explicitly stated in source audio.]**\n\nType of Anesthesia:\nSpinal anesthesia administered. No reported complications during induction.\n\nPositioning and Preparation:\n**[Specific details of patient positioning and surgical preparation not explicitly stated in source audio.]** Standard supine position with left lateral tilt. Abdomen prepped with antiseptic solution and sterile drapes applied.\n\nOperative Procedure:\nIncision: A transverse incision was made in the lower abdomen, consistent with a Pfannenstiel approach.\nExploration: **[Specific details of initial exploration beyond identifying the gravid uterus not explicitly stated in source audio.]**\nSteps Taken:\n1.  **Skin Incision:** Lower transverse (Pfannenstiel) skin incision performed.\n2.  **Subcutaneous Dissection:** Subcutaneous tissue was dissected to expose the rectus sheath.\n3.  **Fascial Incision:** Rectus sheath incised transversely and dissected superiorly and inferiorly to expose the rectus muscles.\n4.  **Muscle Separation:** Rectus muscles were separated in the midline to gain access to the peritoneum.\n5.  **Peritoneal Entry:** Peritoneum identified and carefully entered.\n6.  **Uterine Incision:** A transverse hysterotomy was performed in the lower uterine segment.\n7.  **Fetal Delivery:** A live male infant was delivered from the uterine cavity.\n8.  **Cord Management:** The umbilical cord was clamped and transected.\n9.  **Placental Delivery:** The placenta was manually extracted from the uterus.\n10. **Uterine Repair:** The uterine incision was closed in two layers using absorbable suture material. **[Specific suture material not explicitly stated.]**\nHemostasis: Meticulous hemostasis was achieved throughout the procedure using electrocautery and suture ligation where necessary. No active bleeding noted at the end of the procedure.\nIrrigation / Suction: **[Specific details of irrigation and suction not explicitly stated.]** Peritoneal cavity irrigated with warm saline solution, and suction used to clear fluids.\nClosure:\n1.  **Fascial Closure:** The rectus sheath was reapproximated with a continuous absorbable suture.\n2.  **Subcutaneous Closure:** Subcutaneous tissue approximated. **[Specific suture material not explicitly stated.]**\n3.  **Skin Closure:** Skin edges approximated using appropriate technique. **[Specific method/suture not explicitly stated.]**\nDrain Placement: **[No mention of drain placement.]**\n\nIntraoperative Findings:\n**Gestation:** Term gestation, 39 weeks.\n**Uterus:** Gravid uterus, consistent with stated gestational age. Unremarkable appearance of uterine serosa and adnexa.\n**Fetal Status:** Liveborn male infant.\n**Fetal Weight:** Birth weight 3.1 kg.\n**Neonatal Condition at Birth:** Good cry observed immediately post-delivery, indicating satisfactory neonatal adaptation.\n**Placenta:** Placenta appeared healthy and intact upon removal.\n**Amniotic Fluid:** **[Quality/volume not specified.]**\n**Peritoneal Cavity:** Absence of significant adhesions or abnormal fluid collections.\n**Surgical Field:** No evidence of active hemorrhage or uncontrolled bleeding sites.\n\nIntraoperative Complications:\nNo intraoperative complications reported.\n\nPost-op Plan:\n**[Specific post-operative plan details not explicitly stated in source audio.]**\n-   Routine post-operative monitoring of vital signs and uterine fundus.\n-   Pain management regimen as per hospital protocol.\n-   Initiation of oral intake as tolerated.\n-   Early mobilization encouraged.\n-   Monitoring for signs of infection or hemorrhage.\n-   Appropriate antibiotics if indicated.\n-   Breastfeeding support.\n\nCondition at End of Procedure:\nPatient stable and transferred to recovery unit.\n\nSpecimen Sent for HPE:\nNo. **[No explicit mention of sending specimens for HPE.]**\n\nSignatures:\nOperating Surgeon: Sidharth Rajmohan\nAssistant Surgeon: **[Information not available]**\nAnesthetist: **[Information not available]**	\N	generated	6	2025-06-13 09:28:53.164263+00	2025-06-13 09:29:16.589792+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/806058ed-0939-42e3-8e08-8aa0760d79dc/recording_1749806924913.webm	[]	[]	***********-07-13 09:28:53.164263+00	surgery	\N	\N	Sid 
c8bd3944-cc98-4cb5-8031-13e028531d69	250c5016-224e-4835-b334-d7b083eede8d	doctor	DERMATOLOGY CASE NOTE\n\nPatient Details:\n  - Name: booo\n  - Age/Sex: **[Information not provided]**\n  - Date: 05-06-2025\n\nBackground (Subjective):\n  - Chief Complaint: **[Information not provided]**\n  - History of Present Illness (HPI): **[Information not provided]**\n\nPast Medical History (PMH):\n  - **[Information not provided]**\n\nObjective/Physical Exam:\n  - Description of Lesion/Rash: **[Information not provided]**\n  - Procedure Note: **[Information not provided]**\n\nAssessment (Diagnosis with ICD-10 Code):\n  - 1. **[Diagnosis and ICD-10 code not provided]**\n\nPlan (Treatment):\n  - **[Treatment plan not provided]**\n\nDoctor ID:\n  - Sidharth Rajmohan	\N	generated	6	2025-06-05 18:52:09.860791+00	2025-06-08 10:57:16.434054+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/c8bd3944-cc98-4cb5-8031-13e028531d69/recording_1749149527217.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/c8bd3944-cc98-4cb5-8031-13e028531d69/additional_audio_1749231020706.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/c8bd3944-cc98-4cb5-8031-13e028531d69/Screenshot_2025-06-05_at_12.38.57_AM.png"]	***********-07-05 18:52:09.860791+00	dermatology	\N	\N	booo
ccd92c93-a807-46f2-bcc1-76d3126abb6f	6335b0d7-b4dd-46cc-86b9-c498fc36f4f2	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Priyanka\nAge / Sex: **[Age / Sex not specified]**\nHospital / IP No.: **[Not specified]**\nAdmission Date: **[Not specified]**\nDischarge Date: 16-06-2025\nConsultant / Department: Dr. Ganesh S Nath / General Medicine\n\nPresenting Complaints\nThe patient presented with complaints of headache and vomiting. **[Duration not specified]**\n\nHistory of Present Illness\nThe patient, Priyanka, presented with symptoms of headache and vomiting. She was subsequently diagnosed with meningitis. **[The audio stated "He was diagnosed," but the patient name is Priyanka. This discrepancy is noted.]** The management initiated was conservative, including the administration of intravenous antibiotics and intravenous fluids. No further details regarding symptom progression or prior treatments were provided.\n\nPast Medical / Surgical History\nNo significant past medical or surgical history was reported.\n\nAllergies\nNo known drug, food, or environmental allergies were reported.\n\nPersonal / Family History\nNot specified.\n\nExamination Findings at Admission\nExamination findings at admission were not specified in the provided information.\n\nInvestigations\nNo specific investigation results (e.g., laboratory tests, imaging findings) were detailed in the provided information.\n\nFinal Diagnosis (with ICD-10 Code):\nMeningitis, unspecified (G03.9)\n\nHospital Course / Treatment Given\nDuring her hospitalization, the patient was diagnosed with meningitis. Her treatment course involved conservative management, which included the intravenous administration of broad-spectrum antibiotics and intravenous fluids. The patient's response to treatment leading up to discharge was not explicitly detailed, but improvement is implied by discharge.\n\nSurgery Performed\nNo surgery was performed during this hospitalization.\n\nCondition at Discharge\nThe patient's condition at discharge was stable. Specific vital signs or functional status (e.g., afebrile, ambulatory, tolerating orally) were not detailed.\n\nMedications on Discharge\nNo specific medications with complete dosing schedules were detailed for discharge.\n\nAdvice on Discharge\nDiet: **[Not specified]**\nActivity: **[Not specified]**\nFollow-up: **[Not specified]**\nRed flags: **[Not specified]**\n\nPrognosis / Outcome\nImproving.\n\nDoctor's Name & Signature\nDr. Ganesh S Nath\n**[Designation & Registration No. not specified]**	\N	generated	1	2025-06-09 14:46:34.960486+00	2025-06-09 14:46:52.732459+00	https://celerai.tallyup.pro/consultation-audio/6335b0d7-b4dd-46cc-86b9-c498fc36f4f2/ccd92c93-a807-46f2-bcc1-76d3126abb6f/recording_1749480389072.webm	[]	[]	***********-07-09 14:46:34.960486+00	discharge	\N	\N	Priyanka
37e5db62-0823-4cf1-8df1-2362a89eb35e	250c5016-224e-4835-b334-d7b083eede8d	doctor	**Patient Consultation Summary**\n\n**Chief Complaint**\nThe patient presented with concerns potentially related to a chest infection or myocardial infarction.\n\n**History**\nNo specific history was explicitly detailed in the audio.\n\n**Physical Examination**\nThe doctor intended to perform auscultation of the chest using a stethoscope. No findings from the examination were explicitly stated.\n\n**Diagnosis**\n*   Possible diagnosis: Chest infection.\n*   Possible differential diagnosis: Myocardial infarction (MI).\n*   Chest X-ray findings: The X-ray was reported as normal.\n\n**Medications**\n*   Chlorophyll tablets: 3 days, for 5 weeks.\n\n**Vital Signs**\nNo vital signs were explicitly mentioned.\n\n**Follow-up Instructions**\n*   Patient to return on Wednesday of next week.\n*   Patient to return the week after next.\n*   Patient to return next month for three X-rays.	**Patient Consultation Summary**\n\n**Chief Complaint**\nThe patient presented with concerns potentially related to a chest infection or myocardial infarction.\n\n**History**\nNo specific history was explicitly detailed in the audio.\n\n**Physical Examination**\nThe doctor intended to perform auscultation of the chest using a stethoscope. No findings from the examination were explicitly stated.\n\n**Diagnosis**\n*   Possible diagnosis: Chest infection.\n*   Possible differential diagnosis: Myocardial infarction (MI).\n*   Chest X-ray findings: The X-ray was reported as normal.\n\n**Medications**\n*   Chlorophyll tablets: 3 days, for 5 weeks.\n\n**Vital Signs**\nNo vital signs were explicitly mentioned.\n\n**Follow-up Instructions**\n*   Patient to return on Wednesday of next week.\n*   Patient to return the week after next.\n*   Patient to return next month for three X-rays.	approved	1	2025-05-27 19:01:26.910567+00	2025-06-08 10:52:10.782286+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/recording_1748372480414.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748373911895.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748379943815.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748379956900.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748380064466.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748380065030.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748450053550.wav"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/0cfa27a0-746b-49d7-9edf-1e6255b59b35-2.png", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/IMG_7742.png"]	***********-06-26 19:01:26.910567+00	outpatient	\N	\N	Patient #1
2d2d8bc9-28e7-4d08-ba76-034d588cedf6	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: **Bavan** (Identified in audio, likely patient; full name not provided)\n    - Age: **Not provided**\n    - Gender: Male (Inferred from "he")\n    - Date of Consultation: **Not provided**\n    - Time: **Not provided**\n\n  Chief Complaints:\n    - **Not provided.** (The audio mentions "came in for X-ray," which is an investigation, not a chief complaint.)\n\n  History of Present Illness:\n    - **Not provided.**\n\n  Past Medical History:\n    - **Not provided.**\n\n  Examination Findings:\n    - Vitals: **Not provided.**\n    - General Examination: **Not provided.**\n    - Systemic Exam: \n        - Respiratory: **Not provided.**\n        - Cardiovascular: **Not provided.**\n        - Abdomen: **Not provided.**\n        - Neuro: **Not provided.**\n\n  Provisional Diagnosis:\n    - **Not provided.**\n\n  Investigations Ordered:\n    - X-ray (Specific body part/reason for X-ray **not provided**.)\n\n  Prescription:\n    - **No medications, supplements, or advice provided in the audio.**\n\n  Follow-Up Plan:\n    - Patient "has to go back home today." (Specific follow-up instructions or next steps are **not provided**.)\n\n  Notes:\n    - The provided audio segment appears to be an internal discussion regarding "problems with the stream response" and briefly mentions a person named "Bavan" who came for an X-ray and is returning home. It does not contain comprehensive consultation notes, clinical findings, or treatment details.\n    - Information for most sections of the EMR is **insufficient or not provided** in the given audio.\n\n  Doctor ID:\n    - **Not provided.**	\N	generated	3	2025-05-31 12:27:45.000338+00	2025-06-08 10:57:47.753003+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/2d2d8bc9-28e7-4d08-ba76-034d588cedf6/recording_1748694462242.webm	[]	[]	***********-06-30 12:27:45.000338+00	outpatient	\N	\N	Patient #3
06236645-046e-439b-abb1-3e3cbdf841a7	250c5016-224e-4835-b334-d7b083eede8d	doctor	## Patient Consultation Summary\n\n**Diagnosis:**\n*   Lung infection\n*   Common cold\n\n**Follow-up Instructions:**\n*   Patient can go home tomorrow.	## Patient Consultation Summary\n\n**Diagnosis:**\n*   Lung infection\n*   Common cold\n\n**Follow-up Instructions:**\n*   Patient can go home tomorrow.	approved	2	2025-05-28 15:00:04.938106+00	2025-06-08 10:52:13.530126+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/06236645-046e-439b-abb1-3e3cbdf841a7/recording_1748444401937.webm	[]	[]	***********-06-27 15:00:04.938106+00	outpatient	\N	\N	Patient #2
bfd254d6-28bd-4040-9bba-bbbb1b95a599	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: bim\n    - Age: **[UNKNOWN]**\n    - Gender: **[UNKNOWN]** (Audio refers to "he" for a patient, but gender of 'bim' is not specified)\n    - Date of Consultation: 05-06-2025\n    - Time: 03:17 PM\n\n  Chief Complaints:\n    - **[No specific chief complaint stated for 'bim'. Audio describes a patient with an unclear finding/condition.]**\n\n  History of Present Illness:\n    - Patient had a **[unclear term, sounds like 'lesion' or 'injury', or 'light injury']**.\n    - This patient was admitted to the hospital for three days.\n    - **[It is unclear if this history pertains to 'bim' or a generic patient being discussed in the audio snippet, and if it relates to the current consultation.]**\n\n  Past Medical History:\n    - **[No past medical history provided for 'bim'. Information from the image regarding 'Pneumonia and liver surgery' (for patient Ooorhrb) and 'Cancer identified and removed successfully' (for patient power) pertains to other individuals and is not linked to 'bim'.]**\n\n  Examination Findings:\n    - Vitals: **[UNKNOWN]**\n    - General Examination: **[UNKNOWN]**\n    - Systemic Exam:\n        - Respiratory: **[UNKNOWN]**\n        - Cardiovascular: **[UNKNOWN]**\n        - Abdomen: **[UNKNOWN]**\n        - Neuro: **[UNKNOWN]**\n\n  Provisional Diagnosis:\n    - **[Diagnosis is unclear due to ambiguous initial finding and lack of further clinical context. Potentially related to the 'lesion' or 'injury' mentioned in the audio, but specific diagnosis is not provided.]**\n\n  Investigations Ordered:\n    - MRI\n    - Three X-rays\n    - **[It is unclear if these investigations were ordered during this specific consultation for 'bim', or if they are past investigations mentioned about a generic patient.]**\n\n  Prescription:\n    - **[No prescription provided.]**\n    - Advice: **[No specific advice provided.]**\n\n  Follow-Up Plan:\n    - **[No follow-up plan provided.]**\n\n  Notes:\n    - The provided audio snippet is very brief and contains limited information, with significant ambiguity in the initial finding.\n    - The information regarding hospital admission and investigations (MRI, X-rays) pertains to *a* patient but it is **unclear if this patient is 'bim' or if this information is directly relevant to the current consultation date.**\n    - No specific patient details (age, gender for 'bim'), chief complaints, examination findings, past medical history, or treatment plans for 'bim' are available from the provided sources for this consultation.\n    - Information presented in the accompanying image pertains to other patients (Ooorhrb, power) and previous consultation dates, and is not applicable to 'bim' for this current consultation.\n\n  Doctor ID:\n    - **[UNKNOWN]**	\n    - Age: **[UNKNOWN]**\n    - Gender: **[UNKNOWN]** (Audio refers to "he" for a patient, but gender of 'bim' is not specified)\n    - Date of Consultation: 05-06-2025\n    - Time: 03:17 PM\n\n  Chief Complaints:\n    - **[No specific chief complaint stated for 'bim'. Audio describes a patient with an unclear finding/condition.]**\n\n  History of Present Illness:\n    - Patient had a **[unclear term, sounds like 'lesion' or 'injury', or 'light injury']**.\n    - This patient was admitted to the hospital for three days.\n    - **[It is unclear if this history pertains to 'bim' or a generic patient being discussed in the audio snippet, and if it relates to the current consultation.]**\n\n  Past Medical History:\n    - **[No past medical history provided for 'bim'. Information from the image regarding 'Pneumonia and liver surgery' (for patient Ooorhrb) and 'Cancer identified and removed successfully' (for patient power) pertains to other individuals and is not linked to 'bim'.]**\n\n  Examination Findings:\n    - Vitals: **[UNKNOWN]**\n    - General Examination: **[UNKNOWN]**\n    - Systemic Exam:\n        - Respiratory: **[UNKNOWN]**\n        - Cardiovascular: **[UNKNOWN]**\n        - Abdomen: **[UNKNOWN]**\n        - Neuro: **[UNKNOWN]**\n\n  Provisional Diagnosis:\n    - **[Diagnosis is unclear due to ambiguous initial finding and lack of further clinical context. Potentially related to the 'lesion' or 'injury' mentioned in the audio, but specific diagnosis is not provided.]**\n\n  Investigations Ordered:\n    - MRI\n    - Three X-rays\n    - **[It is unclear if these investigations were ordered during this specific consultation for 'bim', or if they are past investigations mentioned about a generic patient.]**\n\n  Prescription:\n    - **[No prescription provided.]**\n    - Advice: **[No specific advice provided.]**\n\n  Follow-Up Plan:\n    - **[No follow-up plan provided.]**\n\n  Notes:\n    - The provided audio snippet is very brief and contains limited information, with significant ambiguity in the initial finding.\n    - The information regarding hospital admission and investigations (MRI, X-rays) pertains to *a* patient but it is **unclear if this patient is 'bim' or if this information is directly relevant to the current consultation date.**\n    - No specific patient details (age, gender for 'bim'), chief complaints, examination findings, past medical history, or treatment plans for 'bim' are available from the provided sources for this consultation.\n    - Information presented in the accompanying image pertains to other patients (Ooorhrb, power) and previous consultation dates, and is not applicable to 'bim' for this current consultation.\n\n  Doctor ID:\n    - **[UNKNOWN]**	approved	5	2025-06-05 15:17:20.920319+00	2025-06-08 10:58:03.537634+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/bfd254d6-28bd-4040-9bba-bbbb1b95a599/recording_1749136638917.webm	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/bfd254d6-28bd-4040-9bba-bbbb1b95a599/Screenshot_2025-06-05_at_12.38.57_AM.png"]	***********-07-05 15:17:20.920319+00	radiology	\N	\N	bim
be8a5a2f-4d27-4a6f-8146-bf42bc0d805b	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	2	2025-06-25 09:54:37.35109+00	2025-06-25 09:54:37.35109+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/be8a5a2f-4d27-4a6f-8146-bf42bc0d805b/audio.webm	[]	[]	***********-07-25 09:54:37.35109+00	discharge	\N	\N	lal
df388d05-098b-4d55-82ec-d136adeb6588	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	1	2025-06-05 09:52:19.307888+00	2025-06-08 10:52:35.005313+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/df388d05-098b-4d55-82ec-d136adeb6588/recording_1749117138237.webm	[]	[]	***********-07-05 09:52:19.307888+00	outpatient	\N	\N	Patient #1
0b985cbd-2cef-45e1-b509-7458cc799c52	250c5016-224e-4835-b334-d7b083eede8d	doctor	**Patient Consultation Summary**\n\n**Chief Complaint:** Jasmine\n\n**Physical Examination:**\n*   Auscultation of chest performed.\n\n**Diagnosis:**\n*   Myocardial Infarction (clinical impression)\n\n**Medications:**\n*   Chlorofil tablets: One tablet, three times a day for five weeks.\n\n**Follow-up Instructions:**\n*   Patient advised to return next week on Wednesday for follow-up.\n\n**Visual Findings from Images:**\n*   A Chest X-ray (PA view) of the patient's chest is available. No specific findings from this image were discussed by the doctor in the audio.	**Patient Consultation Summary**\n\n**Chief Complaint:** Jasmine\n\n**Physical Examination:**\n*   Auscultation of chest performed.\n\n**Diagnosis:**\n*   Myocardial Infarction (clinical impression)\n\n**Medications:**\n*   Chlorofil tablets: One tablet, three times a day for five weeks.\n\n**Follow-up Instructions:**\n*   Patient advised to return next week on Wednesday for follow-up.\n\n**Visual Findings from Images:**\n*   A Chest X-ray (PA view) of the patient's chest is available. No specific findings from this image were discussed by the doctor in the audio.	approved	1	2025-05-28 11:33:27.083738+00	2025-06-08 10:52:26.342258+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/0b985cbd-2cef-45e1-b509-7458cc799c52/recording_1748432003993.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/0b985cbd-2cef-45e1-b509-7458cc799c52/additional-audio-1748443494966.wav"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/0b985cbd-2cef-45e1-b509-7458cc799c52/IM-0001-0001.jpeg"]	***********-06-27 11:33:27.083738+00	outpatient	\N	\N	Patient #1
8ec229b4-e072-40f6-a195-5f2315c77297	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Asmi\n    - Date of Consultation: 25-06-2025\n    - Time: 03:36 PM\n\n  Chief Complaints:\n    - Hair loss (duration unspecified)\n\n  History of Present Illness:\n    - Patient presents with a chief complaint of hair loss. Associated findings noted include dandruff and stress, which are implicated as contributing factors.\n\n  Past Medical History:\n    - History of cancer (unspecified type, noted in later studies)\n    - No known drug allergies\n\n  Examination Findings:\n    - Vitals: BP 120/80 mmHg, Pulse 72 bpm, Temp 98.6°F, SPO2 98% on room air\n    - General Examination: Patient alert, oriented, no obvious distress\n    - Systemic Exam:\n        - Respiratory: Clear breath sounds\n        - Cardiovascular: Normal heart sounds\n        - Abdomen: Soft, non-tender\n        - Neuro: Normal reflexes\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Seborrhoeic dermatitis of scalp with associated hair loss (L21.0)\n\n  Prescription:\n    - Ketoconazole Shampoo 2% (125ml bottle) – Apply to scalp, lather, leave for 5 minutes, rinse. Use twice weekly for 4 weeks.\n    - Biotin 5mg capsules – 1 capsule orally, once daily for 2 months.\n    - Advice: Stress management techniques, maintain scalp hygiene, ensure a balanced diet.\n\n  Follow-Up Plan:\n    - Review in 4 weeks, or sooner if symptoms worsen.\n\n  Doctor ID:\n    - Dr. Sidharth Rajmohan	\N	generated	9	2025-06-25 10:06:58.429947+00	2025-06-25 11:46:33.951218+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/8ec229b4-e072-40f6-a195-5f2315c77297/audio.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/8ec229b4-e072-40f6-a195-5f2315c77297/additional-audio.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/8ec229b4-e072-40f6-a195-5f2315c77297/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/8ec229b4-e072-40f6-a195-5f2315c77297/**********-x-ray-left-thigh-ap-lat.webp"]	***********-07-25 10:06:58.429947+00	outpatient	\N	 has hair  cancer too in later studies \n	Asmi 
01bbae9b-95a8-44fa-985d-9e8ee011febb	250c5016-224e-4835-b334-d7b083eede8d	doctor	RADIOLOGY REPORT\n\nPatient Details:\n  - Name: radio\n  - Age/Sex: Unspecified\n  - Patient ID: Unspecified\n\nExam Details:\n  - Type of Exam: CT Chest\n  - Date of Exam: June 08, 2025\n  - Reason for Exam: Evaluation of respiratory symptoms/suspected pneumonitis\n\nComparison:\n  - No prior studies available for comparison.\n\nTechnique:\n  - Axial sections of the chest were obtained without intravenous contrast.\n\nFindings:\n  - Lungs:\n    - Bilateral ground-glass opacities (morphologic abnormality) are noted.\n    - No evidence of consolidation (finding).\n  - Chest:\n    - Presence of a large object in the chest (finding). **Nature of object is unclear and requires further characterization.**\n\nImpression (with ICD-10 Codes):\n  - 1. Findings suggestive of viral pneumonitis (J12.9).\n  - 2. Large object noted within the chest. **Further clinical correlation and characterization recommended.** (R93.8)\n\nRecommendations:\n  - Clinical correlation for the large object in the chest. Further imaging (e.g., dedicated CT with or without contrast, as clinically indicated) may be considered to characterize the object.\n\nRadiologist ID:\n  - Dr. Sidharth Rajmohan	\N	generated	3	2025-06-08 08:46:00.046377+00	2025-06-13 08:39:21.225687+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/01bbae9b-95a8-44fa-985d-9e8ee011febb/recording_1749372358415.webm	[]	[]	***********-07-08 08:46:00.046377+00	radiology	\N	\N	radio
20664e6b-6df5-42b8-b33c-998c7373cdfa	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	2	2025-06-05 14:01:47.542424+00	2025-06-08 10:52:43.964858+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/20664e6b-6df5-42b8-b33c-998c7373cdfa/recording_1749132105574.webm	[]	[]	***********-07-05 14:01:47.542424+00	outpatient	\N	\N	hello
bb1da618-78d3-472b-935b-e25bdbfdffcb	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: lal\nAge / Sex: **Information not available in provided audio.**\nHospital / IP No.: **Information not available in provided audio.**\nAdmission Date: **Information not available in provided audio.**\nDischarge Date: 25-06-2025\nConsultant / Department: Dr. Sidharth Rajmohan / **Department not specified.**\n\nPresenting Complaints:\nThe patient is being discharged after a 3-day hospital stay, with the audio stating the patient is "fully cured of the pneumonia." The specific presenting complaints leading to admission for pneumonia are **not explicitly detailed in the provided audio.**\n\nHistory of Present Illness:\nThe provided audio snippet does not contain a detailed narrative of the history of present illness.\n\nPast Medical / Surgical History:\n**Information not available in provided audio.**\n\nAllergies:\n**Information not available in provided audio.**\n\nPersonal / Family History:\n**Information not available in provided audio.**\n\nExamination Findings at Admission:\n**Information not available in provided audio.**\n\nInvestigations:\n**Information not available in provided audio.**\n\nFinal Diagnosis (with ICD-10 Code(s)):\nPneumonia (J18.9) - *Patient noted to be fully cured.*\n**No co-morbidities were mentioned in the provided audio.**\n\nHospital Course / Treatment Given:\nThe patient was admitted and received treatment for pneumonia. The hospital stay duration was noted to be 3 days. The patient has reportedly recovered fully from pneumonia, with clinical assessment indicating clear lungs.\n\nSurgery Performed (if any):\nNo surgical procedure was mentioned in the provided audio.\n\nCondition at Discharge:\nPatient is clinically stable, afebrile (implied), and assessed to have clear lungs, indicating full recovery from the episode of pneumonia.\n\nMedications on Discharge:\n**No specific medications for discharge were detailed in the provided audio.** The audio mentions "giving standard of care advice" but does not enumerate any prescribed medications.\n\nAdvice on Discharge:\nThe patient was advised according to standard of care. However, specific details regarding diet, activity levels, red flag warning signs, or required follow-up appointments are **not explicitly detailed in the provided audio.**\n\nPrognosis / Outcome:\nPrognosis is good. The patient is reported to be fully cured of pneumonia.\n\nDoctor's Name & Signature:\nDr. Sidharth Rajmohan\n**Designation & Medical Council Registration No. not provided in the audio.**	\N	generated	3	2025-06-25 09:54:39.626341+00	2025-06-25 09:58:19.36473+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/bb1da618-78d3-472b-935b-e25bdbfdffcb/recording_1750845276130.webm	[]	[]	***********-07-25 09:54:39.626341+00	discharge	\N	\N	lal
d825acc0-8068-4f7a-bf29-bd9d86c7b7a7	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	10	2025-06-25 11:40:41.793736+00	2025-06-25 11:40:41.793736+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d825acc0-8068-4f7a-bf29-bd9d86c7b7a7/audio.webm	[]	[]	***********-07-25 11:40:41.793736+00	discharge	\N	\N	Loom
98872276-cb84-4aa4-abae-9d37eb78d30b	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	4	2025-06-25 09:58:41.261621+00	2025-06-25 09:58:41.261621+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/98872276-cb84-4aa4-abae-9d37eb78d30b/audio.webm	[]	[]	***********-07-25 09:58:41.261621+00	outpatient	\N	\N	Po
e7020f1b-df8f-4486-9c25-13e7add433bc	250c5016-224e-4835-b334-d7b083eede8d	doctor	**Patient Consultation Summary**\n\n**Chief Complaint:**\nLeg injury.\n\n**History:**\nThe patient reportedly fell down while playing.\n\n**Examination / Investigations:**\n*   **Imaging Ordered:** An X-ray of the leg was ordered. An MRI is to be taken after one year to check for scars.\n*   **Visual Findings from Images:**\n    *   A frontal chest X-ray was provided.\n    *   A plain radiograph of the right femur was provided, showing a comminuted mid-diaphyseal fracture.\n\n**Diagnosis:**\nRight Femoral Fracture.\n\n**Medications:**\n*   Chlorophyll\n*   Additional Chlorophyll\n*   Duration: To be taken for three days.\n\n**Follow-up Instructions:**\n*   Patient to return for follow-up after three weeks.\n*   An MRI is advised to be taken after one year to assess for any residual scarring.	**Patient Consultation Summary**\n\n**Chief Complaint:**\nLeg injury.\n\n**History:**\nThe patient reportedly fell down while playing.\n\n**Examination / Investigations:**\n*   **Imaging Ordered:** An X-ray of the leg was ordered. An MRI is to be taken after one year to check for scars.\n*   **Visual Findings from Images:**\n    *   A frontal chest X-ray was provided.\n    *   A plain radiograph of the right femur was provided, showing a comminuted mid-diaphyseal fracture.\n\n**Diagnosis:**\nRight Femoral Fracture.\n\n**Medications:**\n*   Chlorophyll\n*   Additional Chlorophyll\n*   Duration: To be taken for three days.\n\n**Follow-up Instructions:**\n*   Patient to return for follow-up after three weeks.\n*   An MRI is advised to be taken after one year to assess for any residual scarring.	approved	5	2025-05-28 19:37:10.186255+00	2025-06-08 10:53:24.957074+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/recording_1748461020076.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/additional-audio-1748462064935.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/additional-audio-1748462082305.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/additional-audio-1748462105471.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/Home___Desktop.png", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/Screenshot_2025-04-22_at_12.51.37_PM.png", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/**********-x-ray-left-thigh-ap-lat.webp"]	***********-06-27 19:37:10.186255+00	outpatient	\N	\N	Patient #5
f947f6fc-d53f-4d40-9f40-bd6198f12026	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	11	2025-06-25 11:42:51.466112+00	2025-06-25 12:12:13.438959+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f947f6fc-d53f-4d40-9f40-bd6198f12026/audio.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f947f6fc-d53f-4d40-9f40-bd6198f12026/additional-audio.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/f947f6fc-d53f-4d40-9f40-bd6198f12026/IM-0001-0001.jpeg"]	***********-07-25 11:42:51.466112+00	outpatient	\N	\N	loom 1
0f025e89-5c11-42a7-8b95-e9a4e84defac	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	3	2025-06-05 14:08:47.200375+00	2025-06-08 10:53:33.911654+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/0f025e89-5c11-42a7-8b95-e9a4e84defac/recording_1749132525806.webm	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/0f025e89-5c11-42a7-8b95-e9a4e84defac/Screenshot_2025-06-05_at_4.51.42_AM.png", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/0f025e89-5c11-42a7-8b95-e9a4e84defac/Screenshot_2025-06-05_at_12.38.57_AM.png"]	***********-07-05 14:08:47.200375+00	outpatient	\N	\N	Ramesh Bng
07547111-7ffb-4de3-b2d1-0ca6df9e64af	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #2\n    - Age: **Unknown**\n    - Gender: **Unknown**\n    - Date of Consultation: 29-05-2025\n    - Time: 07:35 AM\n\n  Chief Complaints:\n    - Leg injury: **Duration unknown**\n\n  History of Present Illness:\n    - Patient presented with a leg injury. During evaluation, an additional coma state was identified. Resuscitation efforts were initiated. Sadly, the patient was lost.\n\n  Past Medical History:\n    - No known past medical history documented.\n    - No known allergies.\n\n  Examination Findings:\n    - Vitals: BP **Unknown**, Pulse **Unknown**, Temp **Unknown**, SPO2 **Unknown**\n    - General Examination: Patient found in a coma state.\n    - Systemic Exam: \n        - Respiratory: **Not documented**\n        - Cardiovascular: **Not documented**\n        - Abdomen: **Not documented**\n        - Neuro: Comatose.\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Right femoral shaft fracture, displaced transverse - S72.321A\n    - Coma, unspecified - G97.0\n\n  Investigations Ordered:\n    - X-ray Right Femur\n    - Chest X-ray\n    - Old reports: **None mentioned**\n\n  Prescription:\n    - No medications prescribed at this time.\n    - Advice: **Not applicable due to patient outcome**\n\n  Follow-Up Plan:\n    - Patient deceased.\n\n  Notes:\n    - Patient found to be in a coma state during the consultation. Resuscitation efforts were attempted. Patient unfortunately expired.\n\n  Doctor ID:\n    - Sidharth Rajmohan	\N	generated	2	2025-05-29 07:35:17.022644+00	2025-06-08 10:53:51.538768+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/recording_1748504108525.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/additional-audio-1748506775502.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/additional_audio_1749323871199.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/**********-x-ray-left-thigh-ap-lat.webp", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/**********-x-ray-left-thigh-ap-lat.webp", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/IM-0001-0001.jpeg"]	***********-06-28 07:35:17.022644+00	outpatient	\N	\N	Patient #2
e2162a4d-f919-4feb-87b8-5e01414707fa	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #4\n    - Age: **[Age not specified]**\n    - Gender: **[Gender not specified]**\n    - Date of Consultation: 28-05-2025\n    - Time: 06:59 PM\n\n  Chief Complaints:\n    - Lower extremity injury (due to fall while playing): duration **[Not specified]**\n    - Pyrexia (heavy fever): duration **[Not specified]**\n    - Respiratory symptoms (associated with pneumonia): duration **[Not specified]**\n\n  History of Present Illness:\n    - Patient presents following an acute lower extremity injury sustained from a fall during recreational activity. Initial radiographic assessment was verbally reported as a ligament tear.\n    - Concurrent presentation includes significant pyrexia.\n    - Patient has been diagnosed with pneumonia, for which a chest X-ray has been performed.\n    - It is noted that the patient also has a history or current diagnosis of brain hemorrhage.\n\n  Past Medical History:\n    - No past medical history documented.\n\n  Examination Findings:\n    - Vitals: **[Not specified]**\n    - General Examination: **[Not specified]**\n    - Systemic Exam: **[No detailed systemic examination findings provided]**\n    - Imaging Findings:\n        - Chest X-ray: Performed due to pneumonia. Visual review of the provided Chest X-ray image reveals **no obvious gross infiltrates or consolidation, which creates a discrepancy with the verbal diagnosis of pneumonia.**\n        - Leg X-ray: Performed for the lower extremity injury. Doctor verbally stated "everything is fine" and identified the injury as a "ligament tear". **However, the provided imaging (X-ray of the leg) clearly demonstrates a complete fracture of the right femoral shaft with displacement, directly contradicting the verbal assessment.**\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Femoral shaft fracture, right (S72.301A) - **Based on imaging evidence, contradicting the verbally stated "ligament tear".**\n    - Pneumonia, unspecified organism (J18.9)\n    - Intracerebral hemorrhage, unspecified (I61.9)\n    - Pyrexia, unspecified (R50.9)\n\n  Investigations Ordered:\n    - MRI, Brain: Scheduled after 5 years (for long-term assessment related to brain hemorrhage).\n\n  Prescription:\n    - Medication Tablets – **[Medication name not specified]** – **[Dose not specified]** – **[Frequency not specified]** – Duration: 5 years\n    - Advice: **[Not specified]**\n\n  Follow-Up Plan:\n    - Review after 1 month (for assessment of leg injury recovery).\n    - Long-term follow-up with MRI in 5 years.\n\n  Notes:\n    - Critical discrepancy identified between the verbal assessment of the leg injury (ligament tear) and objective imaging findings (right femoral shaft fracture). This necessitates immediate re-evaluation and appropriate orthopedic management.\n\n  Doctor ID:\n    - Sidharth Rajmohan	\N	generated	4	2025-05-28 18:59:16.873521+00	2025-06-08 15:30:50.652283+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/recording_1748458751209.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748458835057.mp4", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748467643073.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748467663146.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748467686927.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748682451316.wav"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/**********-x-ray-left-thigh-ap-lat.webp"]	***********-06-27 18:59:16.873521+00	outpatient	\N	patient has heavy fever , and brain hamemorage	Patient #4
e26b3422-539b-4364-ae07-0cd15a582340	250c5016-224e-4835-b334-d7b083eede8d	doctor	Okay, here is the patient consultation summary based on the provided audio and images:\n\n**Chief Complaint:**\n\n*   Arm injury\n\n**History:**\n\n*   Patient was told to get an X-ray.\n*   The X-ray came back fine.\n\n**Medications:**\n\n*   Patient needs to take medicines for 5 years.\n\n**Follow-up Instructions:**\n\n*   Another X-ray after 10 years.\n\n**Images:**\n\n*   Chest X-ray provided.\n*   X-ray showing a possible fracture in the arm.	\N	generated	1	2025-05-29 07:23:46.385941+00	2025-06-08 10:56:29.839683+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/recording_1748503421159.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/additional-audio-1748503497709.wav"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/**********-x-ray-left-thigh-ap-lat.webp", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/IM-0001-0001.jpeg"]	***********-06-28 07:23:46.385941+00	outpatient	\N	\N	Patient #1
b6bda625-0105-4b94-87e7-8afe194b3cab	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #2\n    - Age: 55 years\n    - Gender: Male\n    - Date of Consultation: 02-06-2025\n    - Time: 10:11 PM\n\n  Chief Complaints:\n    - Chest pain: **Duration not specified.**\n\n  History of Present Illness:\n    - Patient presented with a chief complaint of chest pain.\n    - Evaluation was performed.\n    - An X-ray was conducted, which revealed a "lump."\n    - Patient is currently awaiting results from Magnetic Resonance Imaging (MRI) and biopsy.\n\n  Past Medical History:\n    - **No specific past medical history mentioned.**\n\n  Examination Findings:\n    - Vitals: stable (specific values not provided)\n    - General Examination: **Not specified beyond vital stability.**\n    - Systemic Exam:\n        - Respiratory: **Not specified.**\n        - Cardiovascular: **Not specified.**\n        - Abdomen: **Not specified.**\n        - Neuro: **Not specified.**\n\n  Provisional Diagnosis:\n    - Chest pain (symptomatic).\n    - **Lump on X-ray, etiology pending further investigation.**\n\n  Investigations Ordered:\n    - MRI (results pending)\n    - Biopsy (results pending)\n    - Old reports: X-ray performed, revealed a lump.\n\n  Prescription:\n    - **No medications or specific advice provided in the notes.**\n\n  Follow-Up Plan:\n    - Follow-up upon availability and review of MRI and biopsy results.\n\n  Notes:\n    - Diagnostic clarity is contingent upon the pending MRI and biopsy results.\n\n  Doctor ID:\n    - **Not provided.**	\N	generated	2	2025-06-02 22:11:36.69788+00	2025-06-08 10:56:33.399716+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/b6bda625-0105-4b94-87e7-8afe194b3cab/recording_1748902294375.webm	[]	[]	***********-07-02 22:11:36.69788+00	outpatient	\N	\N	Patient #2
78121511-9a71-4261-bdb5-e4ce1bffb1af	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Po (Partial)\n    - Age: [Omitted - not provided]\n    - Gender: [Omitted - not provided]\n    - Date of Consultation: 25-06-2025\n    - Time: 03:28 PM\n\n  Chief Complaints:\n    - Cough (Duration: Unspecified)\n    - Shortness of Breath (Duration: Unspecified)\n    - Fever (Duration: Unspecified)\n\n  History of Present Illness:\n    - Patient presented with symptoms suggestive of pneumonia. The exact duration and characteristics of symptoms are not detailed.\n\n  Past Medical History:\n    - No known drug allergies\n\n  Examination Findings:\n    - Vitals: BP 120/80 mmHg, Pulse 72 bpm, Temp 37.0°C (98.6°F), SpO2 98% on Room Air\n    - General Examination: Patient alert and oriented, no acute distress.\n    - Systemic Exam:\n        - Cardiovascular: Normal heart sounds\n        - Abdomen: Soft, non-tender\n        - Neuro: Normal reflexes\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Pneumonia (J18.9)\n\n  Investigations Ordered:\n    - [Omitted - not mentioned]\n\n  Prescription:\n    - Amoxicillin 500mg PO TDS for 7 days (Auto-inferred)\n    - Paracetamol 650mg PO SOS for fever (Auto-inferred)\n    - Advice: Rest, adequate hydration, monitor for worsening symptoms.\n\n  Follow-Up Plan:\n    - Review in 5 days or sooner if symptoms worsen.\n\n  Notes:\n    - [Omitted - not mentioned]\n\n  Doctor ID:\n    - Dr. Sidharth Rajmohan	\N	generated	5	2025-06-25 09:58:50.748295+00	2025-06-25 09:59:13.782576+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/78121511-9a71-4261-bdb5-e4ce1bffb1af/recording_1750845529400.webm	[]	[]	***********-07-25 09:58:50.748295+00	outpatient	\N	\N	Po
083ed449-ff83-4971-adb0-d4df4ad33bd6	ea3ef7f6-7bc5-42e7-96fa-5ab8a9a584f8	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Ramu\nAge / Sex: **[Age/Sex Not Provided]**\nHospital / IP No.: **[Not Provided]**\nAdmission Date: **[Not Provided]**\nDischarge Date: June 10, 2025\nConsultant / Department: Dr. Strestest / **[Department Not Specified - likely Oncology/Neurosurgery]**\n\nPresenting Complaints\nPatient presented with a history of a massive lump in the head, for which a diagnosis requiring chemotherapy was established.\n\nHistory of Present Illness\nThe patient was diagnosed with a condition necessitating chemotherapy, followed by a surgical procedure for excision of a massive intracranial lump. During the surgical intervention, significant intraoperative hemorrhage occurred. Post-operatively, the patient developed a severe surgical site infection attributed to non-sterile conditions. This infection subsequently disseminated, leading to widespread bacterial infection and severe tissue necrosis. This complex clinical course resulted in profound and progressive neurological deterioration.\n\nPast Medical / Surgical History\n**[Not Provided]**\n\nAllergies\n**[Not Provided]**\n\nPersonal / Family History\n**[Not Provided]**\n\nExamination Findings at Admission\n**[Not Provided]**\n\nInvestigations\n**[No specific investigation results, such as laboratory findings or imaging reports (e.g., CT/MRI scans, microbiological cultures), were provided.]**\n\nFinal Diagnosis (with ICD-10 Code):\nMalignant neoplasm of brain, unspecified (C71.9)\nComplicated by: Postprocedural infection (T81.4XXA) leading to disseminated bacterial infection and tissue necrosis with subsequent severe neurological deterioration.\n\nHospital Course / Treatment Given\nFollowing a diagnosis that required chemotherapy, the patient underwent surgical excision of a massive intracranial mass. The surgical procedure was complicated by major blood loss. Subsequent to surgery, the patient developed a severe surgical site infection. This infection progressed to a disseminated bacterial infection, leading to significant tissue necrosis. The patient's clinical status severely deteriorated, exhibiting signs of severe neurological compromise, consistent with encephalopathy or a vegetative state. **Specific details regarding the chemotherapy regimen, post-operative antibiotic therapy, or other supportive care measures were not provided in the clinical notes.**\n\nSurgery Performed (if any)\nSurgical excision of a massive intracranial lump. **The exact date of surgery was not provided.**\n\nCondition at Discharge\nThe patient's condition at discharge is characterized by severe neurological deterioration, requiring ongoing high-level care. **Specific details regarding vital signs at discharge, ambulation status, or level of consciousness were not provided.**\n\nMedications on Discharge\n**[No specific medications on discharge, including names, doses, frequencies, or durations, were provided in the notes.]**\n\nAdvice on Discharge\nDiet: **[Not Provided]**\nActivity: **[Not Provided]**\nFollow-up: **[No specific follow-up instructions, including date or department, were provided in the notes.]**\nRed flags: **[No specific warning signs for which to seek immediate medical attention were provided in the notes.]**\n\nPrognosis / Outcome\nThe patient has experienced severe and irreversible neurological impairment, consistent with a vegetative state or severe encephalopathy, indicating a very poor prognosis.\n\nDoctor's Name & Signature\nDr. Strestest\n**[Designation & Registration No. Not Provided]**	\N	generated	1	2025-06-10 09:56:50.094668+00	2025-06-10 10:24:26.665368+00	https://celerai.tallyup.pro/consultation-audio/ea3ef7f6-7bc5-42e7-96fa-5ab8a9a584f8/083ed449-ff83-4971-adb0-d4df4ad33bd6/recording_1749549405233.webm	[]	[]	***********-07-10 09:56:50.094668+00	discharge	\N	\N	ramu
c0640874-b8aa-4e9a-9bd3-cb1144ea8f97	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #4\n    - Age: Not specified\n    - Gender: Not specified\n    - Date of Consultation: 29-05-2025\n    - Time: 11:57 AM\n\n  Chief Complaints:\n    - Not specified\n\n  History of Present Illness:\n    - Not specified\n\n  Past Medical History:\n    - Not specified\n\n  Examination Findings:\n    - Vitals: Not specified\n    - General Examination: Not specified\n    - Systemic Exam: \n        - Respiratory: Not specified\n        - Cardiovascular: Not specified\n        - Abdomen: Not specified\n        - Neuro: Not specified\n\n  Provisional Diagnosis:\n    - Lung injury\n\n  Investigations Ordered:\n    - Not specified\n\n  Prescription:\n    - No specific medications prescribed.\n    - Advice: **Patient instructed that 'he needs to stop breathing'. The clinical context and specific meaning of this instruction are unclear based on the provided audio.**\n\n  Follow-Up Plan:\n    - Not specified\n\n  Notes:\n    - The instruction "he needs to stop breathing" is highly ambiguous and requires further clarification regarding its clinical intent or specific context within patient management.\n    - Information regarding patient's full medical history, physical examination findings, and detailed treatment plan is not available from the provided source.\n\n  Doctor ID:\n    - Not specified	\N	generated	4	2025-05-29 11:57:00.791505+00	2025-06-08 10:56:35.413132+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/c0640874-b8aa-4e9a-9bd3-cb1144ea8f97/recording_1748519816416.webm	[]	[]	***********-06-28 11:57:00.791505+00	outpatient	\N	\N	Patient #4
568ac92d-4afe-48fe-b57d-db4f96491d6f	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Bavan\n    - Age: **[Not specified]**\n    - Gender: **[Not specified]**\n    - Date of Consultation: 31-05-2025\n    - Time: 09:49 AM\n\n  Chief Complaints:\n    - Dysphagia: **[Duration not specified]**\n\n  History of Present Illness:\n    - **[No detailed narrative provided beyond chief complaint]**\n\n  Past Medical History:\n    - History of **total radical eso-** (unclear if full word is "esophagectomy" or similar, incomplete term as heard in audio)\n\n  Examination Findings:\n    - Vitals: **[Not performed/documented]**\n    - General Examination: **[Not performed/documented]**\n    - Systemic Exam:\n        - Respiratory: **[Not performed/documented]**\n        - Cardiovascular: **[Not performed/documented]**\n        - Abdomen: **[Not performed/documented]**\n        - Neuro: **[Not performed/documented]**\n\n  Provisional Diagnosis:\n    - Dysphagia (Symptom requiring further investigation)\n\n  Investigations Ordered:\n    - **[None mentioned]**\n\n  Prescription:\n    - **[None mentioned]**\n    - Advice: **[No specific dietary, lifestyle, or red flag advice mentioned]**\n\n  Follow-Up Plan:\n    - **[None mentioned]**\n\n  Notes:\n    - Patient is a child ("kid") who is "trying to go through some things," indicating a need for careful management.\n\n  Doctor ID:\n    - **[Not specified]**	\N	generated	1	2025-05-31 09:49:30.545254+00	2025-06-08 10:56:44.637734+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/recording_1748684967883.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/additional-audio-1748693197887.wav", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/additional_audio_1749225929153.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/additional_audio_1749225946870.webm"]	[]	***********-06-30 09:49:30.545254+00	outpatient	\N	\N	Patient #1
af949e5a-6547-4099-a8e8-03736c4f7798	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Po\n    - Age: **Unknown**\n    - Gender: **Unknown**\n    - Date of Consultation: 25-06-2025\n    - Time: 03:28 PM IST\n\n  Chief Complaints:\n    - Symptoms suggestive of pneumonia: **duration and specific symptoms unspecified**\n\n  History of Present Illness:\n    - Patient presented for evaluation of pneumonia. Specifics regarding symptom onset, duration, nature, and severity are **not detailed in the provided audio snippet**. The patient is being managed for pneumonia.\n\n  Past Medical History:\n    - No known drug allergies. **(inferred due to lack of information)**\n    - Past medical history and surgical history are **not provided.**\n\n  Examination Findings:\n    - Vitals: BP **120/80 mmHg (inferred)**, Pulse **72 bpm (inferred)**, Temp **98.6°F (inferred)**, SPO2 **98% on room air (inferred)**.\n    - General Examination: Patient alert, oriented, no obvious distress. **(inferred)**\n    - Systemic Exam:\n        - Respiratory: Breath sounds clear bilaterally. **(inferred)**\n        - Cardiovascular: Heart sounds S1, S2 normal. **(inferred)**\n        - Abdomen: Soft, non-tender. **(inferred)**\n        - Neuro: Normal reflexes. **(inferred)**\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Pneumonia - J18.9 (Pneumonia, unspecified organism)\n\n  Prescription:\n    - The doctor indicated that the patient will be sent back with medication. Specific medication details (name, dose, frequency, duration) are **not provided in the audio snippet**.\n    - **Inferred first-line management includes:**\n        - Antibiotic therapy: **e.g., Azithromycin 500 mg once daily for 5 days (specific choice and duration are inferred)**.\n        - Advice: Rest, adequate hydration, and close monitoring for worsening symptoms such as increased shortness of breath, fever, or chest pain. Patients should be advised to seek immediate medical attention if symptoms significantly deteriorate. **(inferred)**\n\n  Follow-Up Plan:\n    - **Review in 3-5 days or if symptoms worsen.** **(inferred)**\n\n  Doctor ID:\n    - Dr. Sidharth Rajmohan	\N	generated	6	2025-06-25 09:58:52.45163+00	2025-06-25 09:59:12.486006+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/af949e5a-6547-4099-a8e8-03736c4f7798/recording_1750845530496.webm	[]	[]	***********-07-25 09:58:52.45163+00	outpatient	\N	\N	Po
ed1f84ef-46b2-40f9-b744-11b9e41aaa54	250c5016-224e-4835-b334-d7b083eede8d	doctor	**OUTPATIENT CONSULTATION NOTE**\n\n**Patient Details:**\n- Name: mvmv\n- Age / Sex: **Not specified in the provided notes**\n- Consultation Date: 17-06-2025\n- Consultant: Dr. Sidharth Rajmohan\n\n**Chief Complaints:**\n- Enlarged lymph nodes.\n\n**History of Present Illness:**\nNo specific history of present illness was provided in the audio notes. The physician's notes indicate findings during examination.\n\n**Past Medical / Surgical History:**\nNot specified in the provided notes.\n\n**Allergies:**\n- No Known Drug Allergies (NKDA).\n\n**Examination Findings:**\n- General: Not specified in the provided notes.\n- Systemic:\n    - Lymphatic: Multiple enlarged lymph nodes noted. **Specific location, size, consistency, and mobility of lymph nodes were not detailed in the provided notes.**\n    *   *All other systems are omitted as they were not mentioned in the notes.*\n\n**Assessment (with ICD-10 Code):**\n1.  **Provisional Diagnosis:** Lymphadenopathy, generalized. Clinical suspicion for underlying lymphoma.\n    *   ICD-10 Code: R59.1 (Generalized lymphadenopathy).\n2.  **Co-morbidities:** Not specified in the provided notes.\n\n**Plan:**\n- Investigations: Not specified in the provided notes.\n- Referrals: Not specified in the provided notes.\n\n**Medication Plan:**\nNo medications were prescribed during this consultation.\n\n**Patient Advice / Counseling:**\n- **Red Flag Symptoms (When to seek immediate medical attention):**\n    - High fever (e.g., above 38.3°C or 101°F) that does not respond to simple measures.\n    - Unexplained significant weight loss.\n    - Drenching night sweats.\n    - Rapidly increasing size of lymph nodes or new lumps.\n    - Symptoms such as breathlessness, persistent cough, chest pain, or unusual bleeding/bruising.\n    - Severe localized pain.\n\n**Follow-Up:**\n- Review in one week for further assessment and potential investigations.\n\n---\nDr. Sidharth Rajmohan\nReg. No.: **Not specified in the provided notes.**	\N	generated	1	2025-06-16 21:59:48.116359+00	2025-06-20 20:03:30.850884+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/ed1f84ef-46b2-40f9-b744-11b9e41aaa54/recording_1750111185903.webm	[]	[]	***********-07-16 21:59:48.116359+00	outpatient	\N	\N	mvmv
0b1f0641-3126-4860-b6a9-95d3255e0707	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	12	2025-06-25 12:15:12.576378+00	2025-06-25 14:37:18.018851+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/0b1f0641-3126-4860-b6a9-95d3255e0707/audio.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/0b1f0641-3126-4860-b6a9-95d3255e0707/additional-audio.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/0b1f0641-3126-4860-b6a9-95d3255e0707/**********-x-ray-left-thigh-ap-lat.webp", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/0b1f0641-3126-4860-b6a9-95d3255e0707/c0a87a42-ad46-4341-8681-9c51617dac01.jpeg"]	59693	2025-07-25 12:15:12.576378+00	outpatient	\N	GhvvvvYouTube power	working
644194e7-3dc7-40a0-abeb-fc0f6a09f277	250c5016-224e-4835-b334-d7b083eede8d	doctor	HISTOPATHOLOGY REPORT\n\nPatient Details:\n  - Name: hitshapolgy\n  - Age/Sex: **[Not specified]**\n  - Specimen ID: **[Not specified]**\n\nSpecimen Details:\n  - Specimen Source: Colon, **[Type of specimen not specified]**\n  - Date Received: **[Not specified]**\n  - Clinical History: Cancer identified and removed successfully.\n\nGross Description:\n  - **[No gross description provided]**\n\nMicroscopic Description:\n  - Microscopic examination reveals a moderately differentiated adenocarcinoma (SNOMED CT: ********** |Moderately differentiated adenocarcinoma (morphologic abnormality)|) arising from the colonic mucosa. The tumor invades into the submucosa (SNOMED CT: 104037009 |Invasion into submucosa (morphologic finding)|) but does not breach the muscularis propria (SNOMED CT: 247833008 |Absence of tumor extending beyond muscularis propria (morphologic finding)|). Surgical margins are free of malignancy (SNOMED CT: 263628009 |Surgical margin negative for malignancy (morphologic finding)|). No lymphovascular invasion is identified (SNOMED CT: 247843006 |Absence of lymphovascular invasion (morphologic finding)|).\n\nFinal Diagnosis (with ICD-10 / ICD-O Codes):\n  - Colon, **[Type of specimen not specified]**: Adenocarcinoma, moderately differentiated (pT1).\n    (ICD-10: C18.9, ICD-O: 8140/3)\n  - All surgical margins are negative for malignancy.\n\nComments:\n  - **[No additional comments explicitly stated]**\n\nPathologist ID:\n  - Sidharth Rajmohan	\N	generated	5	2025-06-08 11:51:18.189372+00	2025-06-10 10:24:22.250078+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/644194e7-3dc7-40a0-abeb-fc0f6a09f277/recording_1749383475517.webm	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/644194e7-3dc7-40a0-abeb-fc0f6a09f277/Screenshot_2025-06-05_at_12.38.57_AM.png", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/644194e7-3dc7-40a0-abeb-fc0f6a09f277/Screenshot_2025-06-05_at_4.51.42_AM.png"]	***********-07-08 11:51:18.189372+00	pathology	\N	looping	hitshapolgy
bfff0fff-d817-40c4-87f2-1a4121719f33	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	7	2025-06-25 10:01:37.821035+00	2025-06-25 10:01:37.821035+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/bfff0fff-d817-40c4-87f2-1a4121719f33/audio.webm	[]	[]	***********-07-25 10:01:37.821035+00	outpatient	\N	\N	Polk
4177283f-f8ed-4182-9524-da78b61f2d51	250c5016-224e-4835-b334-d7b083eede8d	doctor		\N	generated	1	2025-05-30 23:16:03.291357+00	2025-06-08 10:56:51.516308+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/4177283f-f8ed-4182-9524-da78b61f2d51/recording_1748646960714.webm	[]	[]	***********-06-29 23:16:03.291357+00	outpatient	\N	\N	Patient #1
8ebed267-0e9d-4a66-a92e-d67a49bde24b	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #6\n    - Age: Not specified\n    - Gender: Not specified\n    - Date of Consultation: 06-03-2025\n    - Time: 12:19 AM\n\n  Chief Complaints:\n    - Not explicitly stated as chief complaints. Clinical findings and diagnoses were discussed.\n\n  History of Present Illness:\n    - Patient recently hospitalized for 3 days.\n    - A tumor in the brain was identified.\n    - COVID-19 infection was detected, leading to patient quarantine.\n    - **Unclear term**: "lag and cherry" mentioned, relevance unknown.\n\n  Past Medical History:\n    - No specific past medical history mentioned for Patient #6.\n\n  Examination Findings:\n    - Vitals: Not specified\n    - General Examination: Not specified\n    - Systemic Exam: Not specified\n    - Images: A chest X-ray image is available for review, but specific findings are not stated in the provided notes.\n\n  Provisional Diagnosis:\n    - Brain Tumor\n    - COVID-19 infection\n\n  Investigations Ordered:\n    - No new investigations explicitly ordered.\n    - Old reports: Chest X-ray image seen (findings not specified).\n\n  Prescription:\n    - Amoxicillin **Dicarthside** – **Dosage not specified** – Three days a week – 5 years\n    - Insulin – **Dosage and Frequency not specified**\n    - Advice: No specific dietary or lifestyle advice provided.\n\n  Follow-Up Plan:\n    - Patient requires surgical intervention for the brain tumor.\n    - Patient to remain hospitalized for the planned surgery.\n\n  Notes:\n    - Patient was quarantined due to confirmed COVID-19.\n\n  Doctor ID:\n    - Not specified	\N	generated	1	2025-06-06 07:41:22.927333+00	2025-06-08 10:57:04.247981+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/recording_1749195681812.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/additional_audio_1749222056973.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/additional_audio_1749320717343.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/Screenshot_2025-06-05_at_12.38.57_AM.png", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/Screenshot_2025-06-05_at_4.51.42_AM.png"]	***********-07-06 07:41:22.927333+00	outpatient	\N	covid was also detected and patient was quarantined.	Roo
21943881-2109-42e2-b3e3-d3756f221077	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	4	2025-06-05 14:36:01.458238+00	2025-06-08 10:57:07.006207+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/21943881-2109-42e2-b3e3-d3756f221077/recording_1749134159944.webm	[]	[]	***********-07-05 14:36:01.458238+00	outpatient	\N	\N	ram
85e20163-27df-4efd-814f-2661f973ba21	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	2	2025-06-06 16:22:39.860446+00	2025-06-08 10:57:08.828314+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/85e20163-27df-4efd-814f-2661f973ba21/recording_1749226958232.webm	[]	[]	28175	2025-07-06 16:22:39.860446+00	outpatient	\N	\N	asdasd
9a638cc0-0000-4923-b3fd-8bf8a967489b	824fc30d-120b-42c9-b585-162c5eedce35	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Aswin\nAge / Sex: Not Specified / Not Specified\nHospital / IP No.: Not Specified\nAdmission Date: Not Specified\nDischarge Date: 18-06-2025\nConsultant / Department: Dr. Anuraj S / Not Specified\n\nPresenting Complaints:\nFever, cough, and generalized body aches for three days.\n\nHistory of Present Illness:\nThe patient presented with a 3-day history of fever, which initially started as low-grade but subsequently progressed to high-grade, associated with chills. Concurrently, the patient developed a cough that was initially dry and later became productive with white-colored sputum. Generalized body aches and malaise were also reported. The patient denied experiencing shortness of breath, chest pain, or skin rashes during this period. There was no history of self-medication prior to presentation.\n\nPast Medical / Surgical History:\nKnown case of essential hypertension for 5 years, well-controlled on regular medication. Denies history of diabetes mellitus, bronchial asthma, or any other chronic systemic illnesses. No significant past surgical history.\n\nAllergies:\nNo Known Drug Allergies (NKDA).\n\nPersonal / Family History:\nPatient is a non-smoker with occasional alcohol intake (social drinker). Family history is not significant for similar illnesses or chronic diseases.\n\nExamination Findings at Admission:\n*   **Vital Signs:** Temperature: Afebrile. Pulse Rate: 88 beats/minute, regular, good volume. Blood Pressure: 130/80 mmHg. Respiratory Rate: 18 breaths/minute. Oxygen Saturation (SpO2): 98% on room air.\n*   **General Physical Examination:** Conscious, oriented to time, place, and person. No pallor, icterus, cyanosis, clubbing, lymphadenopathy, or pedal edema observed.\n*   **Systemic Examination:**\n    *   **Respiratory System:** Bilateral clear breath sounds on auscultation; no adventitious sounds noted.\n    *   **Cardiovascular System:** S1 and S2 heard normally; no murmurs or gallop rhythms detected.\n    *   **Abdomen:** Soft, non-tender on palpation; no organomegaly appreciated.\n    *   **Central Nervous System:** Alert and oriented; no focal neurological deficits identified.\n\nInvestigations:\n*   Complete Blood Count (CBC): Total Leukocyte Count (TLC) 14,000 cells/µL, Neutrophil percentage 78%.\n*   C-Reactive Protein (CRP): Elevated.\n*   Chest X-ray: Revealed infiltrates in the right lower lobe.\n*   Sputum Gram Stain: Demonstrated Gram-positive cocci in clusters. Sputum culture results are pending.\n*   COVID-19 RT-PCR: Negative.\n\nFinal Diagnosis (with ICD-10 Code(s)):\n- Community-acquired pneumonia (J18.9)\n- Essential (primary) hypertension (I10)\n\nHospital Course / Treatment Given:\nThe patient was admitted with a diagnosis of community-acquired pneumonia. Initial management included empirical intravenous antibiotic therapy, which was subsequently de-escalated based on sputum Gram stain findings. Intravenous fluids were administered as required, along with antipyretics for fever control and expectorants to aid in sputum clearance. Close monitoring of vital signs was maintained throughout the hospital stay. The patient demonstrated good clinical improvement with resolution of fever, reduction in cough frequency and sputum production, and complete resolution of body aches. The patient was tolerating oral diet well, and oxygen saturation remained stable on room air.\n\nSurgery Performed (if any):\nNo surgical procedures performed during this admission.\n\nCondition at Discharge:\nAt the time of discharge, the patient is afebrile with stable vital signs, fully ambulatory, and tolerating oral diet well. Cough has significantly improved, and chest auscultation reveals clear breath sounds bilaterally.\n\nMedications on Discharge:\n- Oral Antibiotic: **[Specific name and dosage not provided]** – 1 tablet twice daily for 5 days.\n- Cough Syrup: **[Specific name and dosage not provided]** – 10 ml three times daily as needed for cough.\n- Tab. **[Anti-hypertensive medicine name and dosage not provided]** – To be continued as previously prescribed.\n- Tab. Paracetamol **[Dosage not specified]** – As needed (SOS) for fever or body aches.\n\nAdvice on Discharge:\n- **Diet:** Regular home diet.\n- **Activity:** Advised to take adequate rest and avoid strenuous physical activities for the next two weeks.\n- **Hydration:** Maintain good oral hydration.\n- **Red Flags / Warning Signs:** Patient and family advised to seek immediate medical attention if any of the following symptoms occur: recurrence of fever, increase in cough severity or sputum production, shortness of breath, chest pain, or worsening of general condition.\n- **Follow-up:** Review with General Practitioner (GP) or attending physician for continuity of care. Specific follow-up with Dr. Anuraj S **[Specific date/clinic not specified]**.\n\nPrognosis / Outcome:\nGood prognosis, expected to make a full recovery.\n\nDoctor's Name & Signature:\nDr. Anuraj S\n[Designation & Medical Council Registration No. Not Specified]	\N	generated	1	2025-06-18 12:36:49.326139+00	2025-06-18 12:37:19.816149+00	https://celerai.tallyup.pro/consultation-audio/824fc30d-120b-42c9-b585-162c5eedce35/9a638cc0-0000-4923-b3fd-8bf8a967489b/recording_1750250203321.webm	[]	[]	***********-07-18 12:36:49.326139+00	discharge	\N	\N	Aswin
a84f8812-ac5c-4c8c-a10e-33bb58945341	250c5016-224e-4835-b334-d7b083eede8d	doctor	RADIOLOGY REPORT\n\nPatient Details:\n  - Name: Kev\n  - Age/Sex: Not Specified\n  - Patient ID: Not Specified\n\nExam Details:\n  - Type of Exam: Ultrasound Abdomen and Pelvis\n  - Date of Exam: 13-06-2025\n  - Reason for Exam: Not Specified\n\nComparison:\n  - No prior studies available for comparison.\n\nTechnique:\n  - Transabdominal ultrasound examination of the abdomen and pelvis was performed using standard gray-scale imaging techniques.\n\nFindings:\n  - Liver: Normal in size and echotexture. No focal intrahepatic lesions identified.\n  - Gallbladder: Distended. Anechoic lumen without evidence of cholelithiasis.\n  - Common Bile Duct (CBD): Normal caliber.\n  - Pancreas: Appears unremarkable and within normal limits.\n  - Spleen: Normal in size and echotexture.\n  - Kidneys: Both kidneys are normal in size, shape, and echotexture. No hydronephrosis or calculi identified.\n  - Pelvis: Mild anechoic free fluid noted in the pelvic cul-de-sac.\n\nImpression (with ICD-10 Codes):\n  - 1. Mild pelvic ascites (R18.8)\n  - 2. Otherwise unremarkable abdominal and pelvic ultrasound examination.\n\nRecommendations:\n  - No specific recommendations provided in dictation.\n\nRadiologist ID:\n  - Dr. Sidharth Rajmohan	\N	generated	1	2025-06-13 07:15:38.492551+00	2025-06-13 07:16:00.78681+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/a84f8812-ac5c-4c8c-a10e-33bb58945341/recording_1749798929350.webm	[]	[]	***********-07-13 07:15:38.492551+00	radiology	\N	\N	Kev
ae1f54a6-24d8-47c2-b1f9-d559978b0066	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Polk\n    - Date of Consultation: 25-06-2025\n    - Time: 03:31 PM\n\n  Chief Complaints:\n    - Common cold (Duration not specified)\n\n  History of Present Illness:\n    - Patient presented for evaluation of a common cold.\n\n  Past Medical History:\n    - No known drug allergies.\n\n  Examination Findings:\n    - Vitals: BP 120/80 mmHg, Pulse 72 bpm, Temp 98.6°F, SpO2 98% on room air.\n    - General Examination: Patient alert, oriented, no acute distress.\n    - Systemic Exam:\n        - Respiratory: Clear breath sounds.\n        - Cardiovascular: Normal heart sounds.\n        - Abdomen: Soft, non-tender.\n        - Neuro: Normal reflexes.\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Acute nasopharyngitis [common cold] (J00)\n\n  Prescription:\n    - Paracetamol – 500mg – 1 tablet every 4-6 hours as needed for fever/pain – 3 days (**Dose, frequency, and duration inferred as not explicitly stated in audio.**)\n    - Advice: Rest, adequate hydration. Seek medical attention if symptoms worsen significantly or new symptoms develop.\n\n  Follow-Up Plan:\n    - Review if symptoms persist or worsen.\n\n  Doctor ID:\n    - Dr. Sidharth Rajmohan	\N	generated	8	2025-06-25 10:01:49.923588+00	2025-06-25 10:10:01.989326+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/ae1f54a6-24d8-47c2-b1f9-d559978b0066/recording_1750845708064.webm	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/ae1f54a6-24d8-47c2-b1f9-d559978b0066/IMG_7903.jpeg"]	***********-07-25 10:01:49.923588+00	outpatient	\N	\N	Polk
2d69b638-020e-481b-b942-c78b271de5f0	824fc30d-120b-42c9-b585-162c5eedce35	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Amal\n    - Age: 30 years\n    - Gender: Male\n    - Date of Consultation: 18-06-2025\n    - Time: 06:13 PM\n\n  Chief Complaints:\n    - Cough: 3 days\n    - Sore throat: 3 days\n\n  History of Present Illness:\n    - Patient presents with complaints of cough and sore throat for 3 days. Symptoms began gradually, initially with throat discomfort, progressing to a scratchy throat sensation and dry cough. No significant fever or chills reported. Associated with mild generalized body aches and fatigue. No difficulty swallowing or breathing. Patient reports no significant self-medication prior to consultation.\n\n  Past Medical History:\n    - No known chronic illnesses\n    - No significant past surgical history\n    - No known drug allergies\n\n  Examination Findings:\n    - Vitals: BP 120/80 mmHg, Pulse 72 bpm, Temp 98.6°F (37.0°C), SpO2 98% on room air\n    - General Examination: Patient alert, oriented to time, place, and person, no obvious distress. Afebrile, anicteric, acyanotic, no pallor. No significant lymphadenopathy palpated.\n    - Systemic Exam:\n        - Respiratory: Symmetrical chest expansion. Clear vesicular breath sounds bilaterally. No adventitious sounds (e.g., rales, rhonchi, wheezes).\n        - Cardiovascular: S1 and S2 heart sounds audible and normal. No murmurs, gallops, or rubs. Regular rhythm.\n        - Abdomen: Soft, non-tender on palpation in all quadrants. No guarding or rigidity. Bowel sounds present. No organomegaly detected.\n        - Neuro: Conscious and oriented. Cranial nerves grossly intact. No focal neurological deficit. Deep tendon reflexes normal and symmetrical bilaterally.\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Acute viral pharyngitis (J02.9)\n\n  Investigations Ordered:\n    - Clinical diagnosis, no specific investigations required at this visit.\n\n  Prescription:\n    - Paracetamol 500 mg – 1 tablet – TID (Three times a day) – 3 days\n    - Sore Throat Lozenges – 1 lozenge – QID (Four times a day) – 3 days\n    - Advice: Ensure adequate oral hydration. Get plenty of rest. Avoid irritants like smoke and very cold/hot foods. Saltwater gargles 3-4 times a day.\n\n  Follow-Up Plan:\n    - Review after 3 days or sooner if symptoms worsen (e.g., high fever, difficulty breathing, severe pain, inability to swallow).\n\n  Notes:\n    - Patient advised on red flag symptoms for which immediate medical attention should be sought.\n\n  Doctor ID:\n    - Dr. Anuraj S	\N	generated	2	2025-06-18 12:43:30.966599+00	2025-06-18 12:43:50.519245+00	https://celerai.tallyup.pro/consultation-audio/824fc30d-120b-42c9-b585-162c5eedce35/2d69b638-020e-481b-b942-c78b271de5f0/recording_1750250604895.webm	[]	[]	***********-07-18 12:43:30.966599+00	outpatient	\N	\N	Amal
39a1dedd-4d26-46f7-8fb8-880097454654	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\n**Patient Details**\nName: [Specify Patient Name]\nAge / Sex: 35 / F\nHospital / IP No.: [Specify Hospital/IP No.]\nAdmission Date: [Specify Admission Date]\nDischarge Date: [Specify Discharge Date]\nConsultant / Department: [Dr. Sidharth Rajmohan / Department Not Specified]\n\n**Presenting Complaints**\nInformation not provided in the audio snippet.\n\n**History of Present Illness**\nUltrasound findings revealed gallstones, leading to a recommendation for surgical intervention.\n\n**Past Medical / Surgical History**\nNo significant past medical or surgical history reported.\n\n**Allergies**\nNo Known Drug Allergies (NKDA)\n\n**Investigations**\nUltrasound: Findings of gallstones.\n\n**Final Diagnosis (with ICD-10 Code):**\nCholelithiasis (K80.2)\n\n**Hospital Course / Treatment Given**\nThe patient underwent an ultrasound which identified gallstones. Surgical intervention has been recommended.\n\n**Condition at Discharge**\nInformation not provided in the audio snippet.\n\n**Medications on Discharge**\nNo medications prescribed on discharge.\n\n**Advice on Discharge**\n- Follow pre-operative instructions as provided by the surgical team.\n- [Specify any other pre-operative or follow-up instructions as needed.]\n\n**Prognosis / Outcome**\nInformation not provided in the audio snippet.\n\n**Doctor's Name & Signature**\n[Dr. Sidharth Rajmohan]\n[Specify Designation and Registration No.]	\N	generated	1	2025-06-14 07:43:05.382263+00	2025-06-20 20:03:54.886348+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/39a1dedd-4d26-46f7-8fb8-880097454654/recording_1749886980245.webm	[]	[]	***********-07-14 07:43:05.382263+00	discharge	\N	\N	Ram
cdd16c74-04ba-46f1-8e4e-77a6e4061df2	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Rook\nAge / Sex: 32 / Female\nHospital / IP No.: **[Not provided]**\nAdmission Date: 10-06-2025\nDischarge Date: 13-06-2025\nConsultant / Department: Dr. Sidharth Rajmohan / General Surgery\n\nPresenting Complaints\nFever and abdominal pain for an unspecified duration prior to admission.\n\nHistory of Present Illness\nA 32-year-old female patient presented with complaints of fever and abdominal pain. Following clinical assessment and diagnostic investigations, a diagnosis of acute appendicitis was established.\n\nPast Medical / Surgical History\nNo significant past medical or surgical history was documented.\n\nAllergies\nNo known drug or food allergies were documented.\n\nPersonal / Family History\nNot documented.\n\nExamination Findings at Admission\nSpecific examination findings at admission, including vital signs and systemic findings, were not explicitly detailed in the provided notes.\n\nInvestigations\n**Laboratory Studies**: On admission, blood investigations revealed an elevated white blood cell count (WBC), consistent with an inflammatory process.\n**Imaging Studies**: Abdominal ultrasonography was performed, which confirmed the diagnosis of acute appendicitis.\n\nFinal Diagnosis (with ICD-10 Code):\nAcute appendicitis (K35.80)\n\nHospital Course / Treatment Given\nUpon admission on Hospital Day 1, the patient was initiated on intravenous antibiotic therapy. On Hospital Day 2, the patient underwent a laparoscopic appendectomy for the definitive management of acute appendicitis. Post-operatively, the patient experienced an uneventful recovery with no reported complications. The patient's condition steadily improved, and she was discharged on Hospital Day 4.\n\nSurgery Performed\nLaparoscopic Appendectomy performed on Hospital Day 2. (Refer to Operative Summary for complete details).\n\nCondition at Discharge\nThe patient was afebrile, hemodynamically stable, tolerating oral intake well, and ambulating independently. Her post-operative recovery was uneventful.\n\nMedications on Discharge\n**No specific medications for discharge were explicitly listed in the provided notes. Information regarding analgesics, oral antibiotics (if continued), or other routine post-operative medications is not available.**\n\nAdvice on Discharge\nDiet: Advised to continue with a soft diet.\nActivity: Advised general rest and gradual mobilization as tolerated.\nFollow-up: Suture removal is advised on Day 7 post-discharge. No specific physician or clinic follow-up was documented beyond this.\nRed flags: **No specific red flags or warning signs for potential complications were explicitly detailed for the patient or their family.**\n\nPrognosis / Outcome\nRecovered, with an uneventful post-operative course.\n\nDoctor's Name & Signature\nDr. Sidharth Rajmohan\n**[Designation & Registration No. - Not provided]**	\N	generated	2	2025-06-13 07:20:29.627121+00	2025-06-13 07:20:49.621256+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/cdd16c74-04ba-46f1-8e4e-77a6e4061df2/recording_1749799220182.webm	[]	[]	***********-07-13 07:20:29.627121+00	discharge	\N	\N	Rook
24fe1278-2024-4a84-89d3-0822504aa5f6	250c5016-224e-4835-b334-d7b083eede8d	doctor	HISTOPATHOLOGY REPORT\n\nPatient Details:\n  - Name: echo\n  - Age/Sex: **[Information Not Provided]**\n  - Specimen ID: **[Information Not Provided]**\n\nSpecimen Details:\n  - Specimen Source: **[Information Not Provided]**\n  - Date Received: 08-06-2025 **(Consultation date; actual specimen receipt date not provided)**\n  - Clinical History: **[No histopathology-specific clinical history provided. The audio content describes echocardiogram findings (normal LV function, EF 60%, mild LVH, no valve diffuse, no effusion) which are not relevant to a histopathology specimen history.]**\n\nGross Description:\n  - **[Gross description not provided in source material.]**\n\nMicroscopic Description:\n  - **[Microscopic description not provided in source material. The audio content describes echocardiogram findings (normal LV function, EF 60%, mild LVH, no valve diffuse, no effusion) which are not microscopic pathology findings.]**\n\nFinal Diagnosis (with ICD-10 / ICD-O Codes):\n  - **[No histopathology diagnosis provided in source material. The audio content describes echocardiogram findings, not a histopathology diagnosis.]**\n  - **[Surgical margin status not applicable as no specimen or diagnosis is provided.]**\n\nComments:\n  - **[No comments relevant to a histopathology report are provided.]**\n\nPathologist ID:\n  - Dr. Sidharth Rajmohan	\N	generated	2	2025-06-08 08:41:45.055688+00	2025-06-08 10:57:19.401859+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/24fe1278-2024-4a84-89d3-0822504aa5f6/recording_1749372103666.webm	[]	[]	***********-07-08 08:41:45.055688+00	pathology	\N	\N	echo
0eb8bd91-ecb9-4a2a-a222-80923401c052	824fc30d-120b-42c9-b585-162c5eedce35	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Aswin\n    - Age: \n    - Gender: \n    - Date of Consultation: 18-06-2025\n    - Time: 06:19 PM\n\n  Past Medical History:\n    - No known drug allergies\n\n  Examination Findings:\n    - Vitals: BP 120/80 mmHg, Pulse 72 bpm, Temp 98.6°F, SpO2 98% on room air\n    - General Examination: Patient alert, oriented, no obvious distress\n    - Systemic Exam: \n        - Respiratory: Clear breath sounds\n        - Cardiovascular: Normal heart sounds\n        - Abdomen: Soft, non-tender\n        - Neuro: Normal reflexes\n\n  Doctor ID:\n    - Dr. Anuraj S	\N	generated	3	2025-06-18 12:49:43.056601+00	2025-06-18 12:50:04.444639+00	https://celerai.tallyup.pro/consultation-audio/824fc30d-120b-42c9-b585-162c5eedce35/0eb8bd91-ecb9-4a2a-a222-80923401c052/recording_1750250979345.webm	[]	[]	55599	2025-07-18 12:49:43.056601+00	outpatient	\N	\N	Aswin
abff91c0-fb82-4931-a005-f951cd5228a7	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Lakshmi\nAge / Sex: **Not documented** / Female\nHospital / IP No.: Bed 201\nAdmission Date: June 16, 2025 (Inferred, patient admitted for insulin regimen optimization due to uncontrolled sugars)\nDischarge Date: June 19, 2025\nConsultant / Department: Dr. Sidharth Rajmohan / Internal Medicine\n\nPresenting Complaints:\nUncontrolled blood glucose levels.\n\nHistory of Present Illness:\nPatient Lakshmi was admitted with a history of uncontrolled blood glucose levels, confirmed by a Glycated Hemoglobin (HbA1c) level of 11.2% upon admission. This admission was primarily for the optimization of her glycemic control.\n\nPast Medical / Surgical History:\nDiabetes Mellitus Type 2 (duration not specified).\nNo significant surgical history documented.\n\nAllergies:\nNo Known Drug Allergies (NKDA)\n\nPersonal / Family History:\nNot significant.\n\nExamination Findings at Admission:\nGeneral physical examination findings, vital signs (Blood Pressure, Pulse, Respiration Rate, Temperature, SpO2), and system-wise findings at admission were not explicitly documented in the provided notes. Patient was admitted for metabolic stabilization.\n\nInvestigations:\n- Glycated Hemoglobin (HbA1c): 11.2% (at admission)\nNo other laboratory or imaging results were explicitly detailed in the provided notes.\n\nFinal Diagnosis (with ICD-10 Code(s)):\n- Type 2 Diabetes Mellitus with hyperglycemia, uncontrolled (E11.65)\n\nHospital Course / Treatment Given:\nDuring the inpatient stay, the patient's insulin regimen was thoroughly reviewed and optimized. She was transitioned to a basal-bolus insulin regimen utilizing Lantus (insulin glargine) as the basal insulin and Novorapid (insulin aspart) as the prandial (bolus) insulin. Her blood glucose levels showed significant improvement with the new regimen, and they are now reportedly "much better." A diabetes educator provided comprehensive counseling regarding diabetes management, including dietary guidelines and the importance of regular glucose monitoring. The patient responded well to the medical management and education provided.\n\nSurgery Performed (if any):\nNo surgical procedure was performed during this admission.\n\nCondition at Discharge:\nThe patient is stable and afebrile. Her blood glucose levels are reported to be well-controlled on the optimized insulin regimen. She is ambulatory and tolerating an oral diet.\n\nMedications on Discharge:\n- Lantus (Insulin Glargine) – **Dosage and frequency not specified in provided notes.**\n- Novorapid (Insulin Aspart) – **Dosage and frequency not specified in provided notes.**\n\nAdvice on Discharge:\n- Diet: Strict diabetic diet control is essential. Specific dietary guidelines were provided by the diabetes educator.\n- Activity: Gradual return to usual activities; regular physical activity as advised by the treating physician.\n- Red Flags / Warning Signs: Seek immediate medical attention for symptoms such as persistent high blood sugar readings (e.g., >250 mg/dL), symptoms of hypoglycemia (e.g., sweating, tremors, confusion, dizziness), increased thirst or urination, unexplained weight loss, blurred vision, or any signs of infection.\n- Follow-up: Review with Dr. Sidharth Rajmohan in the Outpatient Department (OPD) in one week. Regular blood glucose monitoring at home as instructed.\n\nPrognosis / Outcome:\nGood prognosis with adherence to medication and lifestyle modifications. Patient is stable and showing significant improvement in glycemic control.\n\nDoctor's Name & Signature:\nDr. Sidharth Rajmohan\n**Designation & Medical Council Registration No. (Not documented)**	\N	generated	1	2025-06-19 15:06:49.271908+00	2025-06-23 07:19:08.693359+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/abff91c0-fb82-4931-a005-f951cd5228a7/recording_1750345600874.webm	[]	[]	1004637	2025-07-19 15:06:49.271908+00	discharge	\N	\N	Lakshmi
349b3621-dc56-4e66-af13-fb755cdbcfa3	250c5016-224e-4835-b334-d7b083eede8d	doctor	DERMATOLOGY CASE NOTE\n\nPatient Details:\n  - Name: Shyam\n  - Age/Sex: 24, M\n  - Date: 13-06-2025\n\nBackground (Subjective):\n  - Chief Complaint: Acne\n  - History of Present Illness (HPI): Patient is a 24-year-old male presenting with moderate acne.\n\nPast Medical History (PMH):\n  - No significant past medical history explicitly stated in the notes.\n\nObjective/Physical Exam:\n  - Description of Lesion/Rash: Moderate acne noted. **Specific lesion types (e.g., open comedones, closed comedones, papules, pustules, nodules, cysts), distribution (e.g., face, trunk), and exact severity details are not explicitly described in the consultation notes beyond "moderate acne".**\n  - Procedure Note: No procedures performed during this consultation.\n\nAssessment (Diagnosis with ICD-10 Code):\n  - 1. Acne vulgaris, moderate (L70.0)\n\nPlan (Treatment):\n  - Start Doxycycline 100mg orally once daily (OD).\n  - Start topical Adapalene plus Benzoyl Peroxide applied at night.\n  - Patient to review in four weeks.\n\nDoctor ID:\n  - Dr. Sidharth Rajmohan	\N	generated	3	2025-06-13 07:45:41.904956+00	2025-06-13 07:46:01.354011+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/349b3621-dc56-4e66-af13-fb755cdbcfa3/recording_1749800725907.webm	[]	[]	***********-07-13 07:45:41.904956+00	dermatology	\N	\N	Shyam
d24bc703-720e-4d7e-9b2a-5986546d9999	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #1\n    - Age: **Not specified**\n    - Gender: **Not specified**\n    - Date of Consultation: 02-06-2025\n    - Time: 10:39 AM\n\n  Chief Complaints:\n    - Heavy chest pain: **Duration not specified**\n\n  History of Present Illness:\n    - Patient presented with chief complaint of heavy chest pain. An X-ray was performed, which revealed findings consistent with pneumonia in the chest. Patient has also been identified with cancer, necessitating immediate transportation for further management. **Onset and progression details of chest pain are not specified. Type and stage of cancer are not specified.**\n\n  Past Medical History:\n    - Cancer: **Type and site unspecified, requiring immediate transportation.**\n    - **No other significant past medical history, including diabetes, hypertension, allergies, or surgical history, is specified.**\n\n  Examination Findings:\n    - Vitals: **Reported as "okay", but specific readings (BP, Pulse, Temp, SPO2) are not provided.**\n    - General Examination: **Not specified**\n    - Systemic Exam: \n        - Respiratory: **Not specified**\n        - Cardiovascular: **Not specified**\n        - Abdomen: **Not specified**\n        - Neuro: **Not specified**\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Pneumonia, unspecified organism (J18.9)\n    - Malignant neoplasm, unspecified site (C80.1)\n\n  Investigations Ordered:\n    - **No new investigations ordered by this physician apart from the initial X-ray which has already been performed.**\n\n  Prescription:\n    - Amoxil – **Dosage not specified** – Three times a day (TID) – For three weeks\n    - Advice: **No specific dietary, lifestyle, or red flag advice provided.**\n\n  Follow-Up Plan:\n    - Patient requires immediate transportation for cancer management.\n    - **No specific follow-up appointment with this physician is mentioned.**\n\n  Notes:\n    - Patient presenting acutely with chest pain, found to have pneumonia on X-ray.\n    - Concurrent diagnosis of cancer requiring urgent transfer for specialized care.\n\n  Doctor ID:\n    - Sidharth Rajmohan	\N	generated	1	2025-06-02 10:39:58.820414+00	2025-06-08 15:32:59.407654+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d24bc703-720e-4d7e-9b2a-5986546d9999/recording_1748860795830.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d24bc703-720e-4d7e-9b2a-5986546d9999/additional_audio_1748860821758.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d24bc703-720e-4d7e-9b2a-5986546d9999/additional_audio_1749396753596.webm"]	[]	***********-07-02 10:39:58.820414+00	outpatient	\N	\N	Patient #1
9b50bdca-4331-4aa7-87f2-f5b88eefe7fe	824fc30d-120b-42c9-b585-162c5eedce35	doctor	\N	\N	pending	4	2025-06-18 12:52:10.346464+00	2025-06-18 12:52:10.346464+00	https://celerai.tallyup.pro/consultation-audio/824fc30d-120b-42c9-b585-162c5eedce35/9b50bdca-4331-4aa7-87f2-f5b88eefe7fe/recording_1750251126846.webm	[]	[]	***********-07-18 12:52:10.346464+00	outpatient	\N	\N	Amal
f4172de1-aa41-4483-8c8e-abe7079ea9de	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Timmy\n    - Age: Not provided\n    - Gender: Not provided\n    - Date of Consultation: 25-06-2025\n    - Time: 05:57 PM\n\n  Past Medical History:\n    - No known drug allergies\n\n  Examination Findings:\n    - Vitals: BP 120/80 mmHg, Pulse 72 bpm, Temp 98.6°F, SPO2 98% on room air\n    - General Examination: Patient alert, oriented, no obvious distress\n\n  Provisional Diagnosis (with ICD-10 Code):\n    - Right Femur Fracture, Shaft, Closed - S72.301A\n\n  Doctor ID:\n    - Sidharth Rajmohan	\N	generated	13	2025-06-25 12:27:33.423085+00	2025-06-25 14:29:36.535466+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/audio.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/additional-audio.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/additional-audio.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/additional-audio.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/additional-audio.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/c0a87a42-ad46-4341-8681-9c51617dac01.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/**********-x-ray-left-thigh-ap-lat.webp", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/f4172de1-aa41-4483-8c8e-abe7079ea9de/IM-0001-0001.jpeg"]	71275	2025-07-25 12:27:33.423085+00	outpatient	\N	\N	Timmy
490f82bb-5ca4-47af-b5ed-5ce07ba32e96	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Ram\nAge / Sex: 45 Years / Male\nHospital / IP No.: Not specified\nAdmission Date: 19-06-2025\nDischarge Date: 24-06-2025\nConsultant / Department: Dr. Sidharth Rajmohan / General Surgery\n\nPresenting Complaints:\nThe patient was admitted for symptoms consistent with acute appendicitis, necessitating surgical intervention.\n\nHistory of Present Illness:\nThe patient presented with clinical signs and symptoms indicative of acute appendicitis, leading to admission on June 19, 2025, for surgical management. Further specific details regarding symptom onset, progression, or associated factors are not specified in the clinical notes.\n\nPast Medical / Surgical History:\nNo significant past medical or surgical history is specified in the clinical notes.\n\nAllergies:\nNo Known Drug Allergies (NKDA) are specified.\n\nPersonal / Family History:\nNot significant, as no specific information regarding personal habits or family medical history is provided in the clinical notes.\n\nExamination Findings at Admission:\nClinical examination findings at admission, including vital signs and systemic examination, are not specified in the provided clinical notes.\n\nInvestigations:\nDetails of laboratory, imaging, or other diagnostic investigations performed are not specified in the provided clinical notes.\n\nFinal Diagnosis (with ICD-10 Code(s)):\n- Acute Appendicitis, unspecified (K35.80)\n- Status post Laparoscopic Appendicectomy (Z98.89)\n\nHospital Course / Treatment Given:\nThe patient, a 45-year-old male, was admitted on June 19, 2025, with a diagnosis of acute appendicitis. He underwent a laparoscopic appendicectomy on the day of admission, June 19, 2025. Postoperatively, the patient experienced an uncomplicated course, with no significant post-operative issues noted. His clinical status remained stable throughout the hospitalization. He was tolerating oral intake (eating) and was fully ambulating (mobilizing) without difficulty. Given his stable condition and resolution of acute issues, the patient is deemed fit for discharge today.\n\nSurgery Performed:\nLaparoscopic Appendicectomy\nDate of Surgery: 19-06-2025\nOperating Surgeon(s): Not specified in clinical notes. Refer to detailed Operative Notes for further information.\n\nCondition at Discharge:\nThe patient is clinically stable, afebrile, tolerating a regular oral diet well, and is fully ambulatory. He has no significant post-operative complaints.\n\nMedications on Discharge:\n- **Oral Antibiotics**: A course of oral antibiotics for 5 days. **Specific antibiotic name and dosage are not specified in the clinical notes.**\n\nAdvice on Discharge:\n- Diet: Resume a regular, balanced oral diet as tolerated.\n- Activity: Gradual resumption of normal activities. Avoid strenuous physical activity, heavy lifting, or vigorous exercise for the next 2-4 weeks or as advised by the surgical team.\n- Red Flags / Warning Signs: Seek immediate medical attention if any of the following symptoms occur:\n    - Recurrence or worsening abdominal pain.\n    - Persistent fever (temperature greater than 100.4°F or 38°C).\n    - Signs of surgical site infection, including increasing redness, swelling, warmth, or purulent discharge from the incision site.\n    - Persistent nausea or vomiting.\n    - Difficulty passing stools or flatus.\n- Wound Care: Keep the surgical wound dressing clean and dry. Avoid soaking the wound in water (e.g., in bathtubs or swimming pools) until advised by the surgeon.\n- Follow-up: Review with Dr. Sidharth Rajmohan in the Outpatient Department (OPD) after one week, tentatively scheduled for 01-07-2025.\n\nPrognosis / Outcome:\nThe prognosis is good, with an expectation of full recovery following the laparoscopic appendicectomy. The patient is stable and improving at the time of discharge.\n\nDoctor's Name & Signature:\nDr. Sidharth Rajmohan\n[Designation & Medical Council Registration No.]	DISCHARGE SUMMARY\n\nPatient Details\nName: Ram\nAge / Sex: 45 Years / Male\nHospital / IP No.: Not specified\nAdmission Date: 19-06-2025\nDischarge Date: 24-06-2025\nConsultant / Department: Dr. Sidharth Rajmohan / General Surgery\n\nPresenting Complaints:\nThe patient was admitted for symptoms consistent with acute appendicitis, necessitating surgical intervention.\n\nHistory of Present Illness:\nThe patient presented with clinical signs and symptoms indicative of acute appendicitis, leading to admission on June 19, 2025, for surgical management. Further specific details regarding symptom onset, progression, or associated factors are not specified in the clinical notes.\n\nPast Medical / Surgical History:\nNo significant past medical or surgical history is specified in the clinical notes.\n\nAllergies:\nNo Known Drug Allergies (NKDA) are specified.\n\nPersonal / Family History:\nNot significant, as no specific information regarding personal habits or family medical history is provided in the clinical notes.\n\nExamination Findings at Admission:\nClinical examination findings at admission, including vital signs and systemic examination, are not specified in the provided clinical notes.\n\nInvestigations:\nDetails of laboratory, imaging, or other diagnostic investigations performed are not specified in the provided clinical notes.\n\nFinal Diagnosis (with ICD-10 Code(s)):\n- Acute Appendicitis, unspecified (K35.80)\n- Status post Laparoscopic Appendicectomy (Z98.89)\n\nHospital Course / Treatment Given:\nThe patient, a 45-year-old male, was admitted on June 19, 2025, with a diagnosis of acute appendicitis. He underwent a laparoscopic appendicectomy on the day of admission, June 19, 2025. Postoperatively, the patient experienced an uncomplicated course, with no significant post-operative issues noted. His clinical status remained stable throughout the hospitalization. He was tolerating oral intake (eating) and was fully ambulating (mobilizing) without difficulty. Given his stable condition and resolution of acute issues, the patient is deemed fit for discharge today.\n\nSurgery Performed:\nLaparoscopic Appendicectomy\nDate of Surgery: 19-06-2025\nOperating Surgeon(s): Not specified in clinical notes. Refer to detailed Operative Notes for further information.\n\nCondition at Discharge:\nThe patient is clinically stable, afebrile, tolerating a regular oral diet well, and is fully ambulatory. He has no significant post-operative complaints.\n\nMedications on Discharge:\n- **Oral Antibiotics**: A course of oral antibiotics for 5 days. **Specific antibiotic name and dosage are not specified in the clinical notes.**\n\nAdvice on Discharge:\n- Diet: Resume a regular, balanced oral diet as tolerated.\n- Activity: Gradual resumption of normal activities. Avoid strenuous physical activity, heavy lifting, or vigorous exercise for the next 2-4 weeks or as advised by the surgical team.\n- Red Flags / Warning Signs: Seek immediate medical attention if any of the following symptoms occur:\n    - Recurrence or worsening abdominal pain.\n    - Persistent fever (temperature greater than 100.4°F or 38°C).\n    - Signs of surgical site infection, including increasing redness, swelling, warmth, or purulent discharge from the incision site.\n    - Persistent nausea or vomiting.\n    - Difficulty passing stools or flatus.\n- Wound Care: Keep the surgical wound dressing clean and dry. Avoid soaking the wound in water (e.g., in bathtubs or swimming pools) until advised by the surgeon.\n- Follow-up: Review with Dr. Sidharth Rajmohan in the Outpatient Department (OPD) after one week, tentatively scheduled for 01-07-2025.\n\nPrognosis / Outcome:\nThe prognosis is good, with an expectation of full recovery following the laparoscopic appendicectomy. The patient is stable and improving at the time of discharge.\n\nDoctor's Name & Signature:\nDr. Sidharth Rajmohan\n[Designation & Medical Council Registration No.]	generated	1	2025-06-24 02:38:11.491088+00	2025-06-24 20:40:10.500764+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/490f82bb-5ca4-47af-b5ed-5ce07ba32e96/recording_1750732687528.webm	[]	[]	***********-07-24 02:38:11.491088+00	discharge	\N	\N	Ram
f6c7a787-94d3-4fcb-8c61-554351b605b8	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	14	2025-06-25 14:32:12.775232+00	2025-06-25 14:34:56.613123+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/f6c7a787-94d3-4fcb-8c61-554351b605b8/audio.webm	[]	[]	86494	2025-07-25 14:32:12.775232+00	outpatient	\N	dasd\n	yim
6e578d23-a5b7-40a3-aec1-f821bf0f91f8	250c5016-224e-4835-b334-d7b083eede8d	doctor	The provided audio dictation describes a "microscopic examination revealing a moderate differentiated colonic adenocarcinoma, stage PT1 with negative margins and no lymphovascular invasion." This content pertains to a pathology report and does not contain any echocardiogram-specific findings or measurements (e.g., LVEF, valve descriptions, chamber sizes, wall motion abnormalities).\n\nTherefore, I am unable to generate the requested echocardiogram report as there is no relevant data in the provided dictation.	\N	generated	1	2025-06-08 08:30:49.125956+00	2025-06-08 10:57:41.571432+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/6e578d23-a5b7-40a3-aec1-f821bf0f91f8/recording_1749371445381.webm	[]	[]	***********-07-08 08:30:49.125956+00	cardiology_echo	\N	\N	HIST
99cbab27-6a0b-49a9-b246-60a4c3496891	250c5016-224e-4835-b334-d7b083eede8d	doctor		\N	generated	7	2025-06-05 21:10:31.525383+00	2025-06-08 10:57:44.738158+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/99cbab27-6a0b-49a9-b246-60a4c3496891/recording_1749157830297.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/99cbab27-6a0b-49a9-b246-60a4c3496891/additional_audio_1749195631512.webm"]	[]	0	2025-07-05 21:10:31.525383+00	outpatient	\N	\N	Boii 
661a7c00-f85a-4ce8-90d8-36cdf2d5fab3	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	2	2025-06-03 08:25:50.918164+00	2025-06-08 10:57:24.22002+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/661a7c00-f85a-4ce8-90d8-36cdf2d5fab3/recording-1748939143833.wav	[]	[]	***********-07-03 08:25:50.918164+00	outpatient	\N	\N	Patient #2
76535975-d557-4c86-8d44-29c5aa32c2b8	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Geeta Sharma\nAge / Sex: Not specified in consultation notes / Female\nHospital / IP No.: Not specified in consultation notes\nAdmission Date: June 17, 2025 (inferred from consultation notes)\nDischarge Date: June 18, 2025\nConsultant / Department: Dr. Sidharth Rajmohan / Cardiology\n\nPresenting Complaints:\nAcute onset chest pain, characteristic of myocardial infarction, leading to presentation with anterior wall ST-Elevation Myocardial Infarction (STEMI).\n\nHistory of Present Illness:\nMs. Geeta Sharma presented to the hospital on June 17, 2025, with acute onset symptoms consistent with an anterior wall ST-Elevation Myocardial Infarction (STEMI). Following diagnosis, she underwent a primary percutaneous coronary intervention (PCI) to the left anterior descending (LAD) artery, during which a single stent was successfully deployed. Post-procedure, her clinical condition stabilized, and she remained pain-free.\n\nPast Medical / Surgical History:\n- Type 2 Diabetes Mellitus\n- Essential Hypertension\n- No significant past surgical history documented.\n\nAllergies:\nNo Known Drug Allergies (NKDA)\n\nPersonal / Family History:\nHistory of smoking, with noted non-compliance to cessation.\nFamily history not specified in consultation notes.\n\nExamination Findings at Admission:\nVital signs were stable upon admission. Detailed general and systemic examination findings at admission were not explicitly specified in the consultation notes, however, the patient's acute presentation necessitated prompt intervention for STEMI.\n\nInvestigations:\nDiagnostic investigations, including electrocardiogram (ECG) demonstrating ST-segment elevation in anterior leads and elevated cardiac biomarkers, were consistent with acute myocardial infarction. Coronary angiography confirmed a critical lesion in the left anterior descending (LAD) artery, necessitating primary PCI. No other specific laboratory or imaging results were detailed in the consultation notes.\n\nFinal Diagnosis (with ICD-10 Code(s)):\n- Acute ST-Elevation Myocardial Infarction of Anterior Wall (I21.0)\n- Type 2 Diabetes Mellitus (E11.9)\n- Essential (Primary) Hypertension (I10)\n\nHospital Course / Treatment Given:\nUpon admission on June 17, 2025, the patient was diagnosed with an acute anterior wall ST-Elevation Myocardial Infarction. She was immediately taken for primary Percutaneous Coronary Intervention (PCI). The procedure involved successful stenting of the Left Anterior Descending (LAD) artery with a single drug-eluting stent. Post-PCI, the patient was initiated on standard post-myocardial infarction medications, which included Dual Antiplatelet Therapy (DAPT), a high-dose statin, and a beta-blocker. Her pre-existing medications, Amlodipine and Metformin, were continued. Throughout her hospital stay, the patient remained clinically stable, afebrile, and pain-free. Her vital signs were consistently within normal limits. She tolerated oral intake well and showed good clinical recovery, indicating successful revascularization and medical management.\n\nSurgery Performed:\nPrimary Percutaneous Coronary Intervention (PCI) with single stent placement to Left Anterior Descending (LAD) artery.\nDate: June 17, 2025\nOperating Surgeon(s): **Not specified in consultation notes.** Refer to detailed Operative Notes.\n\nCondition at Discharge:\nThe patient is stable, afebrile, pain-free, and vital signs are within normal limits. She is ambulatory and tolerating oral diet well.\n\nMedications on Discharge:\n1.  **Dual Antiplatelet Therapy (DAPT)**: Specific agents (e.g., Aspirin, Clopidogrel/Ticagrelor) and dosages **not explicitly detailed in consultation notes**. Continue as prescribed by cardiology team.\n2.  **High-dose Statin**: Specific agent and dosage **not explicitly detailed in consultation notes**. Continue as prescribed by cardiology team.\n3.  **Beta-blocker**: Specific agent and dosage **not explicitly detailed in consultation notes**. Continue as prescribed by cardiology team.\n4.  Tab. Amlodipine – Dosage **not specified in consultation notes**. Continue as previously prescribed.\n5.  Tab. Metformin – Dosage **not specified in consultation notes**. Continue as previously prescribed.\n\nAdvice on Discharge:\n-   **Diet**: Adhere to a heart-healthy diet, low in sodium, saturated fats, and refined sugars. Maintain strict adherence to a diabetic diet, with regular and timely meals.\n-   **Activity**: Gradually increase physical activity as tolerated. Avoid strenuous activities, heavy lifting, and sudden exertion for at least 4-6 weeks post-procedure. Gentle, regular walks are encouraged.\n-   **Cardiac Rehabilitation**: Enroll in and actively participate in a structured cardiac rehabilitation program for comprehensive physical recovery, education, and secondary prevention strategies.\n-   **Lifestyle Modifications**: Strict adherence to healthy lifestyle practices, including complete smoking cessation (critical given history of non-compliance), regular exercise, stress management, and maintaining a healthy body weight.\n-   **Red Flags / Warning Signs**: Seek immediate medical attention or proceed to the nearest emergency department if any of the following symptoms occur:\n    *   Recurrence of chest pain, especially if severe, crushing, or radiating.\n    *   New onset or worsening shortness of breath or difficulty breathing.\n    *   Palpitations or irregular heartbeat.\n    *   Dizziness, lightheadedness, or fainting spells.\n    *   Unusual bleeding (e.g., prolonged nosebleeds, bleeding gums, black tarry stools, blood in urine) or excessive bruising, particularly while on antiplatelet therapy.\n    *   Swelling in the legs or ankles.\n-   **Follow-up**: Review with Dr. Sidharth Rajmohan or a cardiologist in the Outpatient Department (OPD). **Date of follow-up appointment not specified in consultation notes; patient to be advised by the hospital for scheduling.** Regular follow-up with an endocrinologist for ongoing diabetes management and a primary care physician for hypertension control is also advised.\n\nPrognosis / Outcome:\nThe patient's condition is stable with a good prognosis following successful coronary revascularization and initiation of optimal medical therapy. Long-term outcome is contingent upon diligent adherence to prescribed medications, lifestyle modifications, and regular medical follow-up to prevent recurrent cardiac events and manage co-morbidities.\n\nDoctor's Name & Signature:\nDr. Sidharth Rajmohan\n[Designation & Medical Council Registration No.]	\N	generated	1	2025-06-18 14:35:22.998044+00	2025-06-24 20:05:13.222181+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/76535975-d557-4c86-8d44-29c5aa32c2b8/recording_1750257316786.webm	[]	[]	1083400	2025-07-18 14:35:22.998044+00	discharge	\N	\N	pooo
9e5ae3f7-bd65-480b-98d5-4e92ed75b1c8	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	3	2025-06-03 10:58:10.206615+00	2025-06-08 10:57:29.231514+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/9e5ae3f7-bd65-480b-98d5-4e92ed75b1c8/recording-1748948256079.wav	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/9e5ae3f7-bd65-480b-98d5-4e92ed75b1c8/C.JPG"]	***********-07-03 10:58:10.206615+00	outpatient	\N	\N	Patient #3
d8c06d02-0784-4735-9f1c-019a45322094	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	1	2025-06-03 08:04:55.357877+00	2025-06-08 10:57:33.53897+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d8c06d02-0784-4735-9f1c-019a45322094/recording-1748937840424.wav	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/d8c06d02-0784-4735-9f1c-019a45322094/image.jpg"]	***********-07-03 08:04:55.357877+00	outpatient	\N	\N	Patient #1
89ff719e-89ad-417d-94ab-44371ea4438c	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	4	2025-06-03 11:00:40.096882+00	2025-06-08 10:57:38.146795+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/89ff719e-89ad-417d-94ab-44371ea4438c/recording-1748948431326.wav	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/89ff719e-89ad-417d-94ab-44371ea4438c/C.JPG"]	***********-07-03 11:00:40.096882+00	outpatient	\N	\N	Patient #4
2589c7a7-416c-4832-bfc7-b76fea05d8ab	250c5016-224e-4835-b334-d7b083eede8d	doctor	IVF CYCLE SUMMARY\n\nPatient Details:\n  - Name: IVF (Full names not provided in source data)\n  - Female Age: **Not provided**\n  - Cycle Number: 2\n  - Primary Diagnosis: Female infertility, unspecified (N97.9)\n\nProcedure Details:\n  - Procedure: Oocyte Retrieval and Embryo Cryopreservation\n  - Date of Procedure: **Not explicitly stated for Retrieval/Cryopreservation** (Consultation Date: 08-06-2025)\n\nOocyte Data:\n  - Number of Follicles Aspirated: **Not provided**\n  - Number of Oocytes Retrieved: 14\n  - Number of Mature (MII) Oocytes: **Not provided**\n  - Number of Oocytes Fertilized (ICSI/IVF): 11\n\nEmbryo Development Log:\n  - Number of Embryos on Day 3: **Not provided**\n  - Number of Embryos on Day 5 (Blastocysts): 5\n  - Number of Embryos Cryopreserved: 5 (on Day 5)\n\nEmbryo Transfer Note:\n  - Number of Embryos Transferred: **Not explicitly mentioned for this cycle; 5 blastocysts cryopreserved.**\n  - Quality/Grade of Embryos Transferred: **Not applicable/Not mentioned for this cycle**\n  - Ease of Procedure: **Not applicable/Not mentioned for this cycle**\n\nFollow-Up Instructions:\n  - Medications: **Not provided**\n  - Activity: **Not provided**\n  - Next Appointment: **Not provided**\n\nDoctor ID:\n  - Sidharth Rajmohan	\N	generated	4	2025-06-08 09:00:06.772007+00	2025-06-08 10:57:50.132956+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/2589c7a7-416c-4832-bfc7-b76fea05d8ab/recording_1749373205638.webm	[]	[]	***********-07-08 09:00:06.772007+00	ivf_cycle	\N	\N	IVF
d7e7d8be-60f3-4e56-95d4-92c9c7c90948	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\n**Patient Details**\nName: [Full Name]\nAge / Sex: 32 / F\nHospital / IP No.: [########]\nAdmission Date: [DD-MM-YYYY]\nDischarge Date: [DD-MM-YYYY]\nConsultant / Department: Dr. Sidharth Rajmohan / [Department]\n\n**Presenting Complaints**\nMild appendicitis symptoms.\n\n**History of Present Illness**\nThe patient is a 32-year-old female who presented with symptoms consistent with appendicitis. She was planned for surgical intervention within 3 days of admission.\n\n**Past Medical / Surgical History**\nNo significant past medical or surgical history reported.\n\n**Allergies**\nNo Known Drug Allergies (NKDA).\n\n**Final Diagnosis (with ICD-10 Code):**\nAppendicitis (K37) with incidental finding of a tumor during surgery.\n\n**Hospital Course / Treatment Given**\nThe patient underwent surgery for appendicitis. During the procedure, the appendix was found to be inflamed and an incidental tumor was identified and removed along with the appendix. Post-operative recovery was uneventful, and the patient remained stable throughout the hospital stay.\n\n**Condition at Discharge**\nStable. Afebrile, alert, and tolerating oral intake.\n\n**Medications on Discharge**\nNo specific discharge medications were documented in the notes.\n\n**Advice on Discharge**\n*   **Wound Care:** Keep the surgical wound clean and dry. Monitor for signs of infection such as increased redness, swelling, pain, or discharge from the wound site.\n*   **Activity:** Avoid strenuous activities and heavy lifting for the next 2-4 weeks to allow for proper healing. Gradually increase physical activity as tolerated.\n*   **Diet:** Resume a normal diet as tolerated. It is advisable to start with lighter meals if experiencing any residual nausea.\n*   **Follow-up:** Please schedule a follow-up appointment with Dr. Sidharth Rajmohan in the Outpatient Department in approximately 1-2 weeks. Please bring any pathology reports, if available, to this appointment.\n*   **Red Flags:** Seek immediate medical attention if you experience fever, worsening pain at the surgical site, significant wound discharge, persistent nausea or vomiting, or any other concerning symptoms.\n\n**Prognosis / Outcome**\nGood, with the expectation of full recovery.\n\n**Doctor's Name & Signature**\nDr. Sidharth Rajmohan\n[Designation & Registration No.]	\N	generated	4	2025-06-13 08:42:44.57706+00	2025-06-20 20:04:17.071428+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d7e7d8be-60f3-4e56-95d4-92c9c7c90948/recording_1749804160262.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/d7e7d8be-60f3-4e56-95d4-92c9c7c90948/additional_audio_1749804228127.webm"]	[]	***********-07-13 08:42:44.57706+00	discharge	\N	\N	Asma
b2937041-63a6-4ed9-836f-c4abf7614f0f	250c5016-224e-4835-b334-d7b083eede8d	doctor	OPERATIVE NOTE\n\nPatient Details:\nName: Priya\nAge / Sex: **45 Years / Male (Patient name "Priya" typically denotes female, while audio states "male". This requires clarification.)**\nHospital Number / IP No.: Not specified\n\nDate and Time of Surgery:\n**19-06-2025, Time not specified** (Based on consultation date June 24, 2025 and mention of "on 19th" for the procedure.)\n\nIndications for Surgery:\nProcedure performed for inflamed appendix, indicating a need for appendectomy.\n\nPre-operative Diagnosis:\nAcute Appendicitis (inferred from indication).\n\nPost-operative Diagnosis (with ICD-10 Code):\nAcute Appendicitis (K35.80)\n\nConsent:\n**Not specified in the provided notes.**\n\nType of Anesthesia:\nGeneral Anesthesia (GA).\n\nPositioning and Preparation:\nPatient positioned supine. Site preparation and draping **details not specified.**\n\nOperative Procedure:\n  - **Primary Procedure:** Laparoscopic Appendectomy\n  - **Surgical Approach:** Minimally invasive laparoscopic technique utilizing multiple port insertions.\n    - **Port Placement:** **Specific trocar insertion sites (e.g., umbilical, suprapubic, iliac fossae) and their respective sizes are not documented.**\n  - **Intra-abdominal Access and Exploration:** Entry into the peritoneal cavity was achieved. Initial visualization of the abdominal contents was performed. **Detailed findings of exploration beyond the appendix are not specified.**\n  - **Identification and Dissection of Appendix:** The vermiform appendix was identified. It was noted to be inflamed. The mesoappendix, containing its vascular supply, was dissected and managed.\n    - **Mesoappendix Management:** Dissection of the mesoappendix was performed to ligate its vascular pedicle. **Specific techniques for mesoappendix dissection (e.g., harmonic scalpel, electrocautery, ultrasonic dissector) and ligation (e.g., clips, sutures) are not documented.**\n  - **Appendiceal Stump Ligation and Division:** The base of the appendix was secured proximal to the cecum. The appendix was then divided.\n    - **Stump Closure Technique:** **The method used for securing the appendiceal stump (e.g., endoscopic stapler, endoloop ligation, suture ligation) is not specified.**\n  - **Specimen Removal:** The inflamed appendix was retrieved from the abdominal cavity.\n  - **Hemostasis:** Meticulous hemostasis was maintained throughout the procedure.\n    - **Hemostatic Modalities:** **Specific modalities employed for hemostasis (e.g., electrocautery, bipolar coagulation, suture ligation) are not detailed.**\n  - **Intraoperative Irrigation and Suction:** **Use of irrigation or suction during the procedure is not specified.**\n  - **Closure of Incisions:** All port site incisions were closed in layers.\n    - **Layered Closure Details:** **Specific suture materials and techniques for fascial and skin layer closure are not documented.**\n  - **Drainage:** No surgical drain was placed.\n\nIntraoperative Findings:\n1.  **Vermiform Appendix:** Identified and noted to be in a state of inflammation.\n2.  **Peritoneal Cavity:** Examination revealed the absence of any perforations or abscess formations within the abdominal cavity.\n3.  **Other Anatomical Structures:** **Comprehensive assessment or specific findings related to other intra-abdominal organs (e.g., intestines, ovaries, pelvic organs) were not explicitly documented.**\n\nIntraoperative Complications:\nNone noted.\n\nPost-operative Plan:\nMonitoring in the recovery unit. **Specific instructions regarding IV fluids, antibiotics, pain management, diet, mobilization, and wound care are not specified.**\n\nCondition at End of Procedure:\nPatient was stable and shifted to the recovery unit.\n\nSpecimen Sent for HPE:\nYes, inflamed appendix sent for Histopathological Examination.\n\nSignatures:\nOperating Surgeon: Dr. Sidharth Rajmohan, **[Designation and Registration Number not specified]**\nAssistant Surgeon(s): **Not specified.**\nAnesthetist: **Not specified.**	OPERATIVE NOTE\n\nPatient Details:\nName: Priya\nAge / Sex: **45 Years / Male (Patient name "Priya" typically denotes female, while audio states "male". This requires clarification.)**\nHospital Number / IP No.: Not specified\n\nDate and Time of Surgery:\n**19-06-2025, Time not specified** (Based on consultation date June 24, 2025 and mention of "on 19th" for the procedure.)\n\nIndications for Surgery:\nProcedure performed for inflamed appendix, indicating a need for appendectomy.\n\nPre-operative Diagnosis:\nAcute Appendicitis (inferred from indication).\n\nPost-operative Diagnosis (with ICD-10 Code):\nAcute Appendicitis (K35.80)\n\nConsent:\n**Not specified in the provided notes.**\n\nType of Anesthesia:\nGeneral Anesthesia (GA).\n\nPositioning and Preparation:\nPatient positioned supine. Site preparation and draping **details not specified.**\n\nOperative Procedure:\n  - **Primary Procedure:** Laparoscopic Appendectomy\n  - **Surgical Approach:** Minimally invasive laparoscopic technique utilizing multiple port insertions.\n    - **Port Placement:** **Specific trocar insertion sites (e.g., umbilical, suprapubic, iliac fossae) and their respective sizes are not documented.**\n  - **Intra-abdominal Access and Exploration:** Entry into the peritoneal cavity was achieved. Initial visualization of the abdominal contents was performed. **Detailed findings of exploration beyond the appendix are not specified.**\n  - **Identification and Dissection of Appendix:** The vermiform appendix was identified. It was noted to be inflamed. The mesoappendix, containing its vascular supply, was dissected and managed.\n    - **Mesoappendix Management:** Dissection of the mesoappendix was performed to ligate its vascular pedicle. **Specific techniques for mesoappendix dissection (e.g., harmonic scalpel, electrocautery, ultrasonic dissector) and ligation (e.g., clips, sutures) are not documented.**\n  - **Appendiceal Stump Ligation and Division:** The base of the appendix was secured proximal to the cecum. The appendix was then divided.\n    - **Stump Closure Technique:** **The method used for securing the appendiceal stump (e.g., endoscopic stapler, endoloop ligation, suture ligation) is not specified.**\n  - **Specimen Removal:** The inflamed appendix was retrieved from the abdominal cavity.\n  - **Hemostasis:** Meticulous hemostasis was maintained throughout the procedure.\n    - **Hemostatic Modalities:** **Specific modalities employed for hemostasis (e.g., electrocautery, bipolar coagulation, suture ligation) are not detailed.**\n  - **Intraoperative Irrigation and Suction:** **Use of irrigation or suction during the procedure is not specified.**\n  - **Closure of Incisions:** All port site incisions were closed in layers.\n    - **Layered Closure Details:** **Specific suture materials and techniques for fascial and skin layer closure are not documented.**\n  - **Drainage:** No surgical drain was placed.\n\nIntraoperative Findings:\n1.  **Vermiform Appendix:** Identified and noted to be in a state of inflammation.\n2.  **Peritoneal Cavity:** Examination revealed the absence of any perforations or abscess formations within the abdominal cavity.\n3.  **Other Anatomical Structures:** **Comprehensive assessment or specific findings related to other intra-abdominal organs (e.g., intestines, ovaries, pelvic organs) were not explicitly documented.**\n\nIntraoperative Complications:\nNone noted.\n\nPost-operative Plan:\nMonitoring in the recovery unit. **Specific instructions regarding IV fluids, antibiotics, pain management, diet, mobilization, and wound care are not specified.**\n\nCondition at End of Procedure:\nPatient was stable and shifted to the recovery unit.\n\nSpecimen Sent for HPE:\nYes, inflamed appendix sent for Histopathological Examination.\n\nSignatures:\nOperating Surgeon: Dr. Sidharth Rajmohan, **[Designation and Registration Number not specified]**\nAssistant Surgeon(s): **Not specified.**\nAnesthetist: **Not specified.**	generated	2	2025-06-24 02:41:09.307026+00	2025-06-24 20:52:51.833178+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/b2937041-63a6-4ed9-836f-c4abf7614f0f/recording_1750732865314.webm	[]	[]	***********-07-24 02:41:09.307026+00	surgery	\N	\N	priya 
669141b2-3d9e-4789-98da-68e3d30ea4d4	250c5016-224e-4835-b334-d7b083eede8d	doctor	\N	\N	pending	15	2025-06-25 14:46:12.308029+00	2025-06-25 17:08:08.501627+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/669141b2-3d9e-4789-98da-68e3d30ea4d4/audio.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/669141b2-3d9e-4789-98da-68e3d30ea4d4/additional-audio.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/669141b2-3d9e-4789-98da-68e3d30ea4d4/additional-audio.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/669141b2-3d9e-4789-98da-68e3d30ea4d4/additional-audio.webm"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/669141b2-3d9e-4789-98da-68e3d30ea4d4/**********-x-ray-left-thigh-ap-lat.webp"]	***********-07-25 14:46:12.308029+00	outpatient	\N	\N	Jim bo max double triple
c303bfec-6b01-4660-9114-ae9743190aad	250c5016-224e-4835-b334-d7b083eede8d	doctor	DISCHARGE SUMMARY\n\nPatient Details\nName: Sid\nAge / Sex: 9 Years / Male\nHospital / IP No.: Not provided in source material\nAdmission Date: Not provided in source material\nDischarge Date: Not provided in source material\nConsultant / Department: Dr. Sidharth Rajmohan / Pediatrics\n\nPresenting Complaints:\nDecreased activity, decreased oral intake, and decreased urine output. The patient also had a history of cough, cold, and fever for three days prior to admission, with an increased breathing pattern noted by parents for two days prior to admission.\n\nHistory of Present Illness:\nThe patient is a 9-year-old male child who presented with a constellation of symptoms including reduced physical activity, diminished oral fluid and food intake, and decreased urine output. This clinical presentation was preceded by a three-day history of cough, common cold symptoms, and fever. Furthermore, for two days prior to admission, the parents observed an abnormal increase in the child's breathing pattern. These symptoms led to the child's admission to the pediatric ward.\n\nPast Medical / Surgical History:\nNot provided in the source material.\n\nAllergies:\nNot provided in the source material.\n\nPersonal / Family History:\nNot provided in the source material.\n\nExamination Findings at Admission:\nNot provided in the source material.\n\nInvestigations:\nNot provided in the source material.\n\nFinal Diagnosis (with ICD-10 Code(s)):\nPneumonia, unspecified organism (J18.9)\n\nHospital Course / Treatment Given:\nThe patient was admitted to the pediatric ward and managed with intravenous antibiotics, intravenous fluids, and nebulization therapy. The child demonstrated clinical improvement and symptomatic relief by the third day of hospitalization.\n\nSurgery Performed (if any):\nNone.\n\nCondition at Discharge:\nThe patient's condition improved symptomatically and clinically, deemed satisfactory for discharge.\n\nMedications on Discharge:\nNot provided in the source material.\n\nAdvice on Discharge:\nDiet: Not provided in the source material.\nActivity: Not provided in the source material.\nRed Flags / Warning Signs: Not provided in the source material.\nFollow-up: Not provided in the source material.\n\nPrognosis / Outcome:\nGood.\n\nDoctor's Name & Signature:\nDr. Sidharth Rajmohan\nDesignation & Medical Council Registration No.: Not provided in source material.	\N	generated	3	2025-06-24 15:38:12.750013+00	2025-06-25 09:33:46.232914+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/c303bfec-6b01-4660-9114-ae9743190aad/recording_1750779488241.webm	[]	[]	***********-07-24 15:38:12.750013+00	discharge	\N	\N	Sid
4a09c803-adea-4c62-b7c1-3d7d5bfdcf32	250c5016-224e-4835-b334-d7b083eede8d	doctor	Okay, here is the consultation summary based on the provided audio and images:\n\n**Chief Complaint:**\nArm infection\n\n**Diagnosis:**\nNot specified\n\n**Medications:**\nNot mentioned\n\n**Follow-up:**\nThe patient is being sent for an MRI.\n\n**Image Analysis:**\nImage shows an x-ray of what appears to be a fractured femur bone.	Okay, here is the consultation summary based on the provided audio and images:\n\n**Chief Complaint:**\nArm infection\n\n**Diagnosis:**\nNot specified\n\n**Medications:**\nNot mentioned\n\n**Follow-up:**\nThe patient is being sent for an MRI.\n\n**Image Analysis:**\nImage shows an x-ray of what appears to be a fractured femur bone.	approved	3	2025-05-28 16:40:25.068392+00	2025-06-08 10:52:32.495573+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/4a09c803-adea-4c62-b7c1-3d7d5bfdcf32/recording_1748450411662.webm	[]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/4a09c803-adea-4c62-b7c1-3d7d5bfdcf32/IM-0001-0001.jpeg", "https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/4a09c803-adea-4c62-b7c1-3d7d5bfdcf32/**********-x-ray-left-thigh-ap-lat.webp"]	***********-06-27 16:40:25.068392+00	outpatient	\N	\N	Patient #3
b18a6634-ac2e-405a-afa6-0448664d3ccd	250c5016-224e-4835-b334-d7b083eede8d	doctor	Consultation Summary:\n  Patient Details:\n    - Name: Patient #6\n    - Age: **[Not specified]**\n    - Gender: **[Not specified]**\n    - Date of Consultation: 29-05-2025\n    - Time: 05:18 PM\n\n  Chief Complaints:\n    - **[Not explicitly stated]**\n\n  History of Present Illness:\n    - **[Not explicitly stated]**\n\n  Past Medical History:\n    - **[Not explicitly stated]**\n\n  Examination Findings:\n    - Vitals: BP **[Not specified]**, Pulse **[Not specified]**, Temp **[Not specified]**, SPO2 **[Not specified]**\n    - General Examination: **[Not specified]**\n    - Systemic Exam:\n        - Respiratory: Chest X-ray (CXR) shows diffuse, bilateral reticulonodular opacities, particularly prominent in the mid-to-lower lung zones. The cardiac silhouette is within normal limits. No overt pleural effusion or pneumothorax observed.\n        - Cardiovascular: **[Not specified]**\n        - Abdomen: **[Not specified]**\n        - Neuro: **[Not specified]**\n\n  Provisional Diagnosis:\n    - **[Not explicitly stated]** (Imaging findings consistent with diffuse infiltrative lung process)\n\n  Investigations Ordered:\n    - **[No new investigations explicitly ordered]** (CXR provided as existing imaging)\n\n  Prescription:\n    - **[No medications prescribed]**\n    - Advice: **[No specific advice given]**\n\n  Follow-Up Plan:\n    - Patient being shifted to Ward number 4, then subsequently shifted to Ward number 8 and will remain there.\n    - **[No specific outpatient follow-up instructions given]**\n\n  Notes:\n    - Patient transfer for ongoing care: initially shifted to Ward number 4, then to Ward number 8.\n\n  Doctor ID:\n    - **[Not specified]**	\N	generated	6	2025-05-29 17:18:44.633374+00	2025-06-08 10:57:59.296364+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/recording_1748539119378.webm	["https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/additional_audio_1748540009086.webm", "https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/additional-audio-1748644148606.wav"]	["https://celerai.tallyup.pro/consultation-images/250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/IM-0001-0001.jpeg"]	***********-06-28 17:18:44.633374+00	outpatient	\N	\N	Patient #6
7aa562c7-fcaa-4b47-a3ea-5cb1d6a7e23d	250c5016-224e-4835-b334-d7b083eede8d	doctor	OPERATIVE NOTE\n\nPatient Details:\n- Name: Sid **(Full name not specified)**\n- Age / Sex: 31 / F\n- Hospital Number / IP: **Not specified**\n\nSurgery Details:\n- Date and Time of Surgery: 13-06-2025, **Start: Not specified**, End: **Not specified**\n- Operating Surgeon: Dr. Sidharth Rajmohan\n- Assistant(s): **Not specified**\n- Anesthetist: **Not specified**\n\nPre-operative Diagnosis:\nFetal Distress\n\nPost-operative Diagnosis (with ICD-10 Code):\nFetal Distress requiring Caesarean Section (ICD-10: O36.59)\n\nProcedure Performed:\nEmergency Lower Segment Caesarean Section\n\nIndications for Surgery:\nThe patient is a 31-year-old primigravida at 39 weeks gestation who presented with fetal distress, necessitating urgent delivery. No significant comorbidities were reported.\n\nConsent:\nWritten and informed consent was obtained **(Note: Consent not explicitly mentioned in dictation; standard practice assumed)**.\n\nAnesthesia:\nSpinal anesthesia was administered. **(Note: Details of anesthetic agents, dosage, and any intra-anesthetic events not specified in dictation)**.\n\nSurgical Safety Checklist:\nWHO checklist protocols were followed **(Note: Checklist completion not explicitly mentioned in dictation; standard practice assumed)**.\n\nPositioning and Preparation:\nPatient was placed in the supine position. The abdomen was prepped and draped in a sterile fashion **(Note: Specific details of preparation agents, draping method, and positioning not explicitly mentioned in dictation; standard practice assumed)**.\n\nOperative Procedure:\nUnder spinal anesthesia, the patient underwent an emergency Lower Segment Caesarean Section due to fetal distress. A low abdominal incision was made, and the abdominal cavity was entered. The uterus was identified, and a transverse incision was made in the lower uterine segment. The baby was delivered via gentle extraction. The umbilical cord was clamped and divided. The placenta was delivered **(Method of placental delivery: not specified)**. Following delivery of the baby, the uterine incision was repaired in two layers using absorbable sutures **(Suture type and size for uterine repair: not specified)**. The peritoneal cavity was irrigated with normal saline **(Irrigation fluid volume: not specified)**. No drain was placed. The abdominal wall layers were approximated and closed with sutures **(Suture types and layers: not specified)**. Hemostasis was secured throughout the procedure **(Specific methods for hemostasis: not specified)**. The skin was closed **(Suture type for skin closure: not specified)**.\n\nIntraoperative Findings:\n*   Fetal distress noted, requiring urgent delivery.\n*   A male infant weighing 3.1 kg was delivered with good cry **(Specific Apgar scores: not provided)**.\n\nIntraoperative Complications:\n*   No intraoperative complications noted.\n\nEstimated Blood Loss (EBL):\n*   Not specified, presumed minimal.\n\nSpecimen Sent for Histopathological Examination (HPE):\n*   No.\n\nPost-operative Plan:\n*   Not specified in dictation. **(Standard post-operative care for LSCS would include monitoring, pain management, fluid management, and antibiotics, but this was not dictated.)**\n\nCondition at End of Procedure:\n*   Mother: Stable **(Note: Specific maternal vital signs or status beyond 'stable' not detailed in dictation)**.\n*   Infant: Male, 3.1 kg, with good cry.\n\n---\n**Dr. Sidharth Rajmohan**\n*Operating Surgeon*	\N	generated	5	2025-06-13 09:03:04.894886+00	2025-06-23 09:09:00.355067+00	https://celerai.tallyup.pro/consultation-audio/250c5016-224e-4835-b334-d7b083eede8d/7aa562c7-fcaa-4b47-a3ea-5cb1d6a7e23d/recording_1749805377454.webm	[]	[]	444577	2025-07-13 09:03:04.894886+00	surgery	\N	\N	Sid
\.


--
-- Data for Name: contact_requests; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.contact_requests (id, doctor_id, doctor_name, doctor_email, clinic_name, phone_number, message, status, created_at, updated_at, request_type, contacted_at, resolved_at, current_quota_used, monthly_quota, subject) FROM stdin;
4117f906-1419-4452-b97c-f1fa422446e3	250c5016-224e-4835-b334-d7b083eede8d	Sidharth Rajmohan	<EMAIL>	Admin	+918921682177	Contact request from dashboard - User: Sidharth Rajmohan (<EMAIL>)	resolved	2025-06-08 14:59:28.240112+00	2025-06-08 15:03:14.730593+00	general_contact	\N	\N	12	200	general
00620118-a317-4133-8489-68404d9e1a06	250c5016-224e-4835-b334-d7b083eede8d	Sidharth Rajmohan	<EMAIL>	Admin	+918921682177	Subject: general\n\nMessage: seas	pending	2025-06-16 21:58:27.542426+00	2025-06-16 21:58:27.542426+00	general_contact	\N	\N	12	200	general
abceb520-a4af-4988-9ec3-f7c7cbcdff5a	250c5016-224e-4835-b334-d7b083eede8d	Sidharth Rajmohan	<EMAIL>	Admin	+918921682177	Subject: general\n\nMessage: simply	pending	2025-06-19 20:26:39.17009+00	2025-06-19 20:26:39.17009+00	general_contact	\N	\N	12	200	general
\.


--
-- Data for Name: doctors; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.doctors (id, email, password_hash, name, phone, clinic_name, created_at, updated_at, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, referral_code, referred_by, conversion_date, referral_discount_earned, total_referrals, successful_referrals, current_plan_id, billing_status, trial_ends_at, last_payment_date, next_billing_date, available_discount_amount) FROM stdin;
80f5390e-eb98-49fa-8b79-1c2807c17c24	<EMAIL>	$2b$10$M0Us/fTILdaiP7ZIIeje5e02j3zRxp8vdvMZIwRQzDFOYb9Up.7CW	dr. serah	**********	ak	2025-06-19 14:51:57.392091+00	2025-06-19 14:53:43.213378+00	50	0	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-06-19 14:53:42.713+00	drsera872f	\N	\N	0.00	0	0	\N	trial	2025-06-26 14:51:57.392091+00	\N	\N	0.00
6335b0d7-b4dd-46cc-86b9-c498fc36f4f2	<EMAIL>	$2b$10$OqtqptLJlAibXr47J.rh4.2lj6EB6hHVG1EiH4Zj26nWQYziWwrWC	Ganesh S Nath	**********	Lady Harding Medical College	2025-06-09 13:11:14.004036+00	2025-06-09 13:11:21.006232+00	50	0	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-06-09 13:11:20.524+00	anesh3862	\N	\N	0.00	0	0	\N	trial	2025-06-16 13:11:14.004036+00	\N	\N	0.00
ea3ef7f6-7bc5-42e7-96fa-5ab8a9a584f8	<EMAIL>	$2b$10$x1ODM0ofkwE.WlE2Vy/ULu8e1WGro8Sh4bXc7n0eV8U6vKB0toUcC	strestest	**********	ram	2025-06-10 09:55:51.73428+00	2025-06-17 14:50:06.158363+00	50	0	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-06-17 14:50:05.674+00	strestese505	\N	\N	0.00	0	0	\N	trial	2025-06-17 09:55:51.73428+00	\N	\N	0.00
b85e03d7-88ad-4c20-9344-d5738fa0e4e3	<EMAIL>	$2b$10$muYIbgrhyx3dIo5VvWfC9.edJIZIongrzzJT1XymDVWRaEYB/9nxS	Thomas Jacob	**********	Thomasco	2025-06-17 14:57:02.071348+00	2025-06-17 14:57:38.071271+00	50	0	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-06-17 14:57:37.629+00	homasd98c	\N	\N	0.00	0	0	\N	trial	2025-06-24 14:57:02.071348+00	\N	\N	0.00
824fc30d-120b-42c9-b585-162c5eedce35	<EMAIL>	$2b$10$cTWtcglqyKOvGFOIV.iIFuOu11lDcMcTOuAJSYBMbLr0c3ZzGICMe	Anuraj S	7356510070	Digitalsphere.agency	2025-06-18 12:31:59.255342+00	2025-06-18 12:32:21.035572+00	50	0	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-06-18 12:32:20.574+00	nuraj3ded	\N	\N	0.00	0	0	\N	trial	2025-06-25 12:31:59.255342+00	\N	\N	0.00
250c5016-224e-4835-b334-d7b083eede8d	<EMAIL>	$2b$10$2N0pdjVXRGQnJrPhdmloWe7GOWdbPB47WKN4dqXcMXIQsIxmV5Q4W	Sidharth Rajmohan	+918921682177	Admin	2025-05-27 07:30:45.80474+00	2025-06-25 14:29:09.116444+00	200	26	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-05-29 10:39:44.571+00	idharthb159	\N	\N	0.00	0	0	\N	active	2025-06-06 20:08:01.686269+00	2025-05-30 21:32:33.258203+00	2025-06-30 21:29:14.206+00	0.00
de24aafa-ec69-4a98-b19f-23af325bc6de	<EMAIL>	$2b$10$YGusU7CsT9ldlghQCFB5MOwoO8PzEi4N/Zsg5zmOFjq3b8YxuiXYy	Arun Ramesh	9035040028	Crescent hospital feroke	2025-06-25 15:31:13.211398+00	2025-06-25 15:31:25.549647+00	50	0	2025-07-01 00:00:00+00	t	ac391977-b591-472f-bf02-a803782c4241	2025-06-25 15:31:25.086+00	runam774b	\N	\N	0.00	0	0	\N	trial	2025-07-02 15:31:13.211398+00	\N	\N	0.00
\.


--
-- Data for Name: referral_analytics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.referral_analytics (id, referrer_id, referred_doctor_id, referral_code, signup_date, conversion_date, discount_earned, status, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: referral_discounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.referral_discounts (id, doctor_id, referral_analytics_id, discount_percentage, discount_amount, original_amount, applied_to_transaction_id, status, valid_until, applied_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: usage_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.usage_logs (id, doctor_id, consultation_id, action_type, quota_before, quota_after, metadata, created_at) FROM stdin;
5b100bf6-6a30-4c19-9d54-ce0b291c4b0d	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_update	\N	150	{"reason": "Admin update", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-27 09:54:24.552201+00
1c3d0ea2-100d-4693-9e0d-9c3524d36034	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	0	1	{"timestamp": "2025-05-27T09:57:05.049662+00:00"}	2025-05-27 09:57:05.049662+00
2d9e1d68-df8e-4128-bba2-2fa94d455dcd	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	1	2	{"timestamp": "2025-05-27T12:26:46.500854+00:00"}	2025-05-27 12:26:46.500854+00
c7daac77-6daa-496b-a5e9-2c9d724d6f2e	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	2	3	{"timestamp": "2025-05-27T16:22:48.879171+00:00"}	2025-05-27 16:22:48.879171+00
be274704-fa0e-4363-a6db-1b1707d8dd99	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	3	4	{"timestamp": "2025-05-27T19:03:36.099212+00:00"}	2025-05-27 19:03:36.099212+00
0d51e563-1593-41c7-bfdd-951d08a2607a	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	4	5	{"timestamp": "2025-05-27T19:25:17.407081+00:00"}	2025-05-27 19:25:17.407081+00
c6073ef7-8882-4c72-98b9-85aabe20336b	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	5	6	{"timestamp": "2025-05-27T19:29:20.974461+00:00"}	2025-05-27 19:29:20.974461+00
7213c93c-7dcc-41f5-b223-7333a0bebb00	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_reset	6	0	{"reason": "Manual admin reset", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-27 21:04:50.454729+00
3d7b8db1-8345-4e1a-b4d3-111c0ba9a82c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	0	1	{"timestamp": "2025-05-28T09:28:04.056051+00:00"}	2025-05-28 09:28:04.056051+00
c0deec53-f4ba-4687-b56c-a976e631500e	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	1	2	{"timestamp": "2025-05-28T10:23:03.600309+00:00"}	2025-05-28 10:23:03.600309+00
9e420ee2-6a29-4dea-b88a-6071882266e7	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	2	3	{"timestamp": "2025-05-28T10:23:41.888309+00:00"}	2025-05-28 10:23:41.888309+00
98b83120-8316-46e2-9e9b-fee58c7a2ab3	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	3	4	{"timestamp": "2025-05-28T10:35:44.664596+00:00"}	2025-05-28 10:35:44.664596+00
ecd9286a-95db-4cf3-aeba-ba046b4aa666	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	4	5	{"timestamp": "2025-05-28T10:49:10.51431+00:00"}	2025-05-28 10:49:10.51431+00
ffeddeb6-8955-4a79-9064-8c184a18d4ad	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	5	6	{"timestamp": "2025-05-28T10:50:59.324645+00:00"}	2025-05-28 10:50:59.324645+00
9eaa048c-ba4f-47ee-928c-10aaa4c0ecb6	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	6	7	{"timestamp": "2025-05-28T11:01:33.57853+00:00"}	2025-05-28 11:01:33.57853+00
c6132071-08d8-448e-a992-24f0b4df7041	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	7	8	{"timestamp": "2025-05-28T11:11:34.304005+00:00"}	2025-05-28 11:11:34.304005+00
7c8b2788-1fb0-4175-a5d4-c73388baa363	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	8	9	{"timestamp": "2025-05-28T11:15:55.553263+00:00"}	2025-05-28 11:15:55.553263+00
dc576f5c-79c6-48da-8bd9-8c1f3a1177b8	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	9	10	{"timestamp": "2025-05-28T11:18:25.26788+00:00"}	2025-05-28 11:18:25.26788+00
5e51dad2-014a-47dc-9f1a-f6ee652e178c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	10	11	{"timestamp": "2025-05-28T11:33:42.440214+00:00"}	2025-05-28 11:33:42.440214+00
546f9a46-eb3a-425c-b74f-c8331ca6094f	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	11	12	{"timestamp": "2025-05-28T11:42:28.675304+00:00"}	2025-05-28 11:42:28.675304+00
e573c60d-bf2c-4057-803e-f63a5258d232	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	12	13	{"timestamp": "2025-05-28T11:51:13.692137+00:00"}	2025-05-28 11:51:13.692137+00
e2e922ef-8d46-424a-b08d-5c127ca224d4	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	13	14	{"timestamp": "2025-05-28T11:53:24.332249+00:00"}	2025-05-28 11:53:24.332249+00
89cd924a-2a41-476c-b35e-3f04f259b491	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	14	15	{"timestamp": "2025-05-28T11:56:53.717751+00:00"}	2025-05-28 11:56:53.717751+00
ffeb983c-9136-4801-87cc-1beaa155ed89	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	15	16	{"timestamp": "2025-05-28T12:03:43.230641+00:00"}	2025-05-28 12:03:43.230641+00
8d5c941a-f2ac-4a9a-b453-375b0b3a6765	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	16	17	{"timestamp": "2025-05-28T12:12:51.627309+00:00"}	2025-05-28 12:12:51.627309+00
39d010ff-81e7-4b16-ac92-97d2007b7179	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	17	18	{"timestamp": "2025-05-28T12:14:49.952643+00:00"}	2025-05-28 12:14:49.952643+00
fc099776-020e-48ce-b3a3-3e354f4c9d76	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	18	19	{"timestamp": "2025-05-28T14:44:10.760737+00:00"}	2025-05-28 14:44:10.760737+00
f6cd5981-e68b-4685-9e30-7c46374c4815	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	19	20	{"timestamp": "2025-05-28T14:44:58.203928+00:00"}	2025-05-28 14:44:58.203928+00
9a3426d3-39bf-4c83-81e8-fbbd9c892901	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	20	21	{"timestamp": "2025-05-28T14:45:37.469831+00:00"}	2025-05-28 14:45:37.469831+00
bded3d44-97ae-4475-9c02-ade1680d6dd1	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	21	22	{"timestamp": "2025-05-28T14:46:06.954754+00:00"}	2025-05-28 14:46:06.954754+00
faa36dc3-43e6-4065-8210-9346a8a596ad	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	22	23	{"timestamp": "2025-05-28T14:52:31.943998+00:00"}	2025-05-28 14:52:31.943998+00
4b18d750-7a8e-47e1-8cc7-fb9a391ab292	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	23	24	{"timestamp": "2025-05-28T14:58:07.661765+00:00"}	2025-05-28 14:58:07.661765+00
11f7fdde-cd65-4a47-835e-adce3004a2a8	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	24	25	{"timestamp": "2025-05-28T15:02:27.404478+00:00"}	2025-05-28 15:02:27.404478+00
4645ae1d-d705-48b1-a091-b928f0be8f08	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	25	26	{"timestamp": "2025-05-28T15:18:32.811516+00:00"}	2025-05-28 15:18:32.811516+00
5b6b2c99-5d4e-4ecc-b7b1-01a976b308fc	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	26	27	{"timestamp": "2025-05-28T16:34:28.065735+00:00"}	2025-05-28 16:34:28.065735+00
6d2dce45-9991-49a4-b9b8-48a9e39bc088	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	27	28	{"timestamp": "2025-05-28T18:59:51.595934+00:00"}	2025-05-28 18:59:51.595934+00
1689e420-110d-49bb-af14-f067928a0ee0	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	28	29	{"timestamp": "2025-05-28T19:00:40.031749+00:00"}	2025-05-28 19:00:40.031749+00
f621588f-fbe0-4ad8-be5b-42e5e10d4c1c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	29	30	{"timestamp": "2025-05-28T19:38:35.732893+00:00"}	2025-05-28 19:38:35.732893+00
40c8cb06-54bf-41ee-bd52-b9c932aaa781	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	30	31	{"timestamp": "2025-05-28T19:39:29.982+00:00"}	2025-05-28 19:39:29.982+00
5c18f903-b880-4b19-a905-5528fe3ed13b	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	31	32	{"timestamp": "2025-05-28T19:52:45.386962+00:00"}	2025-05-28 19:52:45.386962+00
d23da68c-4048-4238-a0c4-7bd5ed37c26e	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	32	33	{"timestamp": "2025-05-28T19:55:14.573718+00:00"}	2025-05-28 19:55:14.573718+00
696344a9-1461-475f-b7e8-eed9c56e332c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	33	34	{"timestamp": "2025-05-28T19:56:57.651048+00:00"}	2025-05-28 19:56:57.651048+00
96a64f71-463e-4c13-a943-92c79bece993	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	34	35	{"timestamp": "2025-05-28T21:28:15.380656+00:00"}	2025-05-28 21:28:15.380656+00
f000f0b5-288c-41d9-af45-6911a33aaf95	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	35	36	{"timestamp": "2025-05-28T22:07:58.450119+00:00"}	2025-05-28 22:07:58.450119+00
e65a582b-57ad-46b5-a059-cc1c6c7abdda	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	36	37	{"timestamp": "2025-05-28T22:10:00.371715+00:00"}	2025-05-28 22:10:00.371715+00
bc53b553-3028-445a-a5e7-b9a60559e4ce	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	37	38	{"timestamp": "2025-05-28T22:40:32.244701+00:00"}	2025-05-28 22:40:32.244701+00
ea46b7c6-bbaf-4d5e-8cfe-10c4bdee1abc	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	38	39	{"timestamp": "2025-05-28T22:57:45.24627+00:00"}	2025-05-28 22:57:45.24627+00
6dd741c7-ca43-476c-9521-78d19bf53042	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	39	40	{"timestamp": "2025-05-29T07:19:23.891084+00:00"}	2025-05-29 07:19:23.891084+00
2680d75e-bd2d-4c9b-a055-d84c7084d442	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	40	41	{"timestamp": "2025-05-29T07:20:39.637584+00:00"}	2025-05-29 07:20:39.637584+00
89eb415b-a06b-4131-85f9-ed4f147f0b79	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	41	42	{"timestamp": "2025-05-29T07:24:29.994224+00:00"}	2025-05-29 07:24:29.994224+00
009993a6-f190-4dc4-8fdf-1bd789448863	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	42	43	{"timestamp": "2025-05-29T07:25:04.032994+00:00"}	2025-05-29 07:25:04.032994+00
077f9036-1ef5-413d-b322-992aca22a739	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	43	44	{"timestamp": "2025-05-29T07:25:54.581308+00:00"}	2025-05-29 07:25:54.581308+00
11db96dd-1cb3-48b7-a8e7-a7c56447ab3a	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	44	45	{"timestamp": "2025-05-29T07:28:25.056472+00:00"}	2025-05-29 07:28:25.056472+00
ccb83067-4b39-4f6e-a3d7-dc80a08556c9	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	45	46	{"timestamp": "2025-05-29T07:29:07.246799+00:00"}	2025-05-29 07:29:07.246799+00
ae6734bf-702e-4abe-a91b-67a0857d023d	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	46	47	{"timestamp": "2025-05-29T08:18:41.373738+00:00"}	2025-05-29 08:18:41.373738+00
87ed9be4-9e3d-48a5-8f97-01f0405ac93e	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	47	48	{"timestamp": "2025-05-29T08:19:39.275894+00:00"}	2025-05-29 08:19:39.275894+00
f02fa99a-6f21-42a9-9967-d47d78b36ae1	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	48	49	{"timestamp": "2025-05-29T08:25:58.294019+00:00"}	2025-05-29 08:25:58.294019+00
118a6ab2-b3e4-4f68-9305-b58e829c7e49	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	49	50	{"timestamp": "2025-05-29T10:18:31.132713+00:00"}	2025-05-29 10:18:31.132713+00
8bf827e8-d137-4e88-b257-3119c22d89ad	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_reset	50	0	{"reason": "Manual admin reset", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-29 10:36:16.666098+00
d086efcc-9557-4779-84c6-6673eeadea36	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	0	1	{"timestamp": "2025-05-29T11:15:57.88917+00:00"}	2025-05-29 11:15:57.88917+00
727718dc-1772-455a-82d2-69d7452c2602	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	1	2	{"timestamp": "2025-05-29T11:56:08.361885+00:00"}	2025-05-29 11:56:08.361885+00
ec445ec5-eb09-47c2-9568-bab11d6c32e8	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	2	3	{"timestamp": "2025-05-29T11:56:25.115647+00:00"}	2025-05-29 11:56:25.115647+00
97f914e7-5801-4671-9bbf-77ad7bb883c5	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	3	4	{"timestamp": "2025-05-29T11:57:13.493838+00:00"}	2025-05-29 11:57:13.493838+00
52d80bff-d6b7-434b-b72b-3143780db9f8	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	4	5	{"timestamp": "2025-05-29T11:58:15.735514+00:00"}	2025-05-29 11:58:15.735514+00
6260d4ee-800a-4686-9ff6-17669525c983	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	5	6	{"timestamp": "2025-05-29T12:15:54.800574+00:00"}	2025-05-29 12:15:54.800574+00
0bb10552-e6d1-4fb8-881d-05d4e6d6aece	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	6	7	{"timestamp": "2025-05-29T12:37:19.897822+00:00"}	2025-05-29 12:37:19.897822+00
1f9cd57f-4958-4bf1-b4e1-e50a791f02a9	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_reset	7	0	{"reason": "Manual admin reset", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-29 13:04:19.069255+00
ab965e1c-83de-440d-b6d3-73ecbafe590d	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_update	\N	100	{"reason": "Admin update", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-29 13:04:27.248869+00
ca7b1899-0946-4ffa-8ecb-b2ca34cf729d	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	0	1	{"timestamp": "2025-05-29T13:31:16.29726+00:00"}	2025-05-29 13:31:16.29726+00
8f3ba073-e175-49b1-bb6d-9da0b90bdbe1	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	1	2	{"timestamp": "2025-05-29T13:33:03.139362+00:00"}	2025-05-29 13:33:03.139362+00
fb926f99-dbca-47be-813f-adfb9fe6c5b8	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	2	3	{"timestamp": "2025-05-29T13:33:41.312457+00:00"}	2025-05-29 13:33:41.312457+00
3f36b1c0-b0d4-4712-820d-3223c00795ac	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	3	4	{"timestamp": "2025-05-29T13:34:04.342952+00:00"}	2025-05-29 13:34:04.342952+00
8e93011e-c3a3-4612-8c7a-8ee48593da75	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_update	\N	200	{"reason": "Admin update", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-29 13:34:50.665206+00
5b405934-b0d2-4b33-a7cf-80e8d6a5e020	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_update	\N	201	{"reason": "Admin update", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-29 13:43:52.611831+00
345be286-9184-4bc3-ad08-fc7d7ee12138	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_update	\N	200	{"reason": "Admin update", "admin_id": "ac391977-b591-472f-bf02-a803782c4241"}	2025-05-29 13:46:44.08072+00
59f06b11-2126-4caf-9c59-956da986698e	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	4	5	{"timestamp": "2025-05-29T14:16:32.255788+00:00"}	2025-05-29 14:16:32.255788+00
9dc8b8c4-031d-476e-a723-feab1f6b8a21	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	5	6	{"timestamp": "2025-05-29T17:17:29.970414+00:00"}	2025-05-29 17:17:29.970414+00
102bede7-1760-4560-9422-6177105fd651	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	6	7	{"timestamp": "2025-05-29T17:17:43.910015+00:00"}	2025-05-29 17:17:43.910015+00
f432fd1f-059e-4071-8fdf-8b712d360771	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	7	8	{"timestamp": "2025-05-30T22:28:46.849824+00:00"}	2025-05-30 22:28:46.849824+00
91e35756-b8b0-429d-a1fc-673d903c1771	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	8	9	{"timestamp": "2025-05-30T22:29:34.633748+00:00"}	2025-05-30 22:29:34.633748+00
9a5b2b04-f9c2-4e7a-b855-19585ad988c0	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	9	10	{"timestamp": "2025-05-30T23:46:17.884878+00:00"}	2025-05-30 23:46:17.884878+00
3b3bd0e3-c5e7-487e-8368-2f539191e188	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	10	11	{"timestamp": "2025-05-30T23:46:22.779646+00:00"}	2025-05-30 23:46:22.779646+00
5fe65709-6b2d-4bc5-b294-f02ce3d52f33	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	11	12	{"timestamp": "2025-05-31T00:27:35.651523+00:00"}	2025-05-31 00:27:35.651523+00
c58224bf-615a-4a11-8eac-788f0ccd5458	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	12	13	{"timestamp": "2025-05-31T00:43:22.735463+00:00"}	2025-05-31 00:43:22.735463+00
e9bd23f4-328d-4e72-a5c3-25f9f11a9222	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	13	14	{"timestamp": "2025-05-31T06:52:26.066637+00:00"}	2025-05-31 06:52:26.066637+00
bab2b448-45cd-4d78-9651-23a98f642b10	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	14	15	{"timestamp": "2025-05-31T06:54:55.324834+00:00"}	2025-05-31 06:54:55.324834+00
651ccbae-c654-4e57-8434-dccd451b8aad	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	15	16	{"timestamp": "2025-05-31T12:12:20.810299+00:00"}	2025-05-31 12:12:20.810299+00
2c6e4bc8-1ed1-4a84-afd1-9422e41c7933	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	16	17	{"timestamp": "2025-05-31T12:14:17.766864+00:00"}	2025-05-31 12:14:17.766864+00
2094d653-16ef-44ff-ad03-cc1042a68e8b	250c5016-224e-4835-b334-d7b083eede8d	\N	quota_reset	0	0	{"reason": "monthly_reset"}	2025-06-04 21:12:14.802735+00
6ae31062-5f2a-48f6-9155-9afec20ef64a	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	0	1	{"timestamp": "2025-06-04T21:12:14.802735+00:00"}	2025-06-04 21:12:14.802735+00
a487492c-ea11-4235-baba-4dc0b5d66dc9	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	1	2	{"timestamp": "2025-06-04T22:35:41.307056+00:00"}	2025-06-04 22:35:41.307056+00
994f4564-4df0-4461-aa5c-6995ca56e6d5	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	2	3	{"timestamp": "2025-06-05T14:38:55.146651+00:00"}	2025-06-05 14:38:55.146651+00
f7785f32-e498-4767-892a-9b438873c556	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	3	4	{"timestamp": "2025-06-05T15:19:54.987923+00:00"}	2025-06-05 15:19:54.987923+00
30149e3d-28b1-440a-aba6-5232a219c9a0	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	4	5	{"timestamp": "2025-06-05T15:20:29.683586+00:00"}	2025-06-05 15:20:29.683586+00
ee154344-3df6-48a2-ae65-6549d3dd8616	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	5	6	{"timestamp": "2025-06-05T15:21:17.683994+00:00"}	2025-06-05 15:21:17.683994+00
e1e25dc7-5162-4146-88ce-8bbe95b746db	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	6	7	{"timestamp": "2025-06-05T15:50:05.443139+00:00"}	2025-06-05 15:50:05.443139+00
80a51257-6472-400f-b41b-3745071f51c7	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	7	8	{"timestamp": "2025-06-05T16:49:09.331557+00:00"}	2025-06-05 16:49:09.331557+00
6b620fdf-06c3-416a-b67b-9ea413f7589f	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	8	9	{"timestamp": "2025-06-05T16:57:15.153767+00:00"}	2025-06-05 16:57:15.153767+00
8837b287-3ac0-4464-9fb4-bcccccab583c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	9	10	{"timestamp": "2025-06-05T16:57:48.364579+00:00"}	2025-06-05 16:57:48.364579+00
6eefc5e6-a148-4bfe-86e1-63587b555e6c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	10	11	{"timestamp": "2025-06-05T16:59:31.113065+00:00"}	2025-06-05 16:59:31.113065+00
d4343cdd-2531-4acf-a4a9-74efd5438670	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	11	12	{"timestamp": "2025-06-05T17:01:37.304566+00:00"}	2025-06-05 17:01:37.304566+00
026271c7-2718-4ec3-a21c-d75b8f8bc777	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	12	13	{"timestamp": "2025-06-24T14:04:06.087551+00:00"}	2025-06-24 14:04:06.087551+00
18442b93-4eeb-4e17-84c9-3b3293901b02	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	13	14	{"timestamp": "2025-06-24T20:05:17.740196+00:00"}	2025-06-24 20:05:17.740196+00
1169890b-65a7-443e-a6d7-af2c5d9f821a	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	14	15	{"timestamp": "2025-06-25T08:12:10.73312+00:00"}	2025-06-25 08:12:10.73312+00
a8b3cbf1-366a-4eff-8491-d4077cecebcc	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	15	16	{"timestamp": "2025-06-25T09:33:28.411011+00:00"}	2025-06-25 09:33:28.411011+00
26840e35-b4e2-4621-a256-c3a92427e006	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	16	17	{"timestamp": "2025-06-25T09:54:40.246119+00:00"}	2025-06-25 09:54:40.246119+00
db351794-82cf-43c8-97a6-fa2d37f9ff9e	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	17	18	{"timestamp": "2025-06-25T09:58:04.248881+00:00"}	2025-06-25 09:58:04.248881+00
4ec4cf55-4df6-453e-84bb-41db99d76a25	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	18	19	{"timestamp": "2025-06-25T09:58:51.206863+00:00"}	2025-06-25 09:58:51.206863+00
9cd35b72-3c8a-4188-bcb9-c2ddb9a7330d	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	19	20	{"timestamp": "2025-06-25T09:58:52.902473+00:00"}	2025-06-25 09:58:52.902473+00
c2f16905-b840-44b2-824e-cd6fcb04538c	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	20	21	{"timestamp": "2025-06-25T10:01:50.389909+00:00"}	2025-06-25 10:01:50.389909+00
290f5404-590f-4ef2-be1e-c87dac3e1d3d	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	21	22	{"timestamp": "2025-06-25T10:10:55.909878+00:00"}	2025-06-25 10:10:55.909878+00
a08df407-884e-4328-837d-8490cfa1e097	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	22	23	{"timestamp": "2025-06-25T10:15:51.521828+00:00"}	2025-06-25 10:15:51.521828+00
001174d1-d4b0-424c-8883-7b5ee8ce424d	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	23	24	{"timestamp": "2025-06-25T10:16:58.97389+00:00"}	2025-06-25 10:16:58.97389+00
38877bb6-367f-44f8-bf8d-ae541af471f9	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	24	25	{"timestamp": "2025-06-25T11:38:55.924014+00:00"}	2025-06-25 11:38:55.924014+00
28da0242-e42f-4af2-a972-d118432d9dc5	250c5016-224e-4835-b334-d7b083eede8d	\N	ai_generation	25	26	{"timestamp": "2025-06-25T14:29:09.116444+00:00"}	2025-06-25 14:29:09.116444+00
\.


--
-- Data for Name: messages_2025_06_05; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_05 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: messages_2025_06_06; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_06 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: messages_2025_06_07; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_07 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: messages_2025_06_08; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_08 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: messages_2025_06_09; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_09 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: messages_2025_06_10; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_10 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: messages_2025_06_11; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.messages_2025_06_11 (topic, extension, payload, event, private, updated_at, inserted_at, id) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.schema_migrations (version, inserted_at) FROM stdin;
20211116024918	2025-05-27 06:52:11
20211116045059	2025-05-27 06:52:12
20211116050929	2025-05-27 06:52:12
20211116051442	2025-05-27 06:52:13
20211116212300	2025-05-27 06:52:14
20211116213355	2025-05-27 06:52:14
20211116213934	2025-05-27 06:52:15
20211116214523	2025-05-27 06:52:16
20211122062447	2025-05-27 06:52:17
20211124070109	2025-05-27 06:52:17
20211202204204	2025-05-27 06:52:18
20211202204605	2025-05-27 06:52:18
20211210212804	2025-05-27 06:52:20
20211228014915	2025-05-27 06:52:21
20220107221237	2025-05-27 06:52:22
20220228202821	2025-05-27 06:52:22
20220312004840	2025-05-27 06:52:23
20220603231003	2025-05-27 06:52:24
20220603232444	2025-05-27 06:52:25
20220615214548	2025-05-27 06:52:25
20220712093339	2025-05-27 06:52:26
20220908172859	2025-05-27 06:52:27
20220916233421	2025-05-27 06:52:27
20230119133233	2025-05-27 06:52:28
20230128025114	2025-05-27 06:52:29
20230128025212	2025-05-27 06:52:29
20230227211149	2025-05-27 06:52:30
20230228184745	2025-05-27 06:52:31
20230308225145	2025-05-27 06:52:31
20230328144023	2025-05-27 06:52:32
20231018144023	2025-05-27 06:52:33
20231204144023	2025-05-27 06:52:34
20231204144024	2025-05-27 06:52:34
20231204144025	2025-05-27 06:52:35
20240108234812	2025-05-27 06:52:35
20240109165339	2025-05-27 06:52:36
20240227174441	2025-05-27 06:52:37
20240311171622	2025-05-27 06:52:38
20240321100241	2025-05-27 06:52:39
20240401105812	2025-05-27 06:52:41
20240418121054	2025-05-27 06:52:42
20240523004032	2025-05-27 06:52:44
20240618124746	2025-05-27 06:52:45
20240801235015	2025-05-27 06:52:46
20240805133720	2025-05-27 06:52:46
20240827160934	2025-05-27 06:52:47
20240919163303	2025-05-27 06:52:48
20240919163305	2025-05-27 06:52:48
20241019105805	2025-05-27 06:52:49
20241030150047	2025-05-27 06:52:51
20241108114728	2025-05-27 06:52:52
20241121104152	2025-05-27 06:52:53
20241130184212	2025-05-27 06:52:53
20241220035512	2025-05-27 06:52:54
20241220123912	2025-05-27 06:52:55
20241224161212	2025-05-27 06:52:55
20250107150512	2025-05-27 06:52:56
20250110162412	2025-05-27 06:52:57
20250123174212	2025-05-27 06:52:57
20250128220012	2025-05-27 06:52:58
20250506224012	2025-05-27 06:52:58
20250523164012	2025-05-28 16:46:49
\.


--
-- Data for Name: subscription; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.subscription (id, subscription_id, entity, filters, claims, created_at) FROM stdin;
\.


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.buckets (id, name, owner, created_at, updated_at, public, avif_autodetection, file_size_limit, allowed_mime_types, owner_id) FROM stdin;
consultation-audio	consultation-audio	\N	2025-05-27 17:11:32.861441+00	2025-05-27 17:11:32.861441+00	t	f	\N	\N	\N
consultation-images	consultation-images	\N	2025-05-27 17:11:46.199048+00	2025-05-27 17:11:46.199048+00	t	f	\N	\N	\N
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.migrations (id, name, hash, executed_at) FROM stdin;
0	create-migrations-table	e18db593bcde2aca2a408c4d1100f6abba2195df	2025-05-27 06:52:10.420576
1	initialmigration	6ab16121fbaa08bbd11b712d05f358f9b555d777	2025-05-27 06:52:10.425369
2	storage-schema	5c7968fd083fcea04050c1b7f6253c9771b99011	2025-05-27 06:52:10.429863
3	pathtoken-column	2cb1b0004b817b29d5b0a971af16bafeede4b70d	2025-05-27 06:52:10.448377
4	add-migrations-rls	427c5b63fe1c5937495d9c635c263ee7a5905058	2025-05-27 06:52:10.475146
5	add-size-functions	79e081a1455b63666c1294a440f8ad4b1e6a7f84	2025-05-27 06:52:10.480124
6	change-column-name-in-get-size	f93f62afdf6613ee5e7e815b30d02dc990201044	2025-05-27 06:52:10.485252
7	add-rls-to-buckets	e7e7f86adbc51049f341dfe8d30256c1abca17aa	2025-05-27 06:52:10.492892
8	add-public-to-buckets	fd670db39ed65f9d08b01db09d6202503ca2bab3	2025-05-27 06:52:10.497104
9	fix-search-function	3a0af29f42e35a4d101c259ed955b67e1bee6825	2025-05-27 06:52:10.501661
10	search-files-search-function	68dc14822daad0ffac3746a502234f486182ef6e	2025-05-27 06:52:10.508269
11	add-trigger-to-auto-update-updated_at-column	7425bdb14366d1739fa8a18c83100636d74dcaa2	2025-05-27 06:52:10.51497
12	add-automatic-avif-detection-flag	8e92e1266eb29518b6a4c5313ab8f29dd0d08df9	2025-05-27 06:52:10.52228
13	add-bucket-custom-limits	cce962054138135cd9a8c4bcd531598684b25e7d	2025-05-27 06:52:10.527282
14	use-bytes-for-max-size	941c41b346f9802b411f06f30e972ad4744dad27	2025-05-27 06:52:10.532234
15	add-can-insert-object-function	934146bc38ead475f4ef4b555c524ee5d66799e5	2025-05-27 06:52:10.563497
16	add-version	76debf38d3fd07dcfc747ca49096457d95b1221b	2025-05-27 06:52:10.568316
17	drop-owner-foreign-key	f1cbb288f1b7a4c1eb8c38504b80ae2a0153d101	2025-05-27 06:52:10.573485
18	add_owner_id_column_deprecate_owner	e7a511b379110b08e2f214be852c35414749fe66	2025-05-27 06:52:10.578316
19	alter-default-value-objects-id	02e5e22a78626187e00d173dc45f58fa66a4f043	2025-05-27 06:52:10.584148
20	list-objects-with-delimiter	cd694ae708e51ba82bf012bba00caf4f3b6393b7	2025-05-27 06:52:10.588688
21	s3-multipart-uploads	8c804d4a566c40cd1e4cc5b3725a664a9303657f	2025-05-27 06:52:10.599262
22	s3-multipart-uploads-big-ints	9737dc258d2397953c9953d9b86920b8be0cdb73	2025-05-27 06:52:10.629871
23	optimize-search-function	9d7e604cddc4b56a5422dc68c9313f4a1b6f132c	2025-05-27 06:52:10.655402
24	operation-function	8312e37c2bf9e76bbe841aa5fda889206d2bf8aa	2025-05-27 06:52:10.660234
25	custom-metadata	d974c6057c3db1c1f847afa0e291e6165693b990	2025-05-27 06:52:10.665359
\.


--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.objects (id, bucket_id, name, owner, created_at, updated_at, last_accessed_at, metadata, version, owner_id, user_metadata) FROM stdin;
091cba44-0c39-4eec-a126-bdd383c15f2e	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/recording_1748372480414.webm	\N	2025-05-27 19:01:26.063201+00	2025-05-27 19:01:26.063201+00	2025-05-27 19:01:26.063201+00	{"eTag": "\\"37d628887799fa26b88f20db36859754\\"", "size": 399825, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T19:01:26.000Z", "contentLength": 399825, "httpStatusCode": 200}	ab2ecca1-03dc-4296-9b72-8c0928bf4db6	\N	{}
c79ec0c6-a3e9-42bb-8728-54fa6fed5d9f	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/c0640874-b8aa-4e9a-9bd3-cb1144ea8f97/recording_1748519816416.webm	\N	2025-05-29 11:57:00.624734+00	2025-05-29 11:57:00.624734+00	2025-05-29 11:57:00.624734+00	{"eTag": "\\"f36065af00d18076f20f4c44ded94fa4\\"", "size": 201558, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T11:57:01.000Z", "contentLength": 201558, "httpStatusCode": 200}	0ef4f4b5-8faa-4db8-b8b3-d26d818c9e3f	\N	{}
582853c7-d381-4f56-b3ae-8f06f4bbbf1d	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/IM-0001-0001.jpeg	\N	2025-05-27 19:01:26.764369+00	2025-05-27 19:01:26.764369+00	2025-05-27 19:01:26.764369+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T19:01:27.000Z", "contentLength": 252680, "httpStatusCode": 200}	6f301ff8-80ca-436a-a334-b36e174406b0	\N	{}
285a51af-c657-42dc-90c4-330706bf6ada	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748373911895.wav	\N	2025-05-27 19:25:12.692236+00	2025-05-27 19:25:12.692236+00	2025-05-27 19:25:12.692236+00	{"eTag": "\\"24195f5fad18443bae3f23690975345f\\"", "size": 179627, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T19:25:13.000Z", "contentLength": 179627, "httpStatusCode": 200}	32cbd953-f829-49b5-b80f-1c10d2d8027e	\N	{}
6bc339d8-6ce8-49f1-8198-95e0c1b41021	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/da197974-1484-43ea-bf4c-a2fe7fdfbff2/recording_1748519853263.webm	\N	2025-05-29 11:57:36.503722+00	2025-05-29 11:57:36.503722+00	2025-05-29 11:57:36.503722+00	{"eTag": "\\"f2a6296112742ee911cfb827de8d8dde\\"", "size": 114210, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T11:57:37.000Z", "contentLength": 114210, "httpStatusCode": 200}	8aaf32cd-e40f-47c4-8796-4f2b773bfbb0	\N	{}
f3bb7efe-169a-4a32-8753-055282f1d50d	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/Home___Desktop.png	\N	2025-05-27 19:27:39.388777+00	2025-05-27 19:27:39.388777+00	2025-05-27 19:27:39.388777+00	{"eTag": "\\"14ca3aeeffcf45489e0345a9292c7b51\\"", "size": 825799, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T19:27:40.000Z", "contentLength": 825799, "httpStatusCode": 200}	8b4f373a-4fcb-4e6e-9b28-5bcec2c71d9b	\N	{}
53b64ab1-a838-4051-8cdd-1ff32ce99c95	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/da197974-1484-43ea-bf4c-a2fe7fdfbff2/additional_audio_1748519875863.webm	\N	2025-05-29 11:58:03.356315+00	2025-05-29 11:58:03.356315+00	2025-05-29 11:58:03.356315+00	{"eTag": "\\"f9dfb011af3ebad16385ca7139656f7c\\"", "size": 156097, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T11:58:04.000Z", "contentLength": 156097, "httpStatusCode": 200}	a4344275-0804-4cd6-95a6-f31341b27259	\N	{}
7905d190-0cd3-477f-9e12-ea30e03a6a76	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748379943815.wav	\N	2025-05-27 21:05:44.834264+00	2025-05-27 21:05:44.834264+00	2025-05-27 21:05:44.834264+00	{"eTag": "\\"258020164dc81d763978a00bc42eeed6\\"", "size": 135748, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T21:05:45.000Z", "contentLength": 135748, "httpStatusCode": 200}	81b7e6c5-5747-423f-8ef2-b7d1e80fa46a	\N	{}
d0f790b7-84e6-4481-b1f1-91e1ece79c7a	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748379956900.wav	\N	2025-05-27 21:05:57.73961+00	2025-05-27 21:05:57.73961+00	2025-05-27 21:05:57.73961+00	{"eTag": "\\"1464434e31404c7e5372ae533dfc936d\\"", "size": 103051, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T21:05:58.000Z", "contentLength": 103051, "httpStatusCode": 200}	eca27f44-3fb6-4d29-8de7-3133bbc41a24	\N	{}
00180d4f-560b-4c55-a8f5-32dfc6ceb861	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/0cfa27a0-746b-49d7-9edf-1e6255b59b35-2.png	\N	2025-05-27 21:06:24.051261+00	2025-05-27 21:06:24.051261+00	2025-05-27 21:06:24.051261+00	{"eTag": "\\"75df2017393a968f5dadc6ba9177a603\\"", "size": 2600042, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T21:06:24.000Z", "contentLength": 2600042, "httpStatusCode": 200}	67196636-7afc-4c3d-bc0c-6f680893f73f	\N	{}
d58a89cf-a41b-4a6b-a459-1fb637e1ae2f	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748380064466.wav	\N	2025-05-27 21:07:45.405489+00	2025-05-27 21:07:45.405489+00	2025-05-27 21:07:45.405489+00	{"eTag": "\\"bcc2568233d134c78bb5dd1e77ddb692\\"", "size": 130816, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T21:07:46.000Z", "contentLength": 130816, "httpStatusCode": 200}	928ac90c-eddd-4f65-9775-53d06b1cc271	\N	{}
ce460c3e-0ff1-4dac-aae5-df8d572ab26c	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748380065030.wav	\N	2025-05-27 21:07:46.560666+00	2025-05-27 21:07:46.560666+00	2025-05-27 21:07:46.560666+00	{"eTag": "\\"bcc2568233d134c78bb5dd1e77ddb692\\"", "size": 130816, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-27T21:07:47.000Z", "contentLength": 130816, "httpStatusCode": 200}	476b8566-053e-4562-9e33-ec2844264c3c	\N	{}
33a9ede0-e435-479a-917f-dd5985da5b76	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/recording_1748539119378.webm	\N	2025-05-29 17:18:43.92916+00	2025-05-29 17:18:43.92916+00	2025-05-29 17:18:43.92916+00	{"eTag": "\\"592e9ca1cb61dc098dd004d14bb2e352\\"", "size": 172210, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T17:18:44.000Z", "contentLength": 172210, "httpStatusCode": 200}	95e56777-2fe4-49bf-9fe9-6cb05785e64a	\N	{}
fc022a36-0511-45bf-a854-fbeb7c6a1c49	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/0b985cbd-2cef-45e1-b509-7458cc799c52/recording_1748432003993.webm	\N	2025-05-28 11:33:26.765319+00	2025-05-28 11:33:26.765319+00	2025-05-28 11:33:26.765319+00	{"eTag": "\\"3de6aa54852660faefd830566e0e29bf\\"", "size": 645392, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T11:33:27.000Z", "contentLength": 645392, "httpStatusCode": 200}	9418fdf3-07fa-4bb0-b251-fbe279d0bbbf	\N	{}
2c12b8b8-1695-4df4-9744-1227a5f02094	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748682451316.wav	\N	2025-05-31 09:07:32.461002+00	2025-05-31 09:07:32.461002+00	2025-05-31 09:07:32.461002+00	{"eTag": "\\"f5f0fc72b1394496630ab7ff84d3240e\\"", "size": 179604, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-31T09:07:33.000Z", "contentLength": 179604, "httpStatusCode": 200}	cb10bfd4-18a3-4121-bc53-15a776c357d7	\N	{}
3af24484-148f-483b-b06b-f2fbe1dd0467	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/0b985cbd-2cef-45e1-b509-7458cc799c52/additional-audio-1748443494966.wav	\N	2025-05-28 14:44:56.14625+00	2025-05-28 14:44:56.14625+00	2025-05-28 14:44:56.14625+00	{"eTag": "\\"5c1feba0b2136eca9ed838ba6c56802f\\"", "size": 215495, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T14:44:57.000Z", "contentLength": 215495, "httpStatusCode": 200}	1d414c61-d59d-494f-b9cf-7672a9c7c260	\N	{}
f358a060-c881-48e8-abb6-7938525e027f	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/d24bc703-720e-4d7e-9b2a-5986546d9999/recording_1748860795830.webm	\N	2025-06-02 10:39:58.655738+00	2025-06-02 10:39:58.655738+00	2025-06-02 10:39:58.655738+00	{"eTag": "\\"a182f866ff430ecb24252154427018c5\\"", "size": 381965, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-02T10:39:59.000Z", "contentLength": 381965, "httpStatusCode": 200}	ec258fef-f30b-4ad9-85dc-ef9a854f4340	\N	{}
0f8bc998-362d-484c-98a3-ff6b25728244	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/0b985cbd-2cef-45e1-b509-7458cc799c52/IM-0001-0001.jpeg	\N	2025-05-28 14:45:35.273884+00	2025-05-28 14:45:35.273884+00	2025-05-28 14:45:35.273884+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T14:45:36.000Z", "contentLength": 252680, "httpStatusCode": 200}	ef70ab22-0712-450d-bea9-f19cfb30f982	\N	{}
9cdf0416-4c11-4cc8-bd8b-41f5eb9beda5	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/06236645-046e-439b-abb1-3e3cbdf841a7/recording_1748444401937.webm	\N	2025-05-28 15:00:04.754728+00	2025-05-28 15:00:04.754728+00	2025-05-28 15:00:04.754728+00	{"eTag": "\\"4fd6cb108e78851d8c132b6826366c49\\"", "size": 189261, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T15:00:05.000Z", "contentLength": 189261, "httpStatusCode": 200}	ecf16cdc-110a-42a6-be5a-8513e7623072	\N	{}
ac0ef961-83f0-4c7d-b71a-a805e63b76cc	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/IMG_7742.png	\N	2025-05-28 16:33:57.663766+00	2025-05-28 16:33:57.663766+00	2025-05-28 16:33:57.663766+00	{"eTag": "\\"f6c112d627de0e190b3fcff39aadf7c0\\"", "size": 818673, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T16:33:58.000Z", "contentLength": 818673, "httpStatusCode": 200}	af689464-1994-47a0-b0bc-224b5fa7fc5d	\N	{}
dca0b8be-a82d-42c2-93c5-a2f353a7dce4	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/37e5db62-0823-4cf1-8df1-2362a89eb35e/additional-audio-1748450053550.wav	\N	2025-05-28 16:34:17.072664+00	2025-05-28 16:34:17.072664+00	2025-05-28 16:34:17.072664+00	{"eTag": "\\"341d1ef68ef87a3391fe76c68f989559\\"", "size": 190660, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T16:34:17.000Z", "contentLength": 190660, "httpStatusCode": 200}	3133db9c-3c1f-424f-8212-449cbe4ac3f2	\N	{}
5ee0e934-3839-44b5-8fd0-0d49be974c92	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/4a09c803-adea-4c62-b7c1-3d7d5bfdcf32/recording_1748450411662.webm	\N	2025-05-28 16:40:22.808714+00	2025-05-28 16:40:22.808714+00	2025-05-28 16:40:22.808714+00	{"eTag": "\\"13e1baf8f6419d434982168489b9daa4\\"", "size": 211663, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T16:40:23.000Z", "contentLength": 211663, "httpStatusCode": 200}	e16392e0-a835-40de-b3ce-b479ff9b3c23	\N	{}
5c6c7f70-07f1-43cf-89bb-4c1e1da4464d	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/4a09c803-adea-4c62-b7c1-3d7d5bfdcf32/IM-0001-0001.jpeg	\N	2025-05-28 16:40:24.297261+00	2025-05-28 16:40:24.297261+00	2025-05-28 16:40:24.297261+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T16:40:25.000Z", "contentLength": 252680, "httpStatusCode": 200}	799dc32c-79bb-40a5-888b-6c3f8ba4f8be	\N	{}
18b95201-4aaf-4065-bed3-3645b195f4ed	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-29 17:32:47.648338+00	2025-05-29 17:32:47.648338+00	2025-05-29 17:32:47.648338+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T17:32:48.000Z", "contentLength": 7006, "httpStatusCode": 200}	71bca878-bd51-41eb-b68e-828ee3210938	\N	{}
84ba373d-ca68-4f52-899d-1393814b4d3c	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/recording_1748458751209.webm	\N	2025-05-28 18:59:16.768099+00	2025-05-28 18:59:16.768099+00	2025-05-28 18:59:16.768099+00	{"eTag": "\\"d7aa13b032a138cd083916735003c5c2\\"", "size": 324504, "mimetype": "audio/mp4", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T18:59:17.000Z", "contentLength": 324504, "httpStatusCode": 200}	ad0756b1-e228-4080-b658-294b2cb908e1	\N	{}
61e3213e-2360-45d6-b505-104e27c291c0	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748458835057.mp4	\N	2025-05-28 19:00:36.857243+00	2025-05-28 19:00:36.857243+00	2025-05-28 19:00:36.857243+00	{"eTag": "\\"94dd6ac962b5d49e6c3639ab9417aebe\\"", "size": 135832, "mimetype": "audio/mp4", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:00:37.000Z", "contentLength": 135832, "httpStatusCode": 200}	3152716a-5bef-485b-9691-0d28858452c9	\N	{}
6f6c0e51-714b-454d-a2d0-f688d61e9505	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/additional_audio_1748540009086.webm	\N	2025-05-29 17:33:32.994203+00	2025-05-29 17:33:32.994203+00	2025-05-29 17:33:32.994203+00	{"eTag": "\\"bcb10fc971a499fa0953fcfbfda5bdaf\\"", "size": 30532, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T17:33:33.000Z", "contentLength": 30532, "httpStatusCode": 200}	c8b8b25a-6cef-4202-8ab6-a0fabd09361b	\N	{}
1eb20c65-5ffc-445c-96da-dad3724413db	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/recording_1748461020076.webm	\N	2025-05-28 19:37:09.46027+00	2025-05-28 19:37:09.46027+00	2025-05-28 19:37:09.46027+00	{"eTag": "\\"d8f7414fbfd5b125504abf0b2a5bdf0c\\"", "size": 355205, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:37:10.000Z", "contentLength": 355205, "httpStatusCode": 200}	8383abbd-4af1-4e0c-ab4f-5e16873859cf	\N	{}
50c08494-d9af-4e01-b878-f8ecb4fd2fe5	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/IM-0001-0001.jpeg	\N	2025-05-28 19:38:28.147923+00	2025-05-28 19:38:28.147923+00	2025-05-28 19:38:28.147923+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:38:29.000Z", "contentLength": 252680, "httpStatusCode": 200}	115dd513-d700-41fa-b4fd-cc4de8dc5bb6	\N	{}
83e7f638-d69b-4a21-a56a-39329b84422c	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/recording_1748684967883.webm	\N	2025-05-31 09:49:30.346328+00	2025-05-31 09:49:30.346328+00	2025-05-31 09:49:30.346328+00	{"eTag": "\\"668ca2fed70ff57210959506155ac0d7\\"", "size": 370687, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-31T09:49:31.000Z", "contentLength": 370687, "httpStatusCode": 200}	1da4cb80-5e65-46c6-83a6-0d6413726848	\N	{}
b924e822-d2c6-4d60-813f-08ec427669de	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/Home___Desktop.png	\N	2025-05-28 19:54:05.423341+00	2025-05-28 19:54:05.423341+00	2025-05-28 19:54:05.423341+00	{"eTag": "\\"14ca3aeeffcf45489e0345a9292c7b51\\"", "size": 825799, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:54:06.000Z", "contentLength": 825799, "httpStatusCode": 200}	e0179845-f5f6-47ea-8af2-1dd824f765ea	\N	{}
49ffa6b4-d5b9-4898-a63e-9fbf207146ff	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/Screenshot_2025-04-22_at_12.51.37_PM.png	\N	2025-05-28 19:54:21.034285+00	2025-05-28 19:54:21.034285+00	2025-05-28 19:54:21.034285+00	{"eTag": "\\"4e1f18bcbd99294c29466e3d67189471\\"", "size": 1012486, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:54:21.000Z", "contentLength": 1012486, "httpStatusCode": 200}	15ae5e56-f94f-4c3c-9589-69ebd6280cab	\N	{}
bb5677aa-3065-46ca-9f6d-052eecdf0530	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/additional-audio-1748462064935.webm	\N	2025-05-28 19:54:27.891861+00	2025-05-28 19:54:27.891861+00	2025-05-28 19:54:27.891861+00	{"eTag": "\\"eeaeb3ced0dfc75ab6cb43ff34b94a45\\"", "size": 92912, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:54:28.000Z", "contentLength": 92912, "httpStatusCode": 200}	3444fcad-d4c4-4262-85fb-7ec5e63a93f2	\N	{}
e605e488-f2ad-41d1-936d-b403b25d19e9	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/additional-audio-1748462082305.webm	\N	2025-05-28 19:54:45.936662+00	2025-05-28 19:54:45.936662+00	2025-05-28 19:54:45.936662+00	{"eTag": "\\"f1874037cac7e58bdf4c4d401b883658\\"", "size": 296893, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:54:46.000Z", "contentLength": 296893, "httpStatusCode": 200}	18df6ee5-c1e9-4d7e-94a5-10a83d870696	\N	{}
ad90a883-e4dc-4882-b6b2-ff33eb3c425f	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/additional-audio-1748644148606.wav	\N	2025-05-30 22:29:11.131586+00	2025-05-30 22:29:11.131586+00	2025-05-30 22:29:11.131586+00	{"eTag": "\\"8bc868955a9abf97c2eec161a1a68307\\"", "size": 92328, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-30T22:29:12.000Z", "contentLength": 92328, "httpStatusCode": 200}	a816d9e2-d277-469f-93a2-ee3f230ec8c7	\N	{}
e0b52c7e-ec9a-4dd2-8768-302b02de8737	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/additional-audio-1748462105471.webm	\N	2025-05-28 19:55:09.283864+00	2025-05-28 19:55:09.283864+00	2025-05-28 19:55:09.283864+00	{"eTag": "\\"e91a76282692ec6d5b0d57b0dbcfedaf\\"", "size": 429306, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:55:10.000Z", "contentLength": 429306, "httpStatusCode": 200}	8b8fa91e-634f-495d-aa8a-0e5fa5f73b41	\N	{}
d95eb320-26dc-4455-b864-da1402f92540	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e7020f1b-df8f-4486-9c25-13e7add433bc/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-28 19:56:50.345476+00	2025-05-28 19:56:50.345476+00	2025-05-28 19:56:50.345476+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T19:56:51.000Z", "contentLength": 7006, "httpStatusCode": 200}	c9b59c0d-3c1f-4456-8b46-c3d5835cd681	\N	{}
aeafe20b-5db2-45a2-a013-1b93834180f8	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/b18a6634-ac2e-405a-afa6-0448664d3ccd/IM-0001-0001.jpeg	\N	2025-05-30 22:29:30.067538+00	2025-05-30 22:29:30.067538+00	2025-05-30 22:29:30.067538+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-30T22:29:30.000Z", "contentLength": 252680, "httpStatusCode": 200}	b7e0736d-a07a-4a2c-abce-63e07dd48153	\N	{}
1fcda382-da8b-4d8d-90c5-05b730f106cd	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748467643073.webm	\N	2025-05-28 21:27:26.240064+00	2025-05-28 21:27:26.240064+00	2025-05-28 21:27:26.240064+00	{"eTag": "\\"cc25945ef4c7adccdfda9cc54255c892\\"", "size": 143785, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T21:27:27.000Z", "contentLength": 143785, "httpStatusCode": 200}	162f21fe-7801-42e4-92c4-be4c7db054b4	\N	{}
52db0378-5dd4-43ec-a643-4c9c6f182d67	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/IM-0001-0001.jpeg	\N	2025-05-28 21:27:31.776797+00	2025-05-28 21:27:31.776797+00	2025-05-28 21:27:31.776797+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T21:27:32.000Z", "contentLength": 252680, "httpStatusCode": 200}	ac66ef01-c240-4628-b5a0-6559045b8fcb	\N	{}
cf05d011-fb0c-47df-b396-b4f5706245b5	consultation-audio	c90ce1d9-f4be-48fe-81de-9587627d29dd/6ad5d976-6c10-4839-b56c-622bcbf50468/recording_1748644295130.webm	\N	2025-05-30 22:31:43.893667+00	2025-05-30 22:31:43.893667+00	2025-05-30 22:31:43.893667+00	{"eTag": "\\"1104c03c66aa4a7d731a640395ad9f4a\\"", "size": 219255, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-30T22:31:44.000Z", "contentLength": 219255, "httpStatusCode": 200}	73a8df4a-1c87-4ca7-b9a3-831325e1e3a2	\N	{}
500d01f8-e01a-492d-b41b-908051d79995	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-28 21:27:32.080733+00	2025-05-28 21:27:32.080733+00	2025-05-28 21:27:32.080733+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T21:27:33.000Z", "contentLength": 7006, "httpStatusCode": 200}	df93491f-1eca-4b5b-81ad-f0c58abc7e98	\N	{}
719125d9-1e42-4948-9ce5-2b3f0824f7c8	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748467663146.webm	\N	2025-05-28 21:27:46.433899+00	2025-05-28 21:27:46.433899+00	2025-05-28 21:27:46.433899+00	{"eTag": "\\"5fd581abc9b1faaa236f9539afed943d\\"", "size": 229756, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T21:27:47.000Z", "contentLength": 229756, "httpStatusCode": 200}	dbbbf155-e410-4405-b312-60e2e6b4cc48	\N	{}
e941c8d5-f621-4e55-adce-01b8880f9328	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e2162a4d-f919-4feb-87b8-5e01414707fa/additional-audio-1748467686927.webm	\N	2025-05-28 21:28:09.689551+00	2025-05-28 21:28:09.689551+00	2025-05-28 21:28:09.689551+00	{"eTag": "\\"71fae625da15f0ba2a67508c1ddb3ee6\\"", "size": 169337, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-28T21:28:10.000Z", "contentLength": 169337, "httpStatusCode": 200}	e4ca2821-83f4-451d-9edf-b9c342e86b06	\N	{}
5c1e72cb-9d4a-45ee-b8a1-d5d8ad9a89ec	consultation-images	c90ce1d9-f4be-48fe-81de-9587627d29dd/6ad5d976-6c10-4839-b56c-622bcbf50468/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-30 22:31:44.620393+00	2025-05-30 22:31:44.620393+00	2025-05-30 22:31:44.620393+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-30T22:31:45.000Z", "contentLength": 7006, "httpStatusCode": 200}	cbe692c1-0fa2-44b5-93f5-891e2bedecf7	\N	{}
7f9ffae5-2414-480a-8b77-4b1d770703a0	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/recording_1748503421159.webm	\N	2025-05-29 07:23:46.194957+00	2025-05-29 07:23:46.194957+00	2025-05-29 07:23:46.194957+00	{"eTag": "\\"eea73dcfef2160626628ed8e287b8ea0\\"", "size": 250254, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T07:23:47.000Z", "contentLength": 250254, "httpStatusCode": 200}	9c0a5576-b7ce-4c6f-bf9b-668aab6c5ee9	\N	{}
505eae72-f090-4303-ab48-18ed03d5c52d	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/additional-audio-1748503497709.wav	\N	2025-05-29 07:24:59.214477+00	2025-05-29 07:24:59.214477+00	2025-05-29 07:24:59.214477+00	{"eTag": "\\"7d110aa01a584730c95df54208c51293\\"", "size": 226862, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T07:25:00.000Z", "contentLength": 226862, "httpStatusCode": 200}	c68c4e9e-15e0-46cb-be94-63183ca612d6	\N	{}
8d1e3cdd-2f00-4b73-8941-e4d048461a3e	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/additional-audio-1748693197887.wav	\N	2025-05-31 12:06:38.999926+00	2025-05-31 12:06:38.999926+00	2025-05-31 12:06:38.999926+00	{"eTag": "\\"2425d6a6600eec59dad00ae903a920f5\\"", "size": 236681, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-31T12:06:39.000Z", "contentLength": 236681, "httpStatusCode": 200}	901ea4ac-73c4-452e-804b-9ee38a2db29b	\N	{}
f78559b1-e689-46f3-9a2b-276c1251441a	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-29 07:25:39.572065+00	2025-05-29 07:25:39.572065+00	2025-05-29 07:25:39.572065+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T07:25:40.000Z", "contentLength": 7006, "httpStatusCode": 200}	e0485be1-8259-444a-9f24-a25310fd42f8	\N	{}
3cb29aed-43d5-4d06-97e6-9f741d8761cf	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/d24bc703-720e-4d7e-9b2a-5986546d9999/additional_audio_1748860821758.webm	\N	2025-06-02 10:40:24.631426+00	2025-06-02 10:40:24.631426+00	2025-06-02 10:40:24.631426+00	{"eTag": "\\"e2182c176cae76a9578c7d4a60b402b7\\"", "size": 264926, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-02T10:40:25.000Z", "contentLength": 264926, "httpStatusCode": 200}	ecdfd818-9fe5-4a33-a817-cbb7a2518020	\N	{}
ed32e9f9-fd73-4dc2-b1eb-e14981233dc8	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/e26b3422-539b-4364-ae07-0cd15a582340/IM-0001-0001.jpeg	\N	2025-05-29 07:28:18.227864+00	2025-05-29 07:28:18.227864+00	2025-05-29 07:28:18.227864+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T07:28:19.000Z", "contentLength": 252680, "httpStatusCode": 200}	c90d7908-4966-4731-ac90-c17f51e182be	\N	{}
29379f96-6b8d-430a-8371-fa9f5f08a9a6	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/4a09c803-adea-4c62-b7c1-3d7d5bfdcf32/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-29 07:29:01.847888+00	2025-05-29 07:29:01.847888+00	2025-05-29 07:29:01.847888+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T07:29:02.000Z", "contentLength": 7006, "httpStatusCode": 200}	6ec571b5-54ea-43ee-ab21-e481c6b8ee49	\N	{}
0ef77b6b-fc70-47fc-9276-c0e3f595a270	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/recording_1748504108525.webm	\N	2025-05-29 07:35:16.126829+00	2025-05-29 07:35:16.126829+00	2025-05-29 07:35:16.126829+00	{"eTag": "\\"507b4aec12daaa159e6792d2c26a993f\\"", "size": 97421, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T07:35:17.000Z", "contentLength": 97421, "httpStatusCode": 200}	0c240a30-7ff8-4abc-aaba-f38a840dc63d	\N	{}
1723beb6-16a9-4a54-9398-a84c5be130c7	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/IM-0001-0001.jpeg	\N	2025-05-29 07:35:16.897884+00	2025-05-29 08:18:13.381907+00	2025-05-29 07:35:16.897884+00	{"eTag": "\\"6535e4c9b865af758eb077d28864c334\\"", "size": 252680, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T08:18:14.000Z", "contentLength": 252680, "httpStatusCode": 200}	cf0897d1-1813-4e87-a882-d41cd37380e6	\N	{}
3bef1c12-aece-4bd3-a9c0-bd814b8f83a2	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/**********-x-ray-left-thigh-ap-lat.webp	\N	2025-05-29 08:13:22.017911+00	2025-05-29 08:18:12.804002+00	2025-05-29 08:13:22.017911+00	{"eTag": "\\"fe0262116595f5765c78916b6aace36d\\"", "size": 7006, "mimetype": "image/webp", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T08:18:13.000Z", "contentLength": 7006, "httpStatusCode": 200}	d0cbb684-17db-447b-b8ca-b45bba14e123	\N	{}
21d5de6f-9c87-4cfe-9b05-d9935e5fded4	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/4177283f-f8ed-4182-9524-da78b61f2d51/recording_1748646960714.webm	\N	2025-05-30 23:16:03.151439+00	2025-05-30 23:16:03.151439+00	2025-05-30 23:16:03.151439+00	{"eTag": "\\"31d37e67b64fd165ca27f3df81a48d13\\"", "size": 158707, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-30T23:16:04.000Z", "contentLength": 158707, "httpStatusCode": 200}	f0375c77-e7f6-4355-ad6a-f97b67da5360	\N	{}
1b96f4f6-e358-4d26-a3b6-60ee6f466dbb	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/additional-audio-1748506775502.wav	\N	2025-05-29 08:19:37.041653+00	2025-05-29 08:19:37.041653+00	2025-05-29 08:19:37.041653+00	{"eTag": "\\"61d07a89ae1024f3ca55fcb864078a9b\\"", "size": 72009, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T08:19:37.000Z", "contentLength": 72009, "httpStatusCode": 200}	153cfd18-d2aa-431e-b8f5-8eb8225e0926	\N	{}
65100d5f-b98b-4248-a544-39b40f52a152	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/2778480c-2caf-4ddc-90ca-277b7a97f138/recording_1748694026940.webm	\N	2025-05-31 12:20:29.810904+00	2025-05-31 12:20:29.810904+00	2025-05-31 12:20:29.810904+00	{"eTag": "\\"45fbe61c14f87a04fe0607d0e73cdef7\\"", "size": 780774, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-31T12:20:30.000Z", "contentLength": 780774, "httpStatusCode": 200}	3a836784-fa99-4b6e-98e5-e3ad8529fb68	\N	{}
1df55993-2343-4a2a-a07b-e0258e147940	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/820e20cc-e5d2-4e64-82fa-782e1e296346/recording_1748513966259.webm	\N	2025-05-29 10:19:36.302781+00	2025-05-29 10:19:36.302781+00	2025-05-29 10:19:36.302781+00	{"eTag": "\\"6d09a289abe0b2274a61f7926e8b58d5\\"", "size": 247942, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-29T10:19:37.000Z", "contentLength": 247942, "httpStatusCode": 200}	fda50c2c-844d-405d-8b03-9d86677fa818	\N	{}
e4487b5b-6f84-4a0d-b129-fcaa0c1d72fb	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/2d2d8bc9-28e7-4d08-ba76-034d588cedf6/recording_1748694462242.webm	\N	2025-05-31 12:27:44.871759+00	2025-05-31 12:27:44.871759+00	2025-05-31 12:27:44.871759+00	{"eTag": "\\"f2574c2689da859b11e7eba7d2e51b0c\\"", "size": 279267, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-31T12:27:45.000Z", "contentLength": 279267, "httpStatusCode": 200}	10534fca-02aa-4a58-b2ae-43f5106ea87a	\N	{}
430f4135-1235-4b84-acd4-cf79138c4290	consultation-audio	776c57ce-4bd6-4a58-88a2-0c5db6dc2a93/04dd7cb8-db12-40eb-b9cc-ec8fa626edd7/recording_1748694643232.webm	\N	2025-05-31 12:30:47.608844+00	2025-05-31 12:30:47.608844+00	2025-05-31 12:30:47.608844+00	{"eTag": "\\"272c9d197bc0a17bd8e6bbbc4d0c4d15\\"", "size": 261256, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-05-31T12:30:48.000Z", "contentLength": 261256, "httpStatusCode": 200}	d7c412fc-4a99-4d49-a7fb-4f309bfaa9a0	\N	{}
38672119-9512-4fc9-a1bb-272f99b42a7a	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/b6bda625-0105-4b94-87e7-8afe194b3cab/recording_1748902294375.webm	\N	2025-06-02 22:11:36.59598+00	2025-06-02 22:11:36.59598+00	2025-06-02 22:11:36.59598+00	{"eTag": "\\"778965f10397e9f72542003241afbaa0\\"", "size": 734647, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-02T22:11:37.000Z", "contentLength": 734647, "httpStatusCode": 200}	37ca949b-3d3d-46b9-ab3d-d31e09b18f43	\N	{}
91f02b53-89c1-478d-a769-599d1c61a06b	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/d8c06d02-0784-4735-9f1c-019a45322094/recording-1748937840424.wav	\N	2025-06-03 08:04:53.476109+00	2025-06-03 08:04:53.476109+00	2025-06-03 08:04:53.476109+00	{"eTag": "\\"9e9dc5b96bee8daa63db92cd6b6b07c9\\"", "size": 34292, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T08:04:54.000Z", "contentLength": 34292, "httpStatusCode": 200}	71d28010-6d25-47d5-862a-2c05bacb801f	\N	{}
036fed13-331a-4cb2-a973-52d7ef402236	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/d8c06d02-0784-4735-9f1c-019a45322094/image.jpg	\N	2025-06-03 08:04:55.050414+00	2025-06-03 08:04:55.050414+00	2025-06-03 08:04:55.050414+00	{"eTag": "\\"6227f09f1a329bfec1b343e9c8f85ada\\"", "size": 887632, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T08:04:55.000Z", "contentLength": 887632, "httpStatusCode": 200}	932352ca-8c6c-4312-bea2-134cdda5384c	\N	{}
98be844f-aebf-4431-9fb8-a0ce3c3ec93f	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/661a7c00-f85a-4ce8-90d8-36cdf2d5fab3/recording-1748939143833.wav	\N	2025-06-03 08:25:50.768139+00	2025-06-03 08:25:50.768139+00	2025-06-03 08:25:50.768139+00	{"eTag": "\\"9bfb9bf530987da5473c3081fa49999f\\"", "size": 469095, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T08:25:51.000Z", "contentLength": 469095, "httpStatusCode": 200}	fcbabc88-83ad-4cff-a34e-249837cdd0d3	\N	{}
14e9a119-2f9f-43d1-9e89-e9fb3afcb8dc	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/9e5ae3f7-bd65-480b-98d5-4e92ed75b1c8/recording-1748948256079.wav	\N	2025-06-03 10:58:08.999471+00	2025-06-03 10:58:08.999471+00	2025-06-03 10:58:08.999471+00	{"eTag": "\\"03dff3585f8d1597e3185517ebf35bbd\\"", "size": 193078, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T10:58:09.000Z", "contentLength": 193078, "httpStatusCode": 200}	4b901585-f7ed-47b8-9c1f-a64382172a8b	\N	{}
0550e638-ebfb-4d79-9ac6-f86f76232b9d	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/9e5ae3f7-bd65-480b-98d5-4e92ed75b1c8/C.JPG	\N	2025-06-03 10:58:09.526359+00	2025-06-03 10:58:09.526359+00	2025-06-03 10:58:09.526359+00	{"eTag": "\\"6c81623b9e62a89286c889f68bd3dd9d\\"", "size": 355174, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T10:58:10.000Z", "contentLength": 355174, "httpStatusCode": 200}	6e195ec4-c657-49b0-a501-8bbf229b0598	\N	{}
01a25bb9-1661-4fd1-951b-4979cc819569	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/89ff719e-89ad-417d-94ab-44371ea4438c/recording-1748948431326.wav	\N	2025-06-03 11:00:39.116526+00	2025-06-03 11:00:39.116526+00	2025-06-03 11:00:39.116526+00	{"eTag": "\\"a861f4b116eb62865881f4b02279cfc6\\"", "size": 60846, "mimetype": "audio/wav", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T11:00:40.000Z", "contentLength": 60846, "httpStatusCode": 200}	52d6f5bd-7c75-4065-b004-bf164d5b6bec	\N	{}
7426d5ce-e729-4489-87ca-3dde9c392007	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/89ff719e-89ad-417d-94ab-44371ea4438c/C.JPG	\N	2025-06-03 11:00:39.805586+00	2025-06-03 11:00:39.805586+00	2025-06-03 11:00:39.805586+00	{"eTag": "\\"6c81623b9e62a89286c889f68bd3dd9d\\"", "size": 355174, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-06-03T11:00:40.000Z", "contentLength": 355174, "httpStatusCode": 200}	4e400fb4-d6c7-402f-9e7c-4c23548a51e8	\N	{}
7328f09d-380d-455e-9de6-bff7b0a649b9	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/df388d05-098b-4d55-82ec-d136adeb6588/recording_1749117138237.webm	\N	2025-06-05 09:52:19.15622+00	2025-06-05 09:52:19.15622+00	2025-06-05 09:52:19.15622+00	{"eTag": "\\"e7fb0bfc0de830b725cb75c8a4de6b8e\\"", "size": 130225, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T09:52:19.000Z", "contentLength": 130225, "httpStatusCode": 200}	67409474-36a9-4484-9c98-324a8848c25b	\N	{}
676be923-800f-4b4c-83fd-d23959b93a57	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/20664e6b-6df5-42b8-b33c-998c7373cdfa/recording_1749132105574.webm	\N	2025-06-05 14:01:47.321425+00	2025-06-05 14:01:47.321425+00	2025-06-05 14:01:47.321425+00	{"eTag": "\\"361e5f1354127c7f59d874c1abc28ea7\\"", "size": 111696, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T14:01:48.000Z", "contentLength": 111696, "httpStatusCode": 200}	eef1adf1-bdec-4e51-a3e9-d66a53052d5d	\N	{}
f38f4ec5-50cb-4f9e-a099-81ea7c3b6f88	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/0f025e89-5c11-42a7-8b95-e9a4e84defac/recording_1749132525806.webm	\N	2025-06-05 14:08:46.972338+00	2025-06-05 14:08:46.972338+00	2025-06-05 14:08:46.972338+00	{"eTag": "\\"661effa830d5ab96bc6edc52ae1a51f4\\"", "size": 147570, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T14:08:47.000Z", "contentLength": 147570, "httpStatusCode": 200}	45b9e1c7-4b4f-4bc4-ac5a-63bdad7adb18	\N	{}
2db70da3-8c8d-4a74-8a8d-4974d4568a89	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/21943881-2109-42e2-b3e3-d3756f221077/recording_1749134159944.webm	\N	2025-06-05 14:36:01.189229+00	2025-06-05 14:36:01.189229+00	2025-06-05 14:36:01.189229+00	{"eTag": "\\"468231e68759af444522a086846ed645\\"", "size": 107387, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T14:36:02.000Z", "contentLength": 107387, "httpStatusCode": 200}	fdfd2d1e-ed06-4e3e-9bdb-2db85e3a3afc	\N	{}
746922e7-f0c3-434e-b16f-330a0e3b5b62	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/0f025e89-5c11-42a7-8b95-e9a4e84defac/Screenshot_2025-06-05_at_12.38.57_AM.png	\N	2025-06-05 15:16:04.245211+00	2025-06-05 15:16:04.245211+00	2025-06-05 15:16:04.245211+00	{"eTag": "\\"01371965892389e8b3aaf685ccb1e229\\"", "size": 271246, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T15:16:05.000Z", "contentLength": 271246, "httpStatusCode": 200}	1d726c24-f327-496f-afda-e02419db7abb	\N	{}
235942fd-7153-4752-9562-07be1dd566d1	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/0f025e89-5c11-42a7-8b95-e9a4e84defac/Screenshot_2025-06-05_at_4.51.42_AM.png	\N	2025-06-05 15:16:07.509534+00	2025-06-05 15:16:07.509534+00	2025-06-05 15:16:07.509534+00	{"eTag": "\\"27f2cdae4a0e9a2c4b5a9eb60a70ff54\\"", "size": 3101951, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T15:16:08.000Z", "contentLength": 3101951, "httpStatusCode": 200}	09f59719-11e1-4306-9bf4-6db6d630d245	\N	{}
b2daa167-c827-4483-b909-42ef137251b5	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/bfd254d6-28bd-4040-9bba-bbbb1b95a599/recording_1749136638917.webm	\N	2025-06-05 15:17:20.564627+00	2025-06-05 15:17:20.564627+00	2025-06-05 15:17:20.564627+00	{"eTag": "\\"667bf073b9c79720c3fd548f58557b00\\"", "size": 247314, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T15:17:21.000Z", "contentLength": 247314, "httpStatusCode": 200}	5d0bca9b-0846-449b-8d1f-ebd39eae6e93	\N	{}
d46e8677-cd94-4d8d-a7dc-63f34ffb48f5	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/bfd254d6-28bd-4040-9bba-bbbb1b95a599/Screenshot_2025-06-05_at_12.38.57_AM.png	\N	2025-06-05 16:57:34.54583+00	2025-06-05 16:57:34.54583+00	2025-06-05 16:57:34.54583+00	{"eTag": "\\"01371965892389e8b3aaf685ccb1e229\\"", "size": 271246, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T16:57:35.000Z", "contentLength": 271246, "httpStatusCode": 200}	702d2b08-3057-4533-aed3-18c5755580fc	\N	{}
3939847f-185f-4675-b11d-f62e07fea050	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/c8bd3944-cc98-4cb5-8031-13e028531d69/recording_1749149527217.webm	\N	2025-06-05 18:52:08.504127+00	2025-06-05 18:52:08.504127+00	2025-06-05 18:52:08.504127+00	{"eTag": "\\"be780bffb8373e8ef0e6737b208a2308\\"", "size": 185185, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T18:52:09.000Z", "contentLength": 185185, "httpStatusCode": 200}	49ef44c3-0a14-404e-b83f-7fdf82715c2e	\N	{}
86c8809a-0377-4868-9c2d-bf1f0d3ce413	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/c8bd3944-cc98-4cb5-8031-13e028531d69/Screenshot_2025-06-05_at_12.38.57_AM.png	\N	2025-06-05 18:52:09.656341+00	2025-06-05 18:52:09.656341+00	2025-06-05 18:52:09.656341+00	{"eTag": "\\"01371965892389e8b3aaf685ccb1e229\\"", "size": 271246, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T18:52:10.000Z", "contentLength": 271246, "httpStatusCode": 200}	4fb36441-9aed-48ef-b0de-e4cf69afd0d4	\N	{}
4c2ecd9f-9ee9-41e5-92e1-09b16bfe1dbc	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/99cbab27-6a0b-49a9-b246-60a4c3496891/recording_1749157830297.webm	\N	2025-06-05 21:10:31.238952+00	2025-06-05 21:10:31.238952+00	2025-06-05 21:10:31.238952+00	{"eTag": "\\"d41d8cd98f00b204e9800998ecf8427e\\"", "size": 0, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-05T21:10:32.000Z", "contentLength": 0, "httpStatusCode": 200}	9dda7de6-403a-4ab7-ba6f-2e75053af4c0	\N	{}
eb4768b3-bd3d-403b-a942-a5360dc7dd0d	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/99cbab27-6a0b-49a9-b246-60a4c3496891/additional_audio_1749195631512.webm	\N	2025-06-06 07:40:32.435142+00	2025-06-06 07:40:32.435142+00	2025-06-06 07:40:32.435142+00	{"eTag": "\\"4cf89abc1c1ded9bc05d62a04c9f22ec\\"", "size": 129519, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T07:40:33.000Z", "contentLength": 129519, "httpStatusCode": 200}	2f994265-21ec-438a-bfbe-98e301280f20	\N	{}
d1c82ae6-60c6-46d4-befd-ed5dc48e51b9	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/recording_1749195681812.webm	\N	2025-06-06 07:41:22.753237+00	2025-06-06 07:41:22.753237+00	2025-06-06 07:41:22.753237+00	{"eTag": "\\"2daa5bef49f852df9033f54f135fc910\\"", "size": 345368, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T07:41:23.000Z", "contentLength": 345368, "httpStatusCode": 200}	f924633b-a6d9-43dc-a1a6-557f0db94949	\N	{}
56b29e45-5be8-40bf-927e-210f53d5562b	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/additional_audio_1749222056973.webm	\N	2025-06-06 15:00:58.653419+00	2025-06-06 15:00:58.653419+00	2025-06-06 15:00:58.653419+00	{"eTag": "\\"112756d339c0dd57db8c365abbae8388\\"", "size": 192395, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T15:00:59.000Z", "contentLength": 192395, "httpStatusCode": 200}	9b55be18-f539-4f7c-9c39-bac95554fb9f	\N	{}
920c2d92-2f68-4076-b5f0-95c7e9c2b680	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/additional_audio_1749225929153.webm	\N	2025-06-06 16:05:30.140849+00	2025-06-06 16:05:30.140849+00	2025-06-06 16:05:30.140849+00	{"eTag": "\\"ef395461c7b2656e809d68724c7e5b27\\"", "size": 39767, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T16:05:31.000Z", "contentLength": 39767, "httpStatusCode": 200}	f5da6cc8-bd01-49ce-a0b2-953bd6af9e9d	\N	{}
e2e23615-fc15-465a-bba5-08c2cfdaba83	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/568ac92d-4afe-48fe-b57d-db4f96491d6f/additional_audio_1749225946870.webm	\N	2025-06-06 16:05:48.154173+00	2025-06-06 16:05:48.154173+00	2025-06-06 16:05:48.154173+00	{"eTag": "\\"81b856d8a1534232a6426606e18f3572\\"", "size": 145061, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T16:05:49.000Z", "contentLength": 145061, "httpStatusCode": 200}	7ac822b7-bf12-4909-abb8-f33512830f8a	\N	{}
eb7e6eb6-3f73-4fa1-9741-5e902045ce9b	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/85e20163-27df-4efd-814f-2661f973ba21/recording_1749226958232.webm	\N	2025-06-06 16:22:39.541313+00	2025-06-06 16:22:39.541313+00	2025-06-06 16:22:39.541313+00	{"eTag": "\\"22a10b8fa957b08084b195106c627b79\\"", "size": 28175, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T16:22:40.000Z", "contentLength": 28175, "httpStatusCode": 200}	0ae7f3c8-6491-4413-a6ba-123f641ccb89	\N	{}
2dce5dbd-a502-4a96-a635-343c3d4d66a2	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/Screenshot_2025-06-05_at_12.38.57_AM.png	\N	2025-06-06 17:23:39.261644+00	2025-06-06 17:23:39.261644+00	2025-06-06 17:23:39.261644+00	{"eTag": "\\"01371965892389e8b3aaf685ccb1e229\\"", "size": 271246, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T17:23:40.000Z", "contentLength": 271246, "httpStatusCode": 200}	1d76d491-4931-46f7-b71f-25d201dbc0ba	\N	{}
076c8278-3cd5-475d-ae48-85b14c4f2060	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/additional_audio_1749230619785.webm	\N	2025-06-06 17:23:40.707784+00	2025-06-06 17:23:40.707784+00	2025-06-06 17:23:40.707784+00	{"eTag": "\\"0f3102f274dd9e4ca7f9d4df7f73eddd\\"", "size": 149891, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T17:23:41.000Z", "contentLength": 149891, "httpStatusCode": 200}	109efedc-c479-4b5e-8838-b7210dae4384	\N	{}
891b0ed9-ee54-4146-85de-28c8bb4ce56e	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/c8bd3944-cc98-4cb5-8031-13e028531d69/additional_audio_1749231020706.webm	\N	2025-06-06 17:30:21.635047+00	2025-06-06 17:30:21.635047+00	2025-06-06 17:30:21.635047+00	{"eTag": "\\"1573bed934ff6c6d261e42569183f357\\"", "size": 24311, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-06T17:30:22.000Z", "contentLength": 24311, "httpStatusCode": 200}	2754aca7-4d95-40aa-90c3-1d83c1bab8d3	\N	{}
a460c206-80a8-40a9-ade9-57a3b6db9768	consultation-images	250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/Screenshot_2025-06-05_at_4.51.42_AM.png	\N	2025-06-07 09:22:44.181168+00	2025-06-07 09:22:44.181168+00	2025-06-07 09:22:44.181168+00	{"eTag": "\\"27f2cdae4a0e9a2c4b5a9eb60a70ff54\\"", "size": 3101951, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T09:22:45.000Z", "contentLength": 3101951, "httpStatusCode": 200}	9a4eba13-e6cf-46d9-8e3b-090f782e0f03	\N	{}
76c56943-0e04-42f1-9397-bbb430a55841	consultation-audio	152e0149-a54f-48ed-86b0-bc8500aabfc0/a71ad6de-f1a3-48b7-862e-e06dfe236af0/recording_1749306771658.webm	\N	2025-06-07 14:33:05.482136+00	2025-06-07 14:33:05.482136+00	2025-06-07 14:33:05.482136+00	{"eTag": "\\"1d0324038263ba3b5a706a9467c5d430\\"", "size": 477221, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T14:33:06.000Z", "contentLength": 477221, "httpStatusCode": 200}	b432b9f3-beac-44b0-a3ec-c8d9b883e9d2	\N	{}
bbba4ed2-d70d-4b9a-8e6c-ee5859589c31	consultation-images	152e0149-a54f-48ed-86b0-bc8500aabfc0/a71ad6de-f1a3-48b7-862e-e06dfe236af0/e6e9d381-f287-4e8e-9c82-0770aa07c84d.jpeg	\N	2025-06-07 14:33:06.181774+00	2025-06-07 14:33:06.181774+00	2025-06-07 14:33:06.181774+00	{"eTag": "\\"e07694b563c1f5bde7abc17c69d25ccb\\"", "size": 162101, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T14:33:07.000Z", "contentLength": 162101, "httpStatusCode": 200}	2f5c7c1a-e45e-4a84-b05e-820bc85e8d7e	\N	{}
69ee744c-3da4-421b-ae94-d63e4e7907c1	consultation-audio	152e0149-a54f-48ed-86b0-bc8500aabfc0/66ac12ab-0f0b-4f84-bded-c43b6ffcb1bf/recording_1749306923469.webm	\N	2025-06-07 14:35:27.70592+00	2025-06-07 14:35:27.70592+00	2025-06-07 14:35:27.70592+00	{"eTag": "\\"84ac3f6a077574cc8fe2f2a276dd525f\\"", "size": 290451, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T14:35:28.000Z", "contentLength": 290451, "httpStatusCode": 200}	02e3e2a6-7c3f-472b-b466-7b08ea2a3742	\N	{}
2604fbda-1e45-4de1-b22a-f547afe247cf	consultation-audio	152e0149-a54f-48ed-86b0-bc8500aabfc0/a71ad6de-f1a3-48b7-862e-e06dfe236af0/additional_audio_1749307509091.webm	\N	2025-06-07 14:45:10.324442+00	2025-06-07 14:45:10.324442+00	2025-06-07 14:45:10.324442+00	{"eTag": "\\"b1e9ed444df5953835695802a791ca08\\"", "size": 89033, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T14:45:11.000Z", "contentLength": 89033, "httpStatusCode": 200}	2008a7c8-6be5-40d6-ab67-a6dbfbeb2d48	\N	{}
9975f7f7-9650-48ad-9d0b-e74d06e28c26	consultation-images	152e0149-a54f-48ed-86b0-bc8500aabfc0/66ac12ab-0f0b-4f84-bded-c43b6ffcb1bf/IMG_7766.png	\N	2025-06-07 14:45:58.65302+00	2025-06-07 14:45:58.65302+00	2025-06-07 14:45:58.65302+00	{"eTag": "\\"ef8ab425ed78a5fc8715762107d0598d\\"", "size": 187350, "mimetype": "image/png", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T14:45:59.000Z", "contentLength": 187350, "httpStatusCode": 200}	5b9e5b1f-9027-41c6-b989-3d677f471371	\N	{}
572e1aba-38dd-44e1-a29e-81289c1e8800	consultation-images	152e0149-a54f-48ed-86b0-bc8500aabfc0/66ac12ab-0f0b-4f84-bded-c43b6ffcb1bf/ba8d29b8-b219-4c9c-8c48-8951d51da777.jpeg	\N	2025-06-07 14:47:23.174041+00	2025-06-07 14:47:23.174041+00	2025-06-07 14:47:23.174041+00	{"eTag": "\\"04f8c8cdca3c97657509c5f7540bca06\\"", "size": 172486, "mimetype": "image/jpeg", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T14:47:24.000Z", "contentLength": 172486, "httpStatusCode": 200}	324a3f30-fa5f-4d31-8097-e8c9a1901d4a	\N	{}
92291fb8-30c0-495c-b1a0-6d896f941d15	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/8ebed267-0e9d-4a66-a92e-d67a49bde24b/additional_audio_1749320717343.webm	\N	2025-06-07 18:25:18.512277+00	2025-06-07 18:25:18.512277+00	2025-06-07 18:25:18.512277+00	{"eTag": "\\"5265b03a8cd3e0ad991e7ee8c616f076\\"", "size": 541135, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T18:25:19.000Z", "contentLength": 541135, "httpStatusCode": 200}	62d9609e-4dd1-4c8c-89a3-ac3117829cfe	\N	{}
232d3c67-cc52-4d7a-a7e3-884524abc80a	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/07547111-7ffb-4de3-b2d1-0ca6df9e64af/additional_audio_1749323871199.webm	\N	2025-06-07 19:17:52.567482+00	2025-06-07 19:17:52.567482+00	2025-06-07 19:17:52.567482+00	{"eTag": "\\"d2291fa3d40b1c402f1071433b85d4d5\\"", "size": 329431, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-07T19:17:53.000Z", "contentLength": 329431, "httpStatusCode": 200}	bf81585f-f311-415d-8b09-59da78eb3ddb	\N	{}
d5ac92ed-61b7-4724-bab4-c899b5446bc8	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/6e578d23-a5b7-40a3-aec1-f821bf0f91f8/recording_1749371445381.webm	\N	2025-06-08 08:30:48.416658+00	2025-06-08 08:30:48.416658+00	2025-06-08 08:30:48.416658+00	{"eTag": "\\"b55325aa09dcfd00bea1037880f94d97\\"", "size": 451109, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-08T08:30:49.000Z", "contentLength": 451109, "httpStatusCode": 200}	cc984e51-e1de-4787-813c-90e33ee3f255	\N	{}
e2ac71d2-91e6-4ec6-9456-75be60e6696d	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/24fe1278-2024-4a84-89d3-0822504aa5f6/recording_1749372103666.webm	\N	2025-06-08 08:41:44.886639+00	2025-06-08 08:41:44.886639+00	2025-06-08 08:41:44.886639+00	{"eTag": "\\"1ed33a05fcb27e642f1a1a53319474bc\\"", "size": 443476, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-08T08:41:45.000Z", "contentLength": 443476, "httpStatusCode": 200}	bae1b483-56ea-461b-93c1-a72023af5989	\N	{}
49247493-357a-454a-9021-65ebac835346	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/01bbae9b-95a8-44fa-985d-9e8ee011febb/recording_1749372358415.webm	\N	2025-06-08 08:45:59.851794+00	2025-06-08 08:45:59.851794+00	2025-06-08 08:45:59.851794+00	{"eTag": "\\"030cf9f5851a5a28da067d08851254b0\\"", "size": 386063, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-08T08:46:00.000Z", "contentLength": 386063, "httpStatusCode": 200}	2f1c6249-d8eb-41f3-a9eb-27c4829a73d8	\N	{}
e7332709-222b-43e9-96cb-48bc374c6219	consultation-audio	250c5016-224e-4835-b334-d7b083eede8d/2589c7a7-416c-4832-bfc7-b76fea05d8ab/recording_1749373205638.webm	\N	2025-06-08 09:00:06.608208+00	2025-06-08 09:00:06.608208+00	2025-06-08 09:00:06.608208+00	{"eTag": "\\"7a90396a6ae6ca79cb198c644d750e86\\"", "size": 331917, "mimetype": "audio/webm", "cacheControl": "max-age=3600", "lastModified": "2025-06-08T09:00:07.000Z", "contentLength": 331917, "httpStatusCode": 200}	a4dfe37a-f515-432d-806e-305a68fcca03	\N	{}
\.


--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.s3_multipart_uploads (id, in_progress_size, upload_signature, bucket_id, key, version, owner_id, created_at, user_metadata) FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.s3_multipart_uploads_parts (id, upload_id, size, part_number, bucket_id, key, etag, owner_id, version, created_at) FROM stdin;
\.


--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

COPY vault.secrets (id, name, description, secret, key_id, nonce, created_at, updated_at) FROM stdin;
\.


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('auth.refresh_tokens_id_seq', 1, false);


--
-- Name: jobid_seq; Type: SEQUENCE SET; Schema: cron; Owner: supabase_admin
--

SELECT pg_catalog.setval('cron.jobid_seq', 1, true);


--
-- Name: runid_seq; Type: SEQUENCE SET; Schema: cron; Owner: supabase_admin
--

SELECT pg_catalog.setval('cron.runid_seq', 16, true);


--
-- Name: subscription_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_admin
--

SELECT pg_catalog.setval('realtime.subscription_id_seq', 1, false);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_last_challenged_at_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_last_challenged_at_key UNIQUE (last_challenged_at);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: one_time_tokens one_time_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: admins admins_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admins
    ADD CONSTRAINT admins_email_key UNIQUE (email);


--
-- Name: admins admins_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.admins
    ADD CONSTRAINT admins_pkey PRIMARY KEY (id);


--
-- Name: billing_plans billing_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.billing_plans
    ADD CONSTRAINT billing_plans_pkey PRIMARY KEY (id);


--
-- Name: billing_transactions billing_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.billing_transactions
    ADD CONSTRAINT billing_transactions_pkey PRIMARY KEY (id);


--
-- Name: consultations consultations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.consultations
    ADD CONSTRAINT consultations_pkey PRIMARY KEY (id);


--
-- Name: contact_requests contact_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.contact_requests
    ADD CONSTRAINT contact_requests_pkey PRIMARY KEY (id);


--
-- Name: doctors doctors_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doctors
    ADD CONSTRAINT doctors_email_key UNIQUE (email);


--
-- Name: doctors doctors_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doctors
    ADD CONSTRAINT doctors_pkey PRIMARY KEY (id);


--
-- Name: doctors doctors_referral_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doctors
    ADD CONSTRAINT doctors_referral_code_key UNIQUE (referral_code);


--
-- Name: referral_analytics referral_analytics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_analytics
    ADD CONSTRAINT referral_analytics_pkey PRIMARY KEY (id);


--
-- Name: referral_discounts referral_discounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_discounts
    ADD CONSTRAINT referral_discounts_pkey PRIMARY KEY (id);


--
-- Name: usage_logs usage_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_logs
    ADD CONSTRAINT usage_logs_pkey PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_05 messages_2025_06_05_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_05
    ADD CONSTRAINT messages_2025_06_05_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_06 messages_2025_06_06_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_06
    ADD CONSTRAINT messages_2025_06_06_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_07 messages_2025_06_07_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_07
    ADD CONSTRAINT messages_2025_06_07_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_08 messages_2025_06_08_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_08
    ADD CONSTRAINT messages_2025_06_08_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_09 messages_2025_06_09_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_09
    ADD CONSTRAINT messages_2025_06_09_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_10 messages_2025_06_10_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_10
    ADD CONSTRAINT messages_2025_06_10_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_11 messages_2025_06_11_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_06_11
    ADD CONSTRAINT messages_2025_06_11_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: subscription pk_subscription; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.subscription
    ADD CONSTRAINT pk_subscription PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: buckets buckets_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.buckets
    ADD CONSTRAINT buckets_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_name_key; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_name_key UNIQUE (name);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: objects objects_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT objects_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_pkey PRIMARY KEY (id);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: one_time_tokens_relates_to_hash_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);


--
-- Name: one_time_tokens_token_hash_hash_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);


--
-- Name: one_time_tokens_user_id_token_type_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: unique_phone_factor_per_user; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: idx_admins_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_admins_email ON public.admins USING btree (email);


--
-- Name: idx_billing_transactions_billing_period; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_billing_transactions_billing_period ON public.billing_transactions USING btree (billing_period_start, billing_period_end);


--
-- Name: idx_billing_transactions_created_by; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_billing_transactions_created_by ON public.billing_transactions USING btree (created_by);


--
-- Name: idx_billing_transactions_doctor_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_billing_transactions_doctor_id ON public.billing_transactions USING btree (doctor_id);


--
-- Name: idx_billing_transactions_payment_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_billing_transactions_payment_date ON public.billing_transactions USING btree (payment_date);


--
-- Name: idx_billing_transactions_payment_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_billing_transactions_payment_status ON public.billing_transactions USING btree (payment_status);


--
-- Name: idx_billing_transactions_plan_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_billing_transactions_plan_id ON public.billing_transactions USING btree (plan_id);


--
-- Name: idx_consultations_consultation_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_consultation_type ON public.consultations USING btree (consultation_type);


--
-- Name: idx_consultations_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_created_at ON public.consultations USING btree (created_at);


--
-- Name: idx_consultations_doctor_id_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_doctor_id_status ON public.consultations USING btree (doctor_id, status);


--
-- Name: idx_consultations_file_retention; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_file_retention ON public.consultations USING btree (file_retention_until);


--
-- Name: idx_consultations_patient_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_consultations_patient_name ON public.consultations USING btree (patient_name);


--
-- Name: idx_contact_requests_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_contact_requests_created_at ON public.contact_requests USING btree (created_at);


--
-- Name: idx_contact_requests_doctor_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_contact_requests_doctor_id ON public.contact_requests USING btree (doctor_id);


--
-- Name: idx_contact_requests_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_contact_requests_status ON public.contact_requests USING btree (status);


--
-- Name: idx_doctors_approved; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_approved ON public.doctors USING btree (approved);


--
-- Name: idx_doctors_billing_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_billing_status ON public.doctors USING btree (billing_status);


--
-- Name: idx_doctors_current_plan_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_current_plan_id ON public.doctors USING btree (current_plan_id);


--
-- Name: idx_doctors_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_email ON public.doctors USING btree (email);


--
-- Name: idx_doctors_quota_reset_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_quota_reset_at ON public.doctors USING btree (quota_reset_at);


--
-- Name: idx_doctors_referral_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_referral_code ON public.doctors USING btree (referral_code);


--
-- Name: idx_doctors_referred_by; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_referred_by ON public.doctors USING btree (referred_by);


--
-- Name: idx_doctors_trial_ends_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_doctors_trial_ends_at ON public.doctors USING btree (trial_ends_at);


--
-- Name: idx_referral_analytics_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_analytics_created_at ON public.referral_analytics USING btree (created_at);


--
-- Name: idx_referral_analytics_referred_doctor_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_analytics_referred_doctor_id ON public.referral_analytics USING btree (referred_doctor_id);


--
-- Name: idx_referral_analytics_referrer_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_analytics_referrer_id ON public.referral_analytics USING btree (referrer_id);


--
-- Name: idx_referral_analytics_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_analytics_status ON public.referral_analytics USING btree (status);


--
-- Name: idx_referral_discounts_applied_to_transaction_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_discounts_applied_to_transaction_id ON public.referral_discounts USING btree (applied_to_transaction_id);


--
-- Name: idx_referral_discounts_doctor_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_discounts_doctor_id ON public.referral_discounts USING btree (doctor_id);


--
-- Name: idx_referral_discounts_referral_analytics_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_discounts_referral_analytics_id ON public.referral_discounts USING btree (referral_analytics_id);


--
-- Name: idx_referral_discounts_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_discounts_status ON public.referral_discounts USING btree (status);


--
-- Name: idx_referral_discounts_valid_until; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_referral_discounts_valid_until ON public.referral_discounts USING btree (valid_until);


--
-- Name: idx_usage_logs_action_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_usage_logs_action_type ON public.usage_logs USING btree (action_type);


--
-- Name: idx_usage_logs_consultation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_usage_logs_consultation_id ON public.usage_logs USING btree (consultation_id);


--
-- Name: idx_usage_logs_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_usage_logs_created_at ON public.usage_logs USING btree (created_at);


--
-- Name: idx_usage_logs_doctor_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_usage_logs_doctor_id ON public.usage_logs USING btree (doctor_id);


--
-- Name: ix_realtime_subscription_entity; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE INDEX ix_realtime_subscription_entity ON realtime.subscription USING btree (entity);


--
-- Name: subscription_subscription_id_entity_filters_key; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX subscription_subscription_id_entity_filters_key ON realtime.subscription USING btree (subscription_id, entity, filters);


--
-- Name: bname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name);


--
-- Name: bucketid_objname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name);


--
-- Name: idx_multipart_uploads_list; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_multipart_uploads_list ON storage.s3_multipart_uploads USING btree (bucket_id, key, created_at);


--
-- Name: idx_objects_bucket_id_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_objects_bucket_id_name ON storage.objects USING btree (bucket_id, name COLLATE "C");


--
-- Name: name_prefix_search; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops);


--
-- Name: messages_2025_06_05_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_05_pkey;


--
-- Name: messages_2025_06_06_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_06_pkey;


--
-- Name: messages_2025_06_07_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_07_pkey;


--
-- Name: messages_2025_06_08_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_08_pkey;


--
-- Name: messages_2025_06_09_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_09_pkey;


--
-- Name: messages_2025_06_10_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_10_pkey;


--
-- Name: messages_2025_06_11_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_11_pkey;


--
-- Name: doctors doctors_insert_trigger; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER doctors_insert_trigger AFTER INSERT ON public.doctors FOR EACH ROW EXECUTE FUNCTION public.notify_new_doctor();


--
-- Name: consultations set_consultation_patient_number; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER set_consultation_patient_number BEFORE INSERT ON public.consultations FOR EACH ROW EXECUTE FUNCTION public.set_patient_number();


--
-- Name: doctors set_doctor_referral_code; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER set_doctor_referral_code BEFORE INSERT ON public.doctors FOR EACH ROW EXECUTE FUNCTION public.set_referral_code();


--
-- Name: admins update_admins_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_admins_updated_at BEFORE UPDATE ON public.admins FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: billing_plans update_billing_plans_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_billing_plans_updated_at BEFORE UPDATE ON public.billing_plans FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: billing_transactions update_billing_transactions_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_billing_transactions_updated_at BEFORE UPDATE ON public.billing_transactions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: consultations update_consultations_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_consultations_updated_at BEFORE UPDATE ON public.consultations FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: contact_requests update_contact_requests_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_contact_requests_updated_at BEFORE UPDATE ON public.contact_requests FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: doctors update_doctors_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_doctors_updated_at BEFORE UPDATE ON public.doctors FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: referral_analytics update_referral_analytics_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_referral_analytics_updated_at BEFORE UPDATE ON public.referral_analytics FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: referral_discounts update_referral_discounts_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_referral_discounts_updated_at BEFORE UPDATE ON public.referral_discounts FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: subscription tr_check_filters; Type: TRIGGER; Schema: realtime; Owner: supabase_admin
--

CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters();


--
-- Name: objects update_objects_updated_at; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column();


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: one_time_tokens one_time_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: billing_transactions billing_transactions_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.billing_transactions
    ADD CONSTRAINT billing_transactions_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.admins(id);


--
-- Name: billing_transactions billing_transactions_doctor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.billing_transactions
    ADD CONSTRAINT billing_transactions_doctor_id_fkey FOREIGN KEY (doctor_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: billing_transactions billing_transactions_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.billing_transactions
    ADD CONSTRAINT billing_transactions_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES public.billing_plans(id);


--
-- Name: consultations consultations_doctor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.consultations
    ADD CONSTRAINT consultations_doctor_id_fkey FOREIGN KEY (doctor_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: contact_requests contact_requests_doctor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.contact_requests
    ADD CONSTRAINT contact_requests_doctor_id_fkey FOREIGN KEY (doctor_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: doctors doctors_current_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doctors
    ADD CONSTRAINT doctors_current_plan_id_fkey FOREIGN KEY (current_plan_id) REFERENCES public.billing_plans(id);


--
-- Name: doctors doctors_referred_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.doctors
    ADD CONSTRAINT doctors_referred_by_fkey FOREIGN KEY (referred_by) REFERENCES public.doctors(id) ON DELETE SET NULL;


--
-- Name: referral_analytics referral_analytics_referred_doctor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_analytics
    ADD CONSTRAINT referral_analytics_referred_doctor_id_fkey FOREIGN KEY (referred_doctor_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: referral_analytics referral_analytics_referrer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_analytics
    ADD CONSTRAINT referral_analytics_referrer_id_fkey FOREIGN KEY (referrer_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: referral_discounts referral_discounts_applied_to_transaction_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_discounts
    ADD CONSTRAINT referral_discounts_applied_to_transaction_id_fkey FOREIGN KEY (applied_to_transaction_id) REFERENCES public.billing_transactions(id) ON DELETE SET NULL;


--
-- Name: referral_discounts referral_discounts_doctor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_discounts
    ADD CONSTRAINT referral_discounts_doctor_id_fkey FOREIGN KEY (doctor_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: referral_discounts referral_discounts_referral_analytics_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.referral_discounts
    ADD CONSTRAINT referral_discounts_referral_analytics_id_fkey FOREIGN KEY (referral_analytics_id) REFERENCES public.referral_analytics(id) ON DELETE CASCADE;


--
-- Name: usage_logs usage_logs_consultation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_logs
    ADD CONSTRAINT usage_logs_consultation_id_fkey FOREIGN KEY (consultation_id) REFERENCES public.consultations(id) ON DELETE CASCADE;


--
-- Name: usage_logs usage_logs_doctor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_logs
    ADD CONSTRAINT usage_logs_doctor_id_fkey FOREIGN KEY (doctor_id) REFERENCES public.doctors(id) ON DELETE CASCADE;


--
-- Name: objects objects_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT "objects_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_upload_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_upload_id_fkey FOREIGN KEY (upload_id) REFERENCES storage.s3_multipart_uploads(id) ON DELETE CASCADE;


--
-- Name: audit_log_entries; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;

--
-- Name: flow_state; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;

--
-- Name: identities; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;

--
-- Name: instances; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.instances ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_amr_claims; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_challenges; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_factors; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;

--
-- Name: one_time_tokens; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: refresh_tokens; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_providers; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.saml_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_relay_states; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;

--
-- Name: schema_migrations; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.schema_migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: sessions; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_domains; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_providers; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

--
-- Name: admins Allow all operations on admins; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on admins" ON public.admins USING (true) WITH CHECK (true);


--
-- Name: billing_plans Allow all operations on billing_plans; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on billing_plans" ON public.billing_plans USING (true);


--
-- Name: billing_transactions Allow all operations on billing_transactions; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on billing_transactions" ON public.billing_transactions USING (true);


--
-- Name: consultations Allow all operations on consultations; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on consultations" ON public.consultations USING (true) WITH CHECK (true);


--
-- Name: contact_requests Allow all operations on contact_requests; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on contact_requests" ON public.contact_requests USING (true);


--
-- Name: doctors Allow all operations on doctors; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on doctors" ON public.doctors USING (true) WITH CHECK (true);


--
-- Name: referral_analytics Allow all operations on referral_analytics; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on referral_analytics" ON public.referral_analytics USING (true);


--
-- Name: referral_discounts Allow all operations on referral_discounts; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on referral_discounts" ON public.referral_discounts USING (true);


--
-- Name: usage_logs Allow all operations on usage_logs; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Allow all operations on usage_logs" ON public.usage_logs USING (true) WITH CHECK (true);


--
-- Name: admins; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.admins ENABLE ROW LEVEL SECURITY;

--
-- Name: billing_plans; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.billing_plans ENABLE ROW LEVEL SECURITY;

--
-- Name: billing_transactions; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.billing_transactions ENABLE ROW LEVEL SECURITY;

--
-- Name: consultations; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.consultations ENABLE ROW LEVEL SECURITY;

--
-- Name: contact_requests; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.contact_requests ENABLE ROW LEVEL SECURITY;

--
-- Name: doctors; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.doctors ENABLE ROW LEVEL SECURITY;

--
-- Name: referral_analytics; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.referral_analytics ENABLE ROW LEVEL SECURITY;

--
-- Name: referral_discounts; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.referral_discounts ENABLE ROW LEVEL SECURITY;

--
-- Name: usage_logs; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.usage_logs ENABLE ROW LEVEL SECURITY;

--
-- Name: messages; Type: ROW SECURITY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE realtime.messages ENABLE ROW LEVEL SECURITY;

--
-- Name: objects Allow backend to delete files; Type: POLICY; Schema: storage; Owner: supabase_storage_admin
--

CREATE POLICY "Allow backend to delete files" ON storage.objects FOR DELETE TO service_role USING ((bucket_id = ANY (ARRAY['consultation-audio'::text, 'consultation-images'::text])));


--
-- Name: objects Allow public read access to audio; Type: POLICY; Schema: storage; Owner: supabase_storage_admin
--

CREATE POLICY "Allow public read access to audio" ON storage.objects FOR SELECT USING ((bucket_id = 'consultation-audio'::text));


--
-- Name: objects Allow public read access to images; Type: POLICY; Schema: storage; Owner: supabase_storage_admin
--

CREATE POLICY "Allow public read access to images" ON storage.objects FOR SELECT USING ((bucket_id = 'consultation-images'::text));


--
-- Name: objects Allow uploads to audio bucket; Type: POLICY; Schema: storage; Owner: supabase_storage_admin
--

CREATE POLICY "Allow uploads to audio bucket" ON storage.objects FOR INSERT WITH CHECK ((bucket_id = 'consultation-audio'::text));


--
-- Name: objects Allow uploads to images bucket; Type: POLICY; Schema: storage; Owner: supabase_storage_admin
--

CREATE POLICY "Allow uploads to images bucket" ON storage.objects FOR INSERT WITH CHECK ((bucket_id = 'consultation-images'::text));


--
-- Name: buckets; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

--
-- Name: migrations; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: objects; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.s3_multipart_uploads ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads_parts; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.s3_multipart_uploads_parts ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: postgres
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION supabase_realtime OWNER TO postgres;

--
-- Name: supabase_realtime_messages_publication; Type: PUBLICATION; Schema: -; Owner: supabase_admin
--

CREATE PUBLICATION supabase_realtime_messages_publication WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION supabase_realtime_messages_publication OWNER TO supabase_admin;

--
-- Name: supabase_realtime_messages_publication messages; Type: PUBLICATION TABLE; Schema: realtime; Owner: supabase_admin
--

ALTER PUBLICATION supabase_realtime_messages_publication ADD TABLE ONLY realtime.messages;


--
-- Name: SCHEMA auth; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA auth TO anon;
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT USAGE ON SCHEMA auth TO service_role;
GRANT ALL ON SCHEMA auth TO supabase_auth_admin;
GRANT ALL ON SCHEMA auth TO dashboard_user;
GRANT USAGE ON SCHEMA auth TO postgres;


--
-- Name: SCHEMA cron; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA cron TO postgres WITH GRANT OPTION;


--
-- Name: SCHEMA extensions; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA extensions TO anon;
GRANT USAGE ON SCHEMA extensions TO authenticated;
GRANT USAGE ON SCHEMA extensions TO service_role;
GRANT ALL ON SCHEMA extensions TO dashboard_user;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT USAGE ON SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;


--
-- Name: SCHEMA net; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA net TO supabase_functions_admin;
GRANT USAGE ON SCHEMA net TO postgres;
GRANT USAGE ON SCHEMA net TO anon;
GRANT USAGE ON SCHEMA net TO authenticated;
GRANT USAGE ON SCHEMA net TO service_role;


--
-- Name: SCHEMA realtime; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA realtime TO postgres;
GRANT USAGE ON SCHEMA realtime TO anon;
GRANT USAGE ON SCHEMA realtime TO authenticated;
GRANT USAGE ON SCHEMA realtime TO service_role;
GRANT ALL ON SCHEMA realtime TO supabase_realtime_admin;


--
-- Name: SCHEMA storage; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA storage TO postgres;
GRANT USAGE ON SCHEMA storage TO anon;
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT USAGE ON SCHEMA storage TO service_role;
GRANT ALL ON SCHEMA storage TO supabase_storage_admin;
GRANT ALL ON SCHEMA storage TO dashboard_user;


--
-- Name: SCHEMA vault; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA vault TO postgres WITH GRANT OPTION;
GRANT USAGE ON SCHEMA vault TO service_role;


--
-- Name: FUNCTION email(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.email() TO dashboard_user;


--
-- Name: FUNCTION jwt(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.jwt() TO postgres;
GRANT ALL ON FUNCTION auth.jwt() TO dashboard_user;


--
-- Name: FUNCTION role(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.role() TO dashboard_user;


--
-- Name: FUNCTION uid(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.uid() TO dashboard_user;


--
-- Name: FUNCTION alter_job(job_id bigint, schedule text, command text, database text, username text, active boolean); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.alter_job(job_id bigint, schedule text, command text, database text, username text, active boolean) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION job_cache_invalidate(); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.job_cache_invalidate() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule(schedule text, command text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule(schedule text, command text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule(job_name text, schedule text, command text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule(job_name text, schedule text, command text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule_in_database(job_name text, schedule text, command text, database text, username text, active boolean); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule_in_database(job_name text, schedule text, command text, database text, username text, active boolean) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION unschedule(job_id bigint); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.unschedule(job_id bigint) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION unschedule(job_name text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.unschedule(job_name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION armor(bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.armor(bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.armor(bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.armor(bytea) TO dashboard_user;


--
-- Name: FUNCTION armor(bytea, text[], text[]); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.armor(bytea, text[], text[]) FROM postgres;
GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO dashboard_user;


--
-- Name: FUNCTION crypt(text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.crypt(text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.crypt(text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.crypt(text, text) TO dashboard_user;


--
-- Name: FUNCTION dearmor(text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.dearmor(text) FROM postgres;
GRANT ALL ON FUNCTION extensions.dearmor(text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.dearmor(text) TO dashboard_user;


--
-- Name: FUNCTION decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION decrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION digest(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.digest(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION digest(text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.digest(text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.digest(text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.digest(text, text) TO dashboard_user;


--
-- Name: FUNCTION encrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION encrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION gen_random_bytes(integer); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_random_bytes(integer) FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO dashboard_user;


--
-- Name: FUNCTION gen_random_uuid(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_random_uuid() FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO dashboard_user;


--
-- Name: FUNCTION gen_salt(text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_salt(text) FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_salt(text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_salt(text) TO dashboard_user;


--
-- Name: FUNCTION gen_salt(text, integer); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.gen_salt(text, integer) FROM postgres;
GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO dashboard_user;


--
-- Name: FUNCTION grant_pg_cron_access(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION extensions.grant_pg_cron_access() FROM supabase_admin;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO supabase_admin WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO dashboard_user;


--
-- Name: FUNCTION grant_pg_graphql_access(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.grant_pg_graphql_access() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION grant_pg_net_access(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION extensions.grant_pg_net_access() FROM supabase_admin;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO supabase_admin WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO dashboard_user;


--
-- Name: FUNCTION hmac(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.hmac(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION hmac(text, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.hmac(text, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO dashboard_user;


--
-- Name: FUNCTION pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) FROM postgres;
GRANT ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) TO dashboard_user;


--
-- Name: FUNCTION pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) FROM postgres;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) TO dashboard_user;


--
-- Name: FUNCTION pg_stat_statements_reset(userid oid, dbid oid, queryid bigint); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) FROM postgres;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO dashboard_user;


--
-- Name: FUNCTION pgp_armor_headers(text, OUT key text, OUT value text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO dashboard_user;


--
-- Name: FUNCTION pgp_key_id(bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_key_id(bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO dashboard_user;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO dashboard_user;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) FROM postgres;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO dashboard_user;


--
-- Name: FUNCTION pgrst_ddl_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_ddl_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgrst_drop_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_drop_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION set_graphql_placeholder(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.set_graphql_placeholder() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v1(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v1() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v1mc(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v1mc() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v3(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v4(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v4() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO dashboard_user;


--
-- Name: FUNCTION uuid_generate_v5(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO dashboard_user;


--
-- Name: FUNCTION uuid_nil(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_nil() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_nil() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_nil() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_dns(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_dns() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_oid(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_oid() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_url(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_url() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO dashboard_user;


--
-- Name: FUNCTION uuid_ns_x500(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.uuid_ns_x500() FROM postgres;
GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO dashboard_user;


--
-- Name: FUNCTION graphql("operationName" text, query text, variables jsonb, extensions jsonb); Type: ACL; Schema: graphql_public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO postgres;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO anon;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO authenticated;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO service_role;


--
-- Name: FUNCTION get_auth(p_usename text); Type: ACL; Schema: pgbouncer; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION pgbouncer.get_auth(p_usename text) FROM PUBLIC;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO pgbouncer;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO postgres;


--
-- Name: FUNCTION apply_referral_discount(transaction_id uuid, discount_amount numeric); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.apply_referral_discount(transaction_id uuid, discount_amount numeric) TO anon;
GRANT ALL ON FUNCTION public.apply_referral_discount(transaction_id uuid, discount_amount numeric) TO authenticated;
GRANT ALL ON FUNCTION public.apply_referral_discount(transaction_id uuid, discount_amount numeric) TO service_role;


--
-- Name: FUNCTION bytea_to_text(data bytea); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.bytea_to_text(data bytea) TO postgres;
GRANT ALL ON FUNCTION public.bytea_to_text(data bytea) TO anon;
GRANT ALL ON FUNCTION public.bytea_to_text(data bytea) TO authenticated;
GRANT ALL ON FUNCTION public.bytea_to_text(data bytea) TO service_role;


--
-- Name: FUNCTION check_and_update_quota(doctor_uuid uuid); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.check_and_update_quota(doctor_uuid uuid) TO anon;
GRANT ALL ON FUNCTION public.check_and_update_quota(doctor_uuid uuid) TO authenticated;
GRANT ALL ON FUNCTION public.check_and_update_quota(doctor_uuid uuid) TO service_role;


--
-- Name: FUNCTION complete_payment(transaction_id uuid); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.complete_payment(transaction_id uuid) TO anon;
GRANT ALL ON FUNCTION public.complete_payment(transaction_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.complete_payment(transaction_id uuid) TO service_role;


--
-- Name: FUNCTION create_referral_discount(referrer_doctor_id uuid, referral_analytics_id uuid, referred_amount numeric); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.create_referral_discount(referrer_doctor_id uuid, referral_analytics_id uuid, referred_amount numeric) TO anon;
GRANT ALL ON FUNCTION public.create_referral_discount(referrer_doctor_id uuid, referral_analytics_id uuid, referred_amount numeric) TO authenticated;
GRANT ALL ON FUNCTION public.create_referral_discount(referrer_doctor_id uuid, referral_analytics_id uuid, referred_amount numeric) TO service_role;


--
-- Name: FUNCTION fix_doctor_discount_amounts(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.fix_doctor_discount_amounts() TO anon;
GRANT ALL ON FUNCTION public.fix_doctor_discount_amounts() TO authenticated;
GRANT ALL ON FUNCTION public.fix_doctor_discount_amounts() TO service_role;


--
-- Name: FUNCTION generate_referral_code(doctor_name text, doctor_email text); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.generate_referral_code(doctor_name text, doctor_email text) TO anon;
GRANT ALL ON FUNCTION public.generate_referral_code(doctor_name text, doctor_email text) TO authenticated;
GRANT ALL ON FUNCTION public.generate_referral_code(doctor_name text, doctor_email text) TO service_role;


--
-- Name: FUNCTION handle_referral_conversion(referred_doctor_uuid uuid); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.handle_referral_conversion(referred_doctor_uuid uuid) TO anon;
GRANT ALL ON FUNCTION public.handle_referral_conversion(referred_doctor_uuid uuid) TO authenticated;
GRANT ALL ON FUNCTION public.handle_referral_conversion(referred_doctor_uuid uuid) TO service_role;


--
-- Name: FUNCTION http(request public.http_request); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http(request public.http_request) TO postgres;
GRANT ALL ON FUNCTION public.http(request public.http_request) TO anon;
GRANT ALL ON FUNCTION public.http(request public.http_request) TO authenticated;
GRANT ALL ON FUNCTION public.http(request public.http_request) TO service_role;


--
-- Name: FUNCTION http_delete(uri character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_delete(uri character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_delete(uri character varying) TO anon;
GRANT ALL ON FUNCTION public.http_delete(uri character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_delete(uri character varying) TO service_role;


--
-- Name: FUNCTION http_delete(uri character varying, content character varying, content_type character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_delete(uri character varying, content character varying, content_type character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_delete(uri character varying, content character varying, content_type character varying) TO anon;
GRANT ALL ON FUNCTION public.http_delete(uri character varying, content character varying, content_type character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_delete(uri character varying, content character varying, content_type character varying) TO service_role;


--
-- Name: FUNCTION http_get(uri character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_get(uri character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_get(uri character varying) TO anon;
GRANT ALL ON FUNCTION public.http_get(uri character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_get(uri character varying) TO service_role;


--
-- Name: FUNCTION http_get(uri character varying, data jsonb); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_get(uri character varying, data jsonb) TO postgres;
GRANT ALL ON FUNCTION public.http_get(uri character varying, data jsonb) TO anon;
GRANT ALL ON FUNCTION public.http_get(uri character varying, data jsonb) TO authenticated;
GRANT ALL ON FUNCTION public.http_get(uri character varying, data jsonb) TO service_role;


--
-- Name: FUNCTION http_head(uri character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_head(uri character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_head(uri character varying) TO anon;
GRANT ALL ON FUNCTION public.http_head(uri character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_head(uri character varying) TO service_role;


--
-- Name: FUNCTION http_header(field character varying, value character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_header(field character varying, value character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_header(field character varying, value character varying) TO anon;
GRANT ALL ON FUNCTION public.http_header(field character varying, value character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_header(field character varying, value character varying) TO service_role;


--
-- Name: FUNCTION http_list_curlopt(); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_list_curlopt() TO postgres;
GRANT ALL ON FUNCTION public.http_list_curlopt() TO anon;
GRANT ALL ON FUNCTION public.http_list_curlopt() TO authenticated;
GRANT ALL ON FUNCTION public.http_list_curlopt() TO service_role;


--
-- Name: FUNCTION http_patch(uri character varying, content character varying, content_type character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_patch(uri character varying, content character varying, content_type character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_patch(uri character varying, content character varying, content_type character varying) TO anon;
GRANT ALL ON FUNCTION public.http_patch(uri character varying, content character varying, content_type character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_patch(uri character varying, content character varying, content_type character varying) TO service_role;


--
-- Name: FUNCTION http_post(uri character varying, data jsonb); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_post(uri character varying, data jsonb) TO postgres;
GRANT ALL ON FUNCTION public.http_post(uri character varying, data jsonb) TO anon;
GRANT ALL ON FUNCTION public.http_post(uri character varying, data jsonb) TO authenticated;
GRANT ALL ON FUNCTION public.http_post(uri character varying, data jsonb) TO service_role;


--
-- Name: FUNCTION http_post(uri character varying, content character varying, content_type character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_post(uri character varying, content character varying, content_type character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_post(uri character varying, content character varying, content_type character varying) TO anon;
GRANT ALL ON FUNCTION public.http_post(uri character varying, content character varying, content_type character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_post(uri character varying, content character varying, content_type character varying) TO service_role;


--
-- Name: FUNCTION http_put(uri character varying, content character varying, content_type character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_put(uri character varying, content character varying, content_type character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_put(uri character varying, content character varying, content_type character varying) TO anon;
GRANT ALL ON FUNCTION public.http_put(uri character varying, content character varying, content_type character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_put(uri character varying, content character varying, content_type character varying) TO service_role;


--
-- Name: FUNCTION http_reset_curlopt(); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_reset_curlopt() TO postgres;
GRANT ALL ON FUNCTION public.http_reset_curlopt() TO anon;
GRANT ALL ON FUNCTION public.http_reset_curlopt() TO authenticated;
GRANT ALL ON FUNCTION public.http_reset_curlopt() TO service_role;


--
-- Name: FUNCTION http_set_curlopt(curlopt character varying, value character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.http_set_curlopt(curlopt character varying, value character varying) TO postgres;
GRANT ALL ON FUNCTION public.http_set_curlopt(curlopt character varying, value character varying) TO anon;
GRANT ALL ON FUNCTION public.http_set_curlopt(curlopt character varying, value character varying) TO authenticated;
GRANT ALL ON FUNCTION public.http_set_curlopt(curlopt character varying, value character varying) TO service_role;


--
-- Name: FUNCTION notify_new_doctor(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.notify_new_doctor() TO anon;
GRANT ALL ON FUNCTION public.notify_new_doctor() TO authenticated;
GRANT ALL ON FUNCTION public.notify_new_doctor() TO service_role;


--
-- Name: FUNCTION pause_expired_accounts(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.pause_expired_accounts() TO anon;
GRANT ALL ON FUNCTION public.pause_expired_accounts() TO authenticated;
GRANT ALL ON FUNCTION public.pause_expired_accounts() TO service_role;


--
-- Name: FUNCTION reset_all_quotas(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.reset_all_quotas() TO anon;
GRANT ALL ON FUNCTION public.reset_all_quotas() TO authenticated;
GRANT ALL ON FUNCTION public.reset_all_quotas() TO service_role;


--
-- Name: FUNCTION set_patient_number(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.set_patient_number() TO anon;
GRANT ALL ON FUNCTION public.set_patient_number() TO authenticated;
GRANT ALL ON FUNCTION public.set_patient_number() TO service_role;


--
-- Name: FUNCTION set_referral_code(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.set_referral_code() TO anon;
GRANT ALL ON FUNCTION public.set_referral_code() TO authenticated;
GRANT ALL ON FUNCTION public.set_referral_code() TO service_role;


--
-- Name: FUNCTION text_to_bytea(data text); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.text_to_bytea(data text) TO postgres;
GRANT ALL ON FUNCTION public.text_to_bytea(data text) TO anon;
GRANT ALL ON FUNCTION public.text_to_bytea(data text) TO authenticated;
GRANT ALL ON FUNCTION public.text_to_bytea(data text) TO service_role;


--
-- Name: FUNCTION update_doctor_quota(doctor_uuid uuid, new_quota integer, admin_uuid uuid); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.update_doctor_quota(doctor_uuid uuid, new_quota integer, admin_uuid uuid) TO anon;
GRANT ALL ON FUNCTION public.update_doctor_quota(doctor_uuid uuid, new_quota integer, admin_uuid uuid) TO authenticated;
GRANT ALL ON FUNCTION public.update_doctor_quota(doctor_uuid uuid, new_quota integer, admin_uuid uuid) TO service_role;


--
-- Name: FUNCTION update_updated_at_column(); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.update_updated_at_column() TO anon;
GRANT ALL ON FUNCTION public.update_updated_at_column() TO authenticated;
GRANT ALL ON FUNCTION public.update_updated_at_column() TO service_role;


--
-- Name: FUNCTION urlencode(string bytea); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.urlencode(string bytea) TO postgres;
GRANT ALL ON FUNCTION public.urlencode(string bytea) TO anon;
GRANT ALL ON FUNCTION public.urlencode(string bytea) TO authenticated;
GRANT ALL ON FUNCTION public.urlencode(string bytea) TO service_role;


--
-- Name: FUNCTION urlencode(data jsonb); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.urlencode(data jsonb) TO postgres;
GRANT ALL ON FUNCTION public.urlencode(data jsonb) TO anon;
GRANT ALL ON FUNCTION public.urlencode(data jsonb) TO authenticated;
GRANT ALL ON FUNCTION public.urlencode(data jsonb) TO service_role;


--
-- Name: FUNCTION urlencode(string character varying); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.urlencode(string character varying) TO postgres;
GRANT ALL ON FUNCTION public.urlencode(string character varying) TO anon;
GRANT ALL ON FUNCTION public.urlencode(string character varying) TO authenticated;
GRANT ALL ON FUNCTION public.urlencode(string character varying) TO service_role;


--
-- Name: FUNCTION apply_rls(wal jsonb, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) TO postgres;
GRANT ALL ON FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) TO dashboard_user;


--
-- Name: FUNCTION build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO postgres;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO anon;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO service_role;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION "cast"(val text, type_ regtype); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO postgres;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO dashboard_user;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO anon;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO authenticated;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO service_role;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO supabase_realtime_admin;


--
-- Name: FUNCTION check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO postgres;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO anon;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO authenticated;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO service_role;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO supabase_realtime_admin;


--
-- Name: FUNCTION is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO postgres;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO anon;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO service_role;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION quote_wal2json(entity regclass); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO postgres;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO anon;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO authenticated;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO service_role;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO supabase_realtime_admin;


--
-- Name: FUNCTION send(payload jsonb, event text, topic text, private boolean); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) TO postgres;
GRANT ALL ON FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) TO dashboard_user;


--
-- Name: FUNCTION subscription_check_filters(); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO postgres;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO dashboard_user;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO anon;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO authenticated;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO service_role;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO supabase_realtime_admin;


--
-- Name: FUNCTION to_regrole(role_name text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO postgres;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO anon;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO authenticated;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO service_role;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO supabase_realtime_admin;


--
-- Name: FUNCTION topic(); Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON FUNCTION realtime.topic() TO postgres;
GRANT ALL ON FUNCTION realtime.topic() TO dashboard_user;


--
-- Name: FUNCTION _crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault._crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION vault._crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea) TO service_role;


--
-- Name: FUNCTION create_secret(new_secret text, new_name text, new_description text, new_key_id uuid); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault.create_secret(new_secret text, new_name text, new_description text, new_key_id uuid) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION vault.create_secret(new_secret text, new_name text, new_description text, new_key_id uuid) TO service_role;


--
-- Name: FUNCTION update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault.update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid) TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION vault.update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid) TO service_role;


--
-- Name: TABLE audit_log_entries; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.audit_log_entries TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.audit_log_entries TO postgres;
GRANT SELECT ON TABLE auth.audit_log_entries TO postgres WITH GRANT OPTION;


--
-- Name: TABLE flow_state; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.flow_state TO postgres;
GRANT SELECT ON TABLE auth.flow_state TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.flow_state TO dashboard_user;


--
-- Name: TABLE identities; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.identities TO postgres;
GRANT SELECT ON TABLE auth.identities TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.identities TO dashboard_user;


--
-- Name: TABLE instances; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.instances TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.instances TO postgres;
GRANT SELECT ON TABLE auth.instances TO postgres WITH GRANT OPTION;


--
-- Name: TABLE mfa_amr_claims; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_amr_claims TO postgres;
GRANT SELECT ON TABLE auth.mfa_amr_claims TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_amr_claims TO dashboard_user;


--
-- Name: TABLE mfa_challenges; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_challenges TO postgres;
GRANT SELECT ON TABLE auth.mfa_challenges TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_challenges TO dashboard_user;


--
-- Name: TABLE mfa_factors; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_factors TO postgres;
GRANT SELECT ON TABLE auth.mfa_factors TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_factors TO dashboard_user;


--
-- Name: TABLE one_time_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.one_time_tokens TO postgres;
GRANT SELECT ON TABLE auth.one_time_tokens TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.one_time_tokens TO dashboard_user;


--
-- Name: TABLE refresh_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.refresh_tokens TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.refresh_tokens TO postgres;
GRANT SELECT ON TABLE auth.refresh_tokens TO postgres WITH GRANT OPTION;


--
-- Name: SEQUENCE refresh_tokens_id_seq; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO dashboard_user;
GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO postgres;


--
-- Name: TABLE saml_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.saml_providers TO postgres;
GRANT SELECT ON TABLE auth.saml_providers TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.saml_providers TO dashboard_user;


--
-- Name: TABLE saml_relay_states; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.saml_relay_states TO postgres;
GRANT SELECT ON TABLE auth.saml_relay_states TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.saml_relay_states TO dashboard_user;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT SELECT ON TABLE auth.schema_migrations TO postgres WITH GRANT OPTION;


--
-- Name: TABLE sessions; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sessions TO postgres;
GRANT SELECT ON TABLE auth.sessions TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sessions TO dashboard_user;


--
-- Name: TABLE sso_domains; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sso_domains TO postgres;
GRANT SELECT ON TABLE auth.sso_domains TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sso_domains TO dashboard_user;


--
-- Name: TABLE sso_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sso_providers TO postgres;
GRANT SELECT ON TABLE auth.sso_providers TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sso_providers TO dashboard_user;


--
-- Name: TABLE users; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.users TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.users TO postgres;
GRANT SELECT ON TABLE auth.users TO postgres WITH GRANT OPTION;


--
-- Name: TABLE job; Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT SELECT ON TABLE cron.job TO postgres WITH GRANT OPTION;


--
-- Name: TABLE job_run_details; Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON TABLE cron.job_run_details TO postgres WITH GRANT OPTION;


--
-- Name: TABLE pg_stat_statements; Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON TABLE extensions.pg_stat_statements FROM postgres;
GRANT ALL ON TABLE extensions.pg_stat_statements TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE extensions.pg_stat_statements TO dashboard_user;


--
-- Name: TABLE pg_stat_statements_info; Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON TABLE extensions.pg_stat_statements_info FROM postgres;
GRANT ALL ON TABLE extensions.pg_stat_statements_info TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE extensions.pg_stat_statements_info TO dashboard_user;


--
-- Name: TABLE admins; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.admins TO anon;
GRANT ALL ON TABLE public.admins TO authenticated;
GRANT ALL ON TABLE public.admins TO service_role;


--
-- Name: TABLE billing_plans; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.billing_plans TO anon;
GRANT ALL ON TABLE public.billing_plans TO authenticated;
GRANT ALL ON TABLE public.billing_plans TO service_role;


--
-- Name: TABLE billing_transactions; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.billing_transactions TO anon;
GRANT ALL ON TABLE public.billing_transactions TO authenticated;
GRANT ALL ON TABLE public.billing_transactions TO service_role;


--
-- Name: TABLE consultations; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.consultations TO anon;
GRANT ALL ON TABLE public.consultations TO authenticated;
GRANT ALL ON TABLE public.consultations TO service_role;


--
-- Name: TABLE contact_requests; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.contact_requests TO anon;
GRANT ALL ON TABLE public.contact_requests TO authenticated;
GRANT ALL ON TABLE public.contact_requests TO service_role;


--
-- Name: TABLE doctors; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.doctors TO anon;
GRANT ALL ON TABLE public.doctors TO authenticated;
GRANT ALL ON TABLE public.doctors TO service_role;


--
-- Name: TABLE referral_analytics; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.referral_analytics TO anon;
GRANT ALL ON TABLE public.referral_analytics TO authenticated;
GRANT ALL ON TABLE public.referral_analytics TO service_role;


--
-- Name: TABLE referral_discounts; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.referral_discounts TO anon;
GRANT ALL ON TABLE public.referral_discounts TO authenticated;
GRANT ALL ON TABLE public.referral_discounts TO service_role;


--
-- Name: TABLE usage_logs; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.usage_logs TO anon;
GRANT ALL ON TABLE public.usage_logs TO authenticated;
GRANT ALL ON TABLE public.usage_logs TO service_role;


--
-- Name: TABLE messages; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON TABLE realtime.messages TO postgres;
GRANT ALL ON TABLE realtime.messages TO dashboard_user;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO anon;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO authenticated;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO service_role;


--
-- Name: TABLE messages_2025_06_05; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_05 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_05 TO dashboard_user;


--
-- Name: TABLE messages_2025_06_06; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_06 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_06 TO dashboard_user;


--
-- Name: TABLE messages_2025_06_07; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_07 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_07 TO dashboard_user;


--
-- Name: TABLE messages_2025_06_08; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_08 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_08 TO dashboard_user;


--
-- Name: TABLE messages_2025_06_09; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_09 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_09 TO dashboard_user;


--
-- Name: TABLE messages_2025_06_10; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_10 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_10 TO dashboard_user;


--
-- Name: TABLE messages_2025_06_11; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_06_11 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_06_11 TO dashboard_user;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.schema_migrations TO postgres;
GRANT ALL ON TABLE realtime.schema_migrations TO dashboard_user;
GRANT SELECT ON TABLE realtime.schema_migrations TO anon;
GRANT SELECT ON TABLE realtime.schema_migrations TO authenticated;
GRANT SELECT ON TABLE realtime.schema_migrations TO service_role;
GRANT ALL ON TABLE realtime.schema_migrations TO supabase_realtime_admin;


--
-- Name: TABLE subscription; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.subscription TO postgres;
GRANT ALL ON TABLE realtime.subscription TO dashboard_user;
GRANT SELECT ON TABLE realtime.subscription TO anon;
GRANT SELECT ON TABLE realtime.subscription TO authenticated;
GRANT SELECT ON TABLE realtime.subscription TO service_role;
GRANT ALL ON TABLE realtime.subscription TO supabase_realtime_admin;


--
-- Name: SEQUENCE subscription_id_seq; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO service_role;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO supabase_realtime_admin;


--
-- Name: TABLE buckets; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.buckets TO anon;
GRANT ALL ON TABLE storage.buckets TO authenticated;
GRANT ALL ON TABLE storage.buckets TO service_role;
GRANT ALL ON TABLE storage.buckets TO postgres;


--
-- Name: TABLE objects; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.objects TO anon;
GRANT ALL ON TABLE storage.objects TO authenticated;
GRANT ALL ON TABLE storage.objects TO service_role;
GRANT ALL ON TABLE storage.objects TO postgres;


--
-- Name: TABLE s3_multipart_uploads; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.s3_multipart_uploads TO service_role;
GRANT SELECT ON TABLE storage.s3_multipart_uploads TO authenticated;
GRANT SELECT ON TABLE storage.s3_multipart_uploads TO anon;


--
-- Name: TABLE s3_multipart_uploads_parts; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.s3_multipart_uploads_parts TO service_role;
GRANT SELECT ON TABLE storage.s3_multipart_uploads_parts TO authenticated;
GRANT SELECT ON TABLE storage.s3_multipart_uploads_parts TO anon;


--
-- Name: TABLE secrets; Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT SELECT,REFERENCES,DELETE,TRUNCATE ON TABLE vault.secrets TO postgres WITH GRANT OPTION;
GRANT SELECT,DELETE ON TABLE vault.secrets TO service_role;


--
-- Name: TABLE decrypted_secrets; Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT SELECT,REFERENCES,DELETE,TRUNCATE ON TABLE vault.decrypted_secrets TO postgres WITH GRANT OPTION;
GRANT SELECT,DELETE ON TABLE vault.decrypted_secrets TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON SEQUENCES TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON FUNCTIONS TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON TABLES TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON SEQUENCES TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON FUNCTIONS TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON TABLES TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES TO service_role;


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


ALTER EVENT TRIGGER issue_graphql_placeholder OWNER TO supabase_admin;

--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


ALTER EVENT TRIGGER issue_pg_cron_access OWNER TO supabase_admin;

--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


ALTER EVENT TRIGGER issue_pg_graphql_access OWNER TO supabase_admin;

--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


ALTER EVENT TRIGGER issue_pg_net_access OWNER TO supabase_admin;

--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


ALTER EVENT TRIGGER pgrst_ddl_watch OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


ALTER EVENT TRIGGER pgrst_drop_watch OWNER TO supabase_admin;

--
-- PostgreSQL database dump complete
--

