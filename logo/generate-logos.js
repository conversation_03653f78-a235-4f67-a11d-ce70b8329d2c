#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Logo sizes we need to generate
const logoSizes = {
  // Favicon sizes
  'favicon-16x16.png': 16,
  'favicon-32x32.png': 32,
  'favicon-48x48.png': 48,
  
  // App icons (PWA/Mobile)
  'icon-72x72.png': 72,
  'icon-96x96.png': 96,
  'icon-128x128.png': 128,
  'icon-144x144.png': 144,
  'icon-152x152.png': 152,
  'icon-192x192.png': 192,
  'icon-384x384.png': 384,
  'icon-512x512.png': 512,
  
  // Social media
  'social-square-400x400.png': 400,
  'og-image-1200x630.png': { width: 1200, height: 630 },
  
  // High resolution
  'logo-1024x1024.png': 1024,
  'logo-2048x2048.png': 2048,
  
  // Apple touch icons
  'apple-touch-icon-180x180.png': 180,
  'apple-touch-icon-167x167.png': 167,
  'apple-touch-icon-152x152.png': 152,
  'apple-touch-icon-120x120.png': 120,
};

console.log('🎨 Celer AI Logo Generator');
console.log('==========================');

// Check if we have the SVG source
const svgPath = path.join(__dirname, 'celer-ai-logo.svg');
if (!fs.existsSync(svgPath)) {
  console.error('❌ SVG source file not found:', svgPath);
  process.exit(1);
}

console.log('✅ Found SVG source file');

// Create directories
const directories = ['favicons', 'app-icons', 'social', 'high-res', 'apple-touch'];
directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  }
});

// Function to determine output directory
function getOutputDir(filename) {
  if (filename.startsWith('favicon-')) return 'favicons';
  if (filename.startsWith('icon-')) return 'app-icons';
  if (filename.startsWith('social-') || filename.startsWith('og-')) return 'social';
  if (filename.startsWith('logo-')) return 'high-res';
  if (filename.startsWith('apple-')) return 'apple-touch';
  return '.';
}

// Read the SVG content
const svgContent = fs.readFileSync(svgPath, 'utf8');

console.log('\n📋 Logo sizes to generate:');
Object.entries(logoSizes).forEach(([filename, size]) => {
  const sizeStr = typeof size === 'number' ? `${size}x${size}` : `${size.width}x${size.height}`;
  console.log(`   ${filename} (${sizeStr})`);
});

console.log('\n🔧 To generate PNG files, you have several options:');
console.log('\n1. Using Sharp (Node.js):');
console.log('   npm install sharp');
console.log('   node generate-with-sharp.js');

console.log('\n2. Using ImageMagick:');
console.log('   brew install imagemagick  # macOS');
console.log('   # Then run the conversion commands below');

console.log('\n3. Using Inkscape:');
console.log('   brew install inkscape  # macOS');
console.log('   # Then run the conversion commands below');

console.log('\n📝 ImageMagick commands:');
Object.entries(logoSizes).forEach(([filename, size]) => {
  const outputDir = getOutputDir(filename);
  const outputPath = path.join(outputDir, filename);
  
  if (typeof size === 'number') {
    console.log(`convert celer-ai-logo.svg -resize ${size}x${size} ${outputPath}`);
  } else {
    console.log(`convert celer-ai-logo.svg -resize ${size.width}x${size.height}! ${outputPath}`);
  }
});

console.log('\n📝 Inkscape commands:');
Object.entries(logoSizes).forEach(([filename, size]) => {
  const outputDir = getOutputDir(filename);
  const outputPath = path.join(outputDir, filename);
  
  if (typeof size === 'number') {
    console.log(`inkscape celer-ai-logo.svg --export-png=${outputPath} --export-width=${size} --export-height=${size}`);
  } else {
    console.log(`inkscape celer-ai-logo.svg --export-png=${outputPath} --export-width=${size.width} --export-height=${size.height}`);
  }
});

// Create a README file
const readmeContent = `# Celer AI Logo Assets

This directory contains all the logo assets for Celer AI in various sizes and formats.

## Directory Structure

- \`favicons/\` - Favicon sizes (16x16, 32x32, 48x48)
- \`app-icons/\` - PWA and mobile app icons
- \`social/\` - Social media and Open Graph images
- \`high-res/\` - High resolution versions for print/design
- \`apple-touch/\` - Apple touch icons for iOS devices

## Source Files

- \`celer-ai-logo.svg\` - Master SVG file (scalable vector)
- \`generate-logos.js\` - Script to generate all sizes

## Usage in Code

### Favicon
\`\`\`html
<link rel="icon" type="image/png" sizes="32x32" href="/logo/favicons/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/logo/favicons/favicon-16x16.png">
\`\`\`

### PWA Manifest
\`\`\`json
{
  "icons": [
    {
      "src": "/logo/app-icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
\`\`\`

### Apple Touch Icons
\`\`\`html
<link rel="apple-touch-icon" sizes="180x180" href="/logo/apple-touch/apple-touch-icon-180x180.png">
\`\`\`

### Open Graph
\`\`\`html
<meta property="og:image" content="/logo/social/og-image-1200x630.png">
\`\`\`

## Design Details

The Celer AI logo features:
- Teal to emerald gradient background (#14b8a6 to #059669)
- White stethoscope icon in the center
- Orange pinging dot in the top-right corner (#fb923c)
- Rounded corners for modern app icon appearance
- Animated ping effect in SVG version

## Regenerating Assets

To regenerate all PNG assets from the SVG source:

1. Install ImageMagick: \`brew install imagemagick\`
2. Run: \`node generate-logos.js\`
3. Copy the generated commands and run them

Or use the automated Sharp script:
1. Install Sharp: \`npm install sharp\`
2. Run: \`node generate-with-sharp.js\`
`;

fs.writeFileSync(path.join(__dirname, 'README.md'), readmeContent);
console.log('\n📖 Created README.md with usage instructions');

console.log('\n✅ Logo generation setup complete!');
console.log('🚀 Run the ImageMagick or Inkscape commands above to generate PNG files');
