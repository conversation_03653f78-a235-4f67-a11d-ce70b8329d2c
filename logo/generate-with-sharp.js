#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Check if Sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.error('❌ Sharp not found. Please install it first:');
  console.error('   npm install sharp');
  process.exit(1);
}

// Logo sizes we need to generate
const logoSizes = {
  // Favicon sizes
  'favicons/favicon-16x16.png': 16,
  'favicons/favicon-32x32.png': 32,
  'favicons/favicon-48x48.png': 48,
  
  // App icons (PWA/Mobile)
  'app-icons/icon-72x72.png': 72,
  'app-icons/icon-96x96.png': 96,
  'app-icons/icon-128x128.png': 128,
  'app-icons/icon-144x144.png': 144,
  'app-icons/icon-152x152.png': 152,
  'app-icons/icon-192x192.png': 192,
  'app-icons/icon-384x384.png': 384,
  'app-icons/icon-512x512.png': 512,
  
  // Social media
  'social/social-square-400x400.png': 400,
  
  // High resolution
  'high-res/logo-1024x1024.png': 1024,
  'high-res/logo-2048x2048.png': 2048,
  
  // Apple touch icons
  'apple-touch/apple-touch-icon-180x180.png': 180,
  'apple-touch/apple-touch-icon-167x167.png': 167,
  'apple-touch/apple-touch-icon-152x152.png': 152,
  'apple-touch/apple-touch-icon-120x120.png': 120,
};

// Special case for Open Graph image (different aspect ratio)
const ogImage = {
  'social/og-image-1200x630.png': { width: 1200, height: 630 }
};

async function generateLogos() {
  console.log('🎨 Generating Celer AI Logo Assets with Sharp');
  console.log('=============================================');

  // Check if we have the SVG source
  const svgPath = path.join(__dirname, 'celer-ai-logo.svg');
  if (!fs.existsSync(svgPath)) {
    console.error('❌ SVG source file not found:', svgPath);
    process.exit(1);
  }

  console.log('✅ Found SVG source file');

  // Create directories
  const directories = ['favicons', 'app-icons', 'social', 'high-res', 'apple-touch'];
  directories.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });

  // Generate square logos
  console.log('\n🔄 Generating square logos...');
  for (const [outputPath, size] of Object.entries(logoSizes)) {
    try {
      const fullOutputPath = path.join(__dirname, outputPath);
      
      await sharp(svgPath)
        .resize(size, size)
        .png({ quality: 100, compressionLevel: 6 })
        .toFile(fullOutputPath);
      
      console.log(`✅ Generated: ${outputPath} (${size}x${size})`);
    } catch (error) {
      console.error(`❌ Failed to generate ${outputPath}:`, error.message);
    }
  }

  // Generate Open Graph image (special aspect ratio)
  console.log('\n🔄 Generating Open Graph image...');
  try {
    const ogOutputPath = path.join(__dirname, 'social/og-image-1200x630.png');
    
    // Create a canvas with the OG dimensions and center the logo
    const logoSize = 400; // Size of the logo on the OG image
    const canvas = sharp({
      create: {
        width: 1200,
        height: 630,
        channels: 4,
        background: { r: 255, g: 255, g: 255, alpha: 1 } // White background
      }
    });

    // Generate the logo at the desired size
    const logoBuffer = await sharp(svgPath)
      .resize(logoSize, logoSize)
      .png()
      .toBuffer();

    // Composite the logo onto the canvas (centered)
    await canvas
      .composite([{
        input: logoBuffer,
        left: Math.round((1200 - logoSize) / 2),
        top: Math.round((630 - logoSize) / 2)
      }])
      .png({ quality: 100 })
      .toFile(ogOutputPath);

    console.log(`✅ Generated: social/og-image-1200x630.png (1200x630)`);
  } catch (error) {
    console.error(`❌ Failed to generate OG image:`, error.message);
  }

  // Generate ICO file for favicon
  console.log('\n🔄 Generating ICO favicon...');
  try {
    const icoOutputPath = path.join(__dirname, 'favicons/favicon.ico');
    
    // Generate multiple sizes for ICO
    const icoSizes = [16, 32, 48];
    const icoBuffers = [];
    
    for (const size of icoSizes) {
      const buffer = await sharp(svgPath)
        .resize(size, size)
        .png()
        .toBuffer();
      icoBuffers.push(buffer);
    }
    
    // Note: Sharp doesn't support ICO creation directly
    // We'll create a 32x32 PNG as favicon.ico for now
    await sharp(svgPath)
      .resize(32, 32)
      .png({ quality: 100 })
      .toFile(icoOutputPath.replace('.ico', '.png'));
    
    console.log(`✅ Generated: favicons/favicon.png (32x32) - rename to .ico if needed`);
  } catch (error) {
    console.error(`❌ Failed to generate ICO:`, error.message);
  }

  console.log('\n📊 Generation Summary:');
  console.log(`   Total files generated: ${Object.keys(logoSizes).length + 2}`);
  console.log(`   Directories created: ${directories.length}`);
  
  console.log('\n🎯 Next Steps:');
  console.log('   1. Copy the generated icons to your app\'s public directory');
  console.log('   2. Update your manifest.json with the new icon paths');
  console.log('   3. Update your HTML head with favicon links');
  console.log('   4. Replace any hardcoded logo references in your code');
  
  console.log('\n✅ Logo generation complete! 🚀');
}

// Run the generator
generateLogos().catch(error => {
  console.error('❌ Generation failed:', error);
  process.exit(1);
});
