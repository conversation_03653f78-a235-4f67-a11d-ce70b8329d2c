<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Celer AI Logo PNG Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .size-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .size-item canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        button {
            background: #14b8a6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #059669;
        }
        .download-all {
            background: #fb923c;
        }
        .download-all:hover {
            background: #ea580c;
        }
        h1 {
            color: #14b8a6;
            text-align: center;
        }
        .instructions {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #14b8a6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Celer AI Logo PNG Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate All Sizes" to create PNG versions</li>
                <li>Right-click on any image and "Save As" to download</li>
                <li>Or click individual "Download" buttons</li>
                <li>Organize files into the appropriate directories as shown in README.md</li>
            </ol>
        </div>

        <button onclick="generateAllSizes()" class="download-all">🚀 Generate All Sizes</button>
        <button onclick="downloadZip()">📦 Download All as ZIP</button>

        <div id="logoContainer" class="logo-preview"></div>
    </div>

    <script>
        const svgContent = `<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="32" y="32" width="448" height="448" rx="64" ry="64" fill="url(#bgGradient)" />
  <rect x="36" y="36" width="448" height="448" rx="64" ry="64" fill="rgba(0,0,0,0.1)" />
  <rect x="32" y="32" width="448" height="448" rx="64" ry="64" fill="url(#bgGradient)" />
  <g transform="translate(256,256) scale(13)" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <g transform="translate(-12,-12)">
      <path d="M11 2v2"/>
      <path d="M5 2v2"/>
      <path d="M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1"/>
      <path d="M8 15a6 6 0 0 0 12 0v-3"/>
      <circle cx="20" cy="10" r="2"/>
    </g>
  </g>
  <g transform="translate(468,44)">
    <circle cx="0" cy="0" r="20" fill="#fb923c"/>
  </g>
</svg>`;

        const sizes = [
            { name: 'favicon-16x16', size: 16, category: 'favicons' },
            { name: 'favicon-32x32', size: 32, category: 'favicons' },
            { name: 'favicon-48x48', size: 48, category: 'favicons' },
            { name: 'icon-72x72', size: 72, category: 'app-icons' },
            { name: 'icon-96x96', size: 96, category: 'app-icons' },
            { name: 'icon-128x128', size: 128, category: 'app-icons' },
            { name: 'icon-144x144', size: 144, category: 'app-icons' },
            { name: 'icon-152x152', size: 152, category: 'app-icons' },
            { name: 'icon-192x192', size: 192, category: 'app-icons' },
            { name: 'icon-384x384', size: 384, category: 'app-icons' },
            { name: 'icon-512x512', size: 512, category: 'app-icons' },
            { name: 'apple-touch-icon-180x180', size: 180, category: 'apple-touch' },
            { name: 'logo-1024x1024', size: 1024, category: 'high-res' },
        ];

        function svgToPng(svgString, width, height) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = width;
                canvas.height = height;

                const img = new Image();
                img.onload = function() {
                    ctx.drawImage(img, 0, 0, width, height);
                    resolve(canvas.toDataURL('image/png'));
                };

                const blob = new Blob([svgString], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                img.src = url;
            });
        }

        async function generateAllSizes() {
            const container = document.getElementById('logoContainer');
            container.innerHTML = '<p>🔄 Generating PNG files...</p>';

            for (const sizeInfo of sizes) {
                const pngDataUrl = await svgToPng(svgContent, sizeInfo.size, sizeInfo.size);
                
                const sizeItem = document.createElement('div');
                sizeItem.className = 'size-item';
                sizeItem.innerHTML = `
                    <h4>${sizeInfo.name}</h4>
                    <p>${sizeInfo.size}x${sizeInfo.size} • ${sizeInfo.category}</p>
                    <img src="${pngDataUrl}" style="max-width: 100px; max-height: 100px; border: 1px solid #ddd;">
                    <br>
                    <button onclick="downloadPng('${pngDataUrl}', '${sizeInfo.name}.png')">Download PNG</button>
                `;
                container.appendChild(sizeItem);
            }
        }

        function downloadPng(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            link.click();
        }

        function downloadZip() {
            alert('For ZIP download, please use the individual download buttons or use the Python script with ImageMagick installed.');
        }

        // Auto-generate on page load
        window.onload = function() {
            setTimeout(generateAllSizes, 1000);
        };
    </script>
</body>
</html>
