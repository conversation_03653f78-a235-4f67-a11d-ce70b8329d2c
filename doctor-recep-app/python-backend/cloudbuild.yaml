steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'asia-south1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/doctor-recep-api', '.']
  
  # Push the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'asia-south1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/doctor-recep-api']
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'doctor-recep-api'
      - '--image'
      - 'asia-south1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/doctor-recep-api'
      - '--region'
      - 'asia-south1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--concurrency'
      - '20'
      - '--max-instances'
      - '10'
      - '--timeout'
      - '300s'

images:
  - 'asia-south1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/doctor-recep-api'
