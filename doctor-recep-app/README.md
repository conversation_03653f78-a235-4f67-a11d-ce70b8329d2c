# Celer AI System - Frontend

## 🎯 **What It Does**

The Celer AI System is a **comprehensive healthcare consultation management platform** built with Next.js 15. It enables doctors and nurses to record patient consultations, upload supporting materials, and generate AI-powered medical summaries using advanced voice and image recognition technology.

### **Core Functionality**
- **🎙️ Audio Recording**: Record patient consultations with high-quality audio capture
- **📸 Image Upload**: Capture prescriptions, medical images, and handwritten notes
- **🤖 AI Summaries**: Generate intelligent medical summaries using Google Gemini 2.5 Flash
- **👥 Multi-User Workflow**: Doctor records → Nurse reviews → Final approval
- **📱 PWA Support**: Mobile-first design with offline capabilities
- **🏥 Multi-Tenant**: Supports multiple doctors/clinics with data isolation

## 🏗️ **How It Works**

### **System Architecture**
```
Mobile PWA (Next.js) → Python Backend (FastAPI) → Gemini AI
        ↓                       ↓
Supabase Storage        Supabase Database
```

### **User Workflow**
1. **🩺 Doctor Records**: Captures audio + images during consultation
2. **☁️ Cloud Upload**: Files stored securely in Supabase Storage
3. **🤖 AI Processing**: Python backend processes files through Gemini API
4. **📝 Summary Generation**: AI generates structured medical summary
5. **👩‍⚕️ Nurse Review**: Nurse reviews and edits summary if needed
6. **✅ Final Approval**: Approved summaries ready for patient records

### **Technical Stack**
- **Frontend**: Next.js 15 with React 19
- **Backend**: Python FastAPI with Google Gemini 2.5 Flash
- **Database**: Supabase (PostgreSQL)
- **Storage**: Supabase Storage (file management)
- **Authentication**: Custom JWT sessions
- **Deployment**: Vercel (frontend) + Google Cloud Run (backend)
- **PWA**: Service workers, offline support, mobile installation

## 👥 **Who It's For**

### **Primary Users**

#### **🩺 Doctors**
- **Record Consultations**: Quick audio recording during patient visits
- **Upload Supporting Materials**: Add prescription images, test results
- **Review AI Summaries**: Edit and refine generated summaries
- **Manage Templates**: Customize summary formats and sections
- **Track Quota**: Monitor monthly AI generation usage

#### **👩‍⚕️ Nurses/Receptionists**
- **Review Summaries**: Quality check AI-generated content
- **Edit Content**: Make corrections and additions
- **Approve Records**: Final approval for patient documentation
- **Manage Workflow**: Track pending and completed consultations

#### **🏥 Clinic Administrators**
- **User Management**: Approve new doctor registrations
- **Quota Management**: Set and monitor AI usage limits
- **System Monitoring**: Track system usage and performance
- **Multi-Clinic Support**: Manage multiple clinic locations

### **Technical Stakeholders**

#### **🔧 Healthcare IT Teams**
- **Integration**: Connect with existing EMR systems
- **Compliance**: HIPAA-ready architecture and data handling
- **Scalability**: Support growing clinic networks
- **Security**: Multi-tenant data isolation and encryption

#### **📊 System Administrators**
- **Deployment**: Easy deployment to Vercel and Cloud Run
- **Monitoring**: Comprehensive logging and error tracking
- **Maintenance**: Automated backups and file retention
- **Performance**: Optimized for mobile and desktop usage

## 🚀 **Production Deployment**

### **Frontend Deployment (Vercel)**
```bash
# Set environment variables
export NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
export NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
export SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
export SESSION_SECRET=your_32_char_secret
export NEXT_PUBLIC_API_URL=https://your-backend.run.app

# Deploy
./deploy-frontend.sh
```

### **Backend Deployment (Cloud Run)**
```bash
# Set environment variables
export PROJECT_ID=your-gcp-project
export GEMINI_API_KEY=your_gemini_key
export FRONTEND_URL=https://your-frontend.vercel.app

# Deploy
cd python-backend
./deploy.sh
```

### **Database Setup (Supabase)**
1. Create Supabase project
2. Run `database/schema.sql` in SQL Editor
3. Create storage buckets: `consultation-audio`, `consultation-images`
4. Configure RLS policies for multi-tenancy

## 📱 **Mobile PWA Features**

### **Installation**
- **Add to Home Screen**: One-tap installation on mobile devices
- **Offline Support**: Core functionality works without internet
- **Native Feel**: App-like experience with custom splash screen
- **Push Notifications**: Ready for future notification features

### **Mobile Optimizations**
- **Touch-Friendly**: Large buttons and touch targets
- **Responsive Design**: Adapts to all screen sizes
- **Camera Integration**: Direct camera access for image capture
- **Microphone Access**: High-quality audio recording
- **Gesture Support**: Swipe and touch gestures

## 🔒 **Security & Compliance**

### **Data Protection**
- **Multi-Tenant Isolation**: Doctor data completely separated
- **Encrypted Storage**: All files encrypted at rest
- **Secure Transmission**: HTTPS everywhere
- **Session Security**: JWT tokens with secure cookies
- **File Retention**: Automatic cleanup after 30 days

### **Authentication & Authorization**
- **Custom Auth**: JWT-based session management
- **Role-Based Access**: Doctor, nurse, admin roles
- **Session Expiry**: 7-day session timeout
- **Password Security**: Bcrypt hashing with salt

### **HIPAA Readiness**
- **Audit Logging**: Comprehensive activity tracking
- **Data Minimization**: Only necessary data collected
- **Access Controls**: Strict user permissions
- **Encryption**: End-to-end data protection

## 📊 **Scalability & Performance**

### **Frontend Optimization**
- **Next.js 15**: Latest performance optimizations
- **Image Optimization**: Automatic image compression and resizing
- **Code Splitting**: Lazy loading for faster initial load
- **Caching**: Aggressive caching strategies
- **CDN**: Global content delivery via Vercel

### **Database Performance**
- **Connection Pooling**: Efficient database connections
- **Indexing**: Optimized queries with proper indexes
- **RLS Policies**: Row-level security for data isolation
- **Backup Strategy**: Automated daily backups

### **File Storage**
- **Supabase Storage**: Scalable object storage
- **CDN Integration**: Fast file delivery worldwide
- **Automatic Cleanup**: 30-day retention policy
- **Size Limits**: 50MB per file, 100MB per consultation

## 🛠️ **Development Setup**

### **Prerequisites**
- Node.js 18+
- Python 3.11+
- Supabase account
- Google Cloud account
- Gemini API key

### **Local Development**
```bash
# Frontend
npm install
npm run dev

# Backend
cd python-backend
pip install -r requirements.txt
python main.py

# Database
# Run schema.sql in Supabase SQL Editor
```

### **Environment Configuration**
```bash
# Frontend (.env.local)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SESSION_SECRET=your_session_secret
NEXT_PUBLIC_API_URL=http://localhost:8080

# Backend (.env)
GEMINI_API_KEY=your_gemini_key
FRONTEND_URL=http://localhost:3000
```

## 🎯 **Key Features**

### **🎙️ Audio Recording**
- **High-Quality Capture**: Professional audio recording
- **Multiple Formats**: Support for various audio formats
- **Real-Time Feedback**: Visual recording indicators
- **Playback Controls**: Review recordings before upload

### **📸 Image Management**
- **Camera Integration**: Direct camera capture
- **File Upload**: Drag-and-drop file upload
- **Image Preview**: Thumbnail previews with zoom
- **Format Support**: JPEG, PNG, WebP, HEIC

### **🤖 AI Processing**
- **Gemini 2.5 Flash**: Latest AI model for medical summaries
- **Multi-Modal**: Processes both audio and images together
- **Template Customization**: Personalized summary formats
- **Language Support**: Multiple languages and tones

### **👥 Workflow Management**
- **Status Tracking**: Pending → Generated → Approved
- **Role-Based Views**: Different interfaces for doctors/nurses
- **Batch Operations**: Process multiple consultations
- **Search & Filter**: Find consultations quickly

### **📊 Analytics & Reporting**
- **Usage Tracking**: Monitor AI generation quota
- **Performance Metrics**: Track consultation processing times
- **Error Reporting**: Comprehensive error logging
- **Audit Trail**: Complete activity history

## 🔧 **API Integration**

### **Frontend → Backend**
- **RESTful API**: Clean API design
- **Error Handling**: Graceful error management
- **Retry Logic**: Automatic retry for failed requests
- **Loading States**: User-friendly loading indicators

### **Backend → Gemini**
- **Files API**: Efficient file upload to Gemini
- **Batch Processing**: Handle multiple files simultaneously
- **Error Recovery**: Continue processing despite partial failures
- **Rate Limiting**: Respect API limits

## 🎨 **User Experience**

### **Design Principles**
- **Mobile-First**: Designed for mobile usage
- **Accessibility**: WCAG 2.1 compliance
- **Intuitive Navigation**: Clear user flows
- **Consistent UI**: Unified design system

### **Performance**
- **Fast Loading**: < 2 second initial load
- **Smooth Interactions**: 60fps animations
- **Offline Support**: Core features work offline
- **Progressive Enhancement**: Works on all devices

## 🤝 **Integration Capabilities**

### **EMR Integration**
- **API-First Design**: Easy integration with existing systems
- **Standard Formats**: HL7 FHIR compatibility ready
- **Webhook Support**: Real-time data synchronization
- **Export Options**: Multiple export formats

### **Third-Party Services**
- **Cloud Storage**: Extensible to other storage providers
- **AI Models**: Pluggable AI service architecture
- **Authentication**: SSO integration ready
- **Monitoring**: APM tool integration

## 📈 **Future Roadmap**

- **🔊 Real-Time Transcription**: Live audio-to-text conversion
- **📋 Template Marketplace**: Shared summary templates
- **📊 Advanced Analytics**: Detailed usage insights
- **🌍 Multi-Language**: Expanded language support
- **🔗 EMR Integration**: Direct EMR system connections
- **📱 Native Apps**: iOS and Android native applications
