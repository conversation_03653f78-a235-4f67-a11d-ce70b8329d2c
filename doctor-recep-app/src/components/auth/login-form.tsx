'use client'

import { useActionState, useEffect } from 'react'
import { login } from '@/lib/actions/auth'
import { FormState } from '@/lib/types'
import { trackAuth } from '@/lib/analytics'

// CORRECTED initialState to conform to FormState interface
const initialState: FormState = {
  success: false,
  message: '',
  // fieldErrors is optional, so it can be omitted or set to an empty object if needed
  // fieldErrors: {},
}

export function LoginForm() {
  const [state, formAction, isPending] = useActionState(login, initialState)

  // Track successful login (when redirecting to dashboard)
  useEffect(() => {
    if (state?.success && state?.message?.includes('Redirecting')) {
      trackAuth('login_successful')
    }
  }, [state?.success, state?.message])

  // Handle form submission to track login attempt
  const handleSubmit = (formData: FormData) => {
    // Track login attempt
    trackAuth('login_attempted')

    // Call the original form action
    formAction(formData)
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
            Email Address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="<EMAIL>"
          />
          {state?.fieldErrors?.email && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.email[0]}</span>
            </p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-2">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900"
            placeholder="Enter your password"
          />
          {state?.fieldErrors?.password && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span className="w-1 h-1 bg-red-600 rounded-full"></span>
              <span>{state.fieldErrors.password[0]}</span>
            </p>
          )}
        </div>
      </div>

      {state?.message && (
        <div className="rounded-xl p-4 border bg-gradient-to-r from-red-50 to-pink-50 border-red-200">
          <p className="text-sm font-medium flex items-center space-x-2 text-red-800">
            <span className="w-2 h-2 rounded-full bg-red-400"></span>
            <span>{state.message}</span>
          </p>
        </div>
      )}

      <div>
        <button
          type="submit"
          disabled={isPending}
          className="group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
        >
          {isPending ? (
            <>
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Signing you in...</span>
            </>
          ) : (
            <>
              <span>Continue the Magic</span>
              <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </>
          )}
        </button>
      </div>
    </form>
  )
}