'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { LogOut, Shield, BarChart3, Stethoscope, Bell } from 'lucide-react'
import { adminLogout } from '@/lib/actions/admin-auth'
import { Admin } from '@/lib/types'
import { getContactRequestsCount } from '@/lib/actions/contact-requests'
import { createClient } from '@/lib/supabase/client'

interface AdminDashboardHeaderProps {
  admin: Admin | null
}

export function AdminDashboardHeader({ admin }: AdminDashboardHeaderProps) {
  const router = useRouter()
  const [pendingRequests, setPendingRequests] = useState(0)

  useEffect(() => {
    const fetchRequestsCount = async () => {
      try {
        const result = await getContactRequestsCount()
        if (result.success) {
          setPendingRequests(result.data.pending || 0)
        }
      } catch (error) {
        console.error('Error fetching contact requests:', error)
      }
    }

    // Initial fetch
    fetchRequestsCount()

    // Set up Supabase Realtime subscription for contact_requests
    const supabase = createClient()
    const channel = supabase
      .channel('contact_requests_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'contact_requests'
        },
        (payload) => {
          console.log('Contact request change detected:', payload)
          // Refetch count when any change happens to contact_requests table
          fetchRequestsCount()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [])

  const handleLogout = async () => {
    try {
      await adminLogout()
      router.push('/admin/login')
    } catch (error) {
      console.error('Logout error:', error)
      // Force redirect even if there's an error
      router.push('/admin/login')
    }
  }

  return (
    <header className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-6">
          {/* Left side - Admin info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Shield className="w-6 h-6 text-red-600" />
                <h2 className="text-lg font-bold text-slate-900">
                  Admin Dashboard
                </h2>
              </div>
              {admin?.role === 'super_admin' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Super Admin
                </span>
              )}
            </div>
            <p className="text-sm text-slate-900 mt-1">
              Welcome back, {admin?.name}
              <span className="ml-2 text-slate-600">• {admin?.email}</span>
            </p>
          </div>

          {/* Center - Celer AI Logo */}
          <div className="flex items-center space-x-4 flex-shrink-0 mx-6">
            <div className="relative">
              <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                <Stethoscope className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping"></div>
              <div className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full"></div>
            </div>
            <div className="text-center">
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent">
                Celer AI
              </h1>
              <p className="text-sm text-teal-600/80">Admin Portal</p>
            </div>
          </div>

          {/* Right side - Navigation and logout */}
          <div className="flex items-center space-x-4 flex-1 justify-end">
            {/* Contact Requests Notification */}
            {pendingRequests > 0 && (
              <Link
                href="/admin/dashboard?tab=billing"
                className="flex items-center space-x-1 px-3 py-2 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-md transition-colors"
              >
                <Bell className="w-4 h-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">
                  {pendingRequests} pending contact{pendingRequests !== 1 ? 's' : ''}
                </span>
              </Link>
            )}

            {/* Navigation Links */}
            <nav className="hidden md:flex space-x-4">
              <Link
                href="/admin/dashboard"
                className="flex items-center space-x-1 text-slate-900 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                <BarChart3 className="w-4 h-4" />
                <span>Dashboard</span>
              </Link>
            </nav>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-1 text-slate-900 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium"
            >
              <LogOut className="w-4 h-4" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}
