'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import { 
  CreditCard, 
  DollarSign, 
  Users, 
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Clock,
  Gift,
  Search,
  Bell,
  Phone,
  MessageCircle
} from 'lucide-react'
import { PlanSelectionModal } from '@/components/ui/plan-selection-modal'
import { 
  getBillingStats, 
  getDoctorsBillingInfo, 
  getAllBillingTransactions,
  markPaymentPaid,
  createBillingTransaction,
  getBillingPlans,
  type DoctorBillingInfo,
  type BillingTransaction,
  type BillingPlan
} from '@/lib/actions/billing'
import { 
  getContactRequests, 
  updateContactRequestStatus, 
  getContactRequestsCount,
  type ContactRequest 
} from '@/lib/actions/contact-requests'
import { createClient } from '@/lib/supabase/client'

interface BillingStats {
  total_revenue: number
  monthly_revenue: number
  pending_payments: number
  active_subscriptions: number
  trial_users: number
  referral_discounts_given: number
}

interface RequestsCount {
  total: number
  pending: number
  contacted: number
  resolved: number
}

// A generic type for the referral discount payload since we don't have a specific type for it
interface ReferralDiscount {
  id: string
  status: 'pending' | 'applied' | 'expired'
  // Add other properties if needed
}

export function BillingManagement() {
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'doctors' | 'plans' | 'requests'>('overview')
  const [billingStats, setBillingStats] = useState<BillingStats | null>(null)
  const [doctors, setDoctors] = useState<DoctorBillingInfo[]>([])
  const [transactions, setTransactions] = useState<BillingTransaction[]>([])
  const [plans, setPlans] = useState<BillingPlan[]>([])
  const [contactRequests, setContactRequests] = useState<ContactRequest[]>([])
  const [requestsCount, setRequestsCount] = useState<RequestsCount | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [processing, setProcessing] = useState<string | null>(null)
  const [showPlanModal, setShowPlanModal] = useState(false)
  const [selectedDoctor, setSelectedDoctor] = useState<DoctorBillingInfo | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const activeTabRef = useRef(activeTab);

  useEffect(() => {
    activeTabRef.current = activeTab;
  }, [activeTab]);

  const loadContactRequestsData = useCallback(async () => {
    try {
      const [requestsResult, requestsCountResult] = await Promise.all([
        getContactRequests(),
        getContactRequestsCount()
      ])
      if (requestsResult.success) setContactRequests(requestsResult.data)
      if (requestsCountResult.success) setRequestsCount(requestsCountResult.data)
    } catch (error) { console.error('Error loading contact requests data:', error) }
  }, [])

  const loadBillingData = useCallback(async () => {
    try {
      const [statsResult, transactionsResult] = await Promise.all([
        getBillingStats(),
        getAllBillingTransactions(100)
      ])
      if (statsResult.success) setBillingStats(statsResult.data)
      if (transactionsResult.success) setTransactions(transactionsResult.data.transactions)
    } catch (error) { console.error('Error loading billing data:', error) }
  }, [])

  const loadDoctorsData = useCallback(async () => {
    try {
      const doctorsResult = await getDoctorsBillingInfo()
      if (doctorsResult.success) setDoctors(doctorsResult.data)
    } catch (error) { console.error('Error loading doctors data:', error) }
  }, [])

  const loadData = useCallback(async () => {
    setLoading(true)
    try {
      const [statsResult, requestsCountResult, plansResult] = await Promise.all([
        getBillingStats(),
        getContactRequestsCount(),
        getBillingPlans()
      ])

      if (statsResult.success) setBillingStats(statsResult.data)
      if (requestsCountResult.success) setRequestsCount(requestsCountResult.data)
      if (plansResult.success) setPlans(plansResult.data)

      if (activeTab === 'transactions') await getAllBillingTransactions(50).then(r => r.success && setTransactions(r.data.transactions))
      else if (activeTab === 'doctors') await getDoctorsBillingInfo().then(r => r.success && setDoctors(r.data))
      else if (activeTab === 'requests') await getContactRequests().then(r => r.success && setContactRequests(r.data))

    } catch (error) {
      console.error('Error loading billing data:', error)
    } finally {
      setLoading(false)
    }
  }, [activeTab])

  useEffect(() => {
    loadData()
  }, [loadData])

  useEffect(() => {
    const supabase = createClient()
    
    const contactRequestsChannel = supabase.channel('contact_requests_billing_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'contact_requests' }, 
        (payload: RealtimePostgresChangesPayload<ContactRequest>) => {
          console.log('Contact request change detected:', payload)
          if (activeTabRef.current === 'requests' || payload.eventType === 'INSERT') {
            loadContactRequestsData()
          } else {
            getContactRequestsCount().then(r => r.success && setRequestsCount(r.data))
          }
        }
      ).subscribe()

    const transactionsChannel = supabase.channel('billing_transactions_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'billing_transactions' },
        (payload: RealtimePostgresChangesPayload<BillingTransaction>) => {
          console.log('Billing transaction change detected:', payload)
          getBillingStats().then(r => r.success && setBillingStats(r.data))
          if (activeTabRef.current === 'transactions') {
            loadBillingData()
          }
        }
      ).subscribe()

    const doctorsChannel = supabase.channel('doctors_billing_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'doctors' },
        (payload: RealtimePostgresChangesPayload<DoctorBillingInfo>) => {
          console.log('Doctor billing info change detected:', payload)
          getBillingStats().then(r => r.success && setBillingStats(r.data))
          if (activeTabRef.current === 'doctors') {
            loadDoctorsData()
          }
        }
      ).subscribe()

    const referralDiscountsChannel = supabase.channel('referral_discounts_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'referral_discounts' },
        (payload: RealtimePostgresChangesPayload<ReferralDiscount>) => {
          console.log('Referral discount change detected:', payload)
          if (payload.eventType === 'INSERT' || (payload.eventType === 'UPDATE' && payload.new?.status === 'applied')) {
            getBillingStats().then(r => r.success && setBillingStats(r.data))
          }
        }
      ).subscribe()

    return () => {
      supabase.removeChannel(contactRequestsChannel)
      supabase.removeChannel(transactionsChannel)
      supabase.removeChannel(doctorsChannel)
      supabase.removeChannel(referralDiscountsChannel)
    }
  }, [loadBillingData, loadContactRequestsData, loadDoctorsData])

  const handleMarkPaid = async (transactionId: string) => {
    const confirmed = confirm('Are you sure you want to mark this payment as paid?')
    if (!confirmed) return
    setProcessing(transactionId)
    setMessage(null)
    try {
      const result = await markPaymentPaid(transactionId, 'admin_manual', `Manual payment confirmation`)
      if (result.success) {
        setMessage({ type: 'success', text: 'Payment marked as paid successfully!' })
        await loadData() // Re-fetch data after action
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to mark payment as paid' })
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred.' })
    } finally {
      setProcessing(null)
    }
  }

  const handleCreateBill = async (doctorId: string, planId: string, planPrice: number) => {
    setProcessing(doctorId)
    setMessage(null)
    try {
      const result = await createBillingTransaction(doctorId, planId, planPrice)
      if (result.success) {
        setShowPlanModal(false)
        setSelectedDoctor(null)
        await loadData() // Re-fetch data after action
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to create bill' })
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred.' })
    } finally {
      setProcessing(null)
    }
  }

  const handleUpdateRequestStatus = async (requestId: string, status: 'pending' | 'contacted' | 'resolved') => {
    setProcessing(requestId)
    setMessage(null)
    try {
      const result = await updateContactRequestStatus(requestId, status)
      if (result.success) {
        await loadData() // Re-fetch data after action
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update status' })
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred.' })
    } finally {
      setProcessing(null)
    }
  }

  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-600" />
      default: return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getBillingStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'trial': return 'bg-blue-100 text-blue-800'
      case 'suspended': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredDoctors = doctors.filter(doctor => 
    doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doctor.email.toLowerCase().includes(searchTerm.toLowerCase())
  ).filter(doctor => 
    statusFilter === 'all' || doctor.billing_status === statusFilter
  )

  const filteredTransactions = transactions.filter(transaction =>
    transaction.doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.doctor.email.toLowerCase().includes(searchTerm.toLowerCase())
  ).filter(transaction =>
    statusFilter === 'all' || transaction.payment_status === statusFilter
  )

  if (loading && !billingStats) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
            {[1, 2, 3, 4].map(i => <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>)}
          </div>
          <div className="bg-gray-200 h-64 rounded-lg mt-6"></div>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview' as const, label: 'Overview', icon: TrendingUp },
    { id: 'transactions' as const, label: 'Transactions', icon: CreditCard },
    { id: 'doctors' as const, label: 'Doctors', icon: Users },
    { id: 'plans' as const, label: 'Plans', icon: Gift },
    { id: 'requests' as const, label: `Requests ${requestsCount?.pending ? `(${requestsCount.pending})` : ''}`, icon: Bell }
  ]

  // The rest of the JSX is identical to your original, working code.
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-slate-900 mb-2">Billing Management</h1>
        <p className="text-slate-800">Manage payments, subscriptions, and referral discounts</p>
      </div>

      {message && (
        <div className={`mb-6 p-4 rounded-md ${
          message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <p className="text-sm font-medium">{message.text}</p>
        </div>
      )}

      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setSearchTerm('');
                  setStatusFilter('all');
                }}
                className={`flex-shrink-0 flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {activeTab === 'overview' && billingStats && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Total Revenue</p><p className="text-2xl font-bold text-slate-900">{formatCurrency(billingStats.total_revenue)}</p></div><DollarSign className="w-8 h-8 text-green-600" /></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Monthly Revenue</p><p className="text-2xl font-bold text-slate-900">{formatCurrency(billingStats.monthly_revenue)}</p></div><TrendingUp className="w-8 h-8 text-blue-600" /></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Pending Payments</p><p className="text-2xl font-bold text-slate-900">{formatCurrency(billingStats.pending_payments)}</p></div><Clock className="w-8 h-8 text-yellow-600" /></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Active Subscriptions</p><p className="text-2xl font-bold text-slate-900">{billingStats.active_subscriptions}</p></div><Users className="w-8 h-8 text-purple-600" /></div></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><h3 className="text-lg font-medium text-slate-900 mb-4">User Distribution</h3><div className="space-y-3"><div className="flex justify-between"><span className="text-slate-800">Trial Users</span><span className="font-medium text-slate-900">{billingStats.trial_users}</span></div><div className="flex justify-between"><span className="text-slate-800">Active Subscribers</span><span className="font-medium text-slate-900">{billingStats.active_subscriptions}</span></div></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><h3 className="text-lg font-medium text-slate-900 mb-4">Referral Program</h3><div className="space-y-3"><div className="flex justify-between"><span className="text-slate-800">Total Discounts Given</span><span className="font-medium text-slate-900">{formatCurrency(billingStats.referral_discounts_given)}</span></div></div></div>
          </div>
        </div>
      )}

      {activeTab === 'transactions' && (
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1"><Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" /><input type="text" placeholder="Search transactions..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-slate-800 bg-white" /></div>
            <select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="px-4 py-2 border border-gray-300 rounded-md text-slate-800 bg-white"><option value="all">All Status</option><option value="pending">Pending</option><option value="paid">Paid</option><option value="failed">Failed</option></select>
          </div>
          <div className="bg-white shadow rounded-lg overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200"><thead className="bg-gray-50"><tr><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Doctor</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Amount</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Status</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Period</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Actions</th></tr></thead><tbody className="bg-white divide-y divide-gray-200">{filteredTransactions.map((transaction) => (<tr key={transaction.id}><td className="px-6 py-4 whitespace-nowrap"><div><div className="text-sm font-medium text-slate-900">{transaction.doctor.name}</div><div className="text-sm text-slate-600">{transaction.doctor.email}</div></div></td><td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-slate-900">{formatCurrency(transaction.final_amount)}{transaction.discount_amount > 0 && (<div className="text-xs text-green-600">{formatCurrency(transaction.discount_amount)} discount applied</div>)}</div></td><td className="px-6 py-4 whitespace-nowrap"><div className="flex items-center space-x-2">{getStatusIcon(transaction.payment_status)}<span className="text-sm capitalize">{transaction.payment_status}</span></div></td><td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{new Date(transaction.billing_period_start).toLocaleDateString()} - {new Date(transaction.billing_period_end).toLocaleDateString()}</td><td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">{transaction.payment_status === 'pending' && (<button onClick={() => handleMarkPaid(transaction.id)} disabled={processing === transaction.id} className="text-green-600 hover:text-green-900 disabled:opacity-50" title="Mark Payment as Paid">{processing === transaction.id ? 'Processing...' : 'Mark Paid'}</button>)}</td></tr>))}</tbody></table>
            {filteredTransactions.length === 0 && !loading && (<div className="text-center py-12 text-gray-500">No transactions found.</div>)}
          </div>
        </div>
      )}

      {activeTab === 'doctors' && (
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1"><Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" /><input type="text" placeholder="Search doctors..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-slate-800 bg-white" /></div>
            <select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="px-4 py-2 border border-gray-300 rounded-md text-slate-800 bg-white"><option value="all">All Status</option><option value="trial">Trial</option><option value="active">Active</option><option value="suspended">Suspended</option><option value="cancelled">Cancelled</option></select>
          </div>
          <div className="bg-white shadow rounded-lg overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200"><thead className="bg-gray-50"><tr><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Doctor</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Status</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Total Paid</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Referrals</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Available Discount</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Actions</th></tr></thead><tbody className="bg-white divide-y divide-gray-200">{filteredDoctors.map((doctor) => (<tr key={doctor.id}><td className="px-6 py-4 whitespace-nowrap"><div><div className="text-sm font-medium text-slate-900">{doctor.name}</div><div className="text-sm text-slate-600">{doctor.email}</div>{doctor.clinic_name && (<div className="text-xs text-slate-500">{doctor.clinic_name}</div>)}</div></td><td className="px-6 py-4 whitespace-nowrap"><span className={`inline-flex px-2 py-1 text-xs rounded-full font-medium ${getBillingStatusColor(doctor.billing_status)}`}>{doctor.billing_status}</span></td><td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">{formatCurrency(doctor.total_paid)}{doctor.pending_payments > 0 && (<div className="text-xs text-yellow-600">{formatCurrency(doctor.pending_payments)} pending</div>)}</td><td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{doctor.referral_info.successful_referrals} successful{doctor.referral_info.referred_by && (<div className="text-xs text-blue-600">Referred by {doctor.referral_info.referred_by}</div>)}</td><td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">{formatCurrency(doctor.available_discount_amount)}</td><td className="px-6 py-4 whitespace-nowrap text-sm space-x-2"><button onClick={() => { setSelectedDoctor(doctor); setShowPlanModal(true); }} className="text-blue-600 hover:text-blue-900">Create Bill</button></td></tr>))}</tbody></table>
            {filteredDoctors.length === 0 && !loading && (<div className="text-center py-12 text-gray-500">No doctors found.</div>)}
          </div>
        </div>
      )}

      {activeTab === 'plans' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">{plans.map((plan) => (<div key={plan.id} className="bg-white p-6 rounded-lg shadow border border-gray-200"><h3 className="text-lg font-medium text-slate-900 mb-2">{plan.name}</h3>{plan.description && (<p className="text-slate-800 mb-4">{plan.description}</p>)}<div className="text-2xl font-bold text-slate-900 mb-4">{formatCurrency(plan.monthly_price)}/month</div><div className="text-sm text-slate-800 mb-4">{plan.quota_limit} consultations</div>{plan.features && typeof plan.features === 'object' && 'features' in plan.features && Array.isArray((plan.features as { features: string[] }).features) && (<ul className="text-sm text-slate-800 space-y-1">{((plan.features as { features: string[] }).features).map((feature: string, index: number) => (<li key={index} className="flex items-center space-x-2"><CheckCircle className="w-3 h-3 text-green-500" /><span>{feature}</span></li>))}</ul>)}</div>))}</div>
      )}

      {activeTab === 'requests' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Total Requests</p><p className="text-2xl font-bold text-slate-900">{requestsCount?.total || 0}</p></div><MessageCircle className="w-8 h-8 text-blue-600" /></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Pending</p><p className="text-2xl font-bold text-orange-600">{requestsCount?.pending || 0}</p></div><Clock className="w-8 h-8 text-orange-600" /></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Contacted</p><p className="text-2xl font-bold text-blue-600">{requestsCount?.contacted || 0}</p></div><Phone className="w-8 h-8 text-blue-600" /></div></div>
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200"><div className="flex items-center justify-between"><div><p className="text-sm font-medium text-slate-900">Resolved</p><p className="text-2xl font-bold text-green-600">{requestsCount?.resolved || 0}</p></div><CheckCircle className="w-8 h-8 text-green-600" /></div></div>
          </div>
          <div className="bg-white shadow rounded-lg overflow-x-auto">
            <div className="px-6 py-4 border-b border-gray-200"><h3 className="text-lg font-medium text-slate-900">Contact Requests</h3><p className="mt-1 text-sm text-slate-800">Doctors requesting quota upgrades or plan changes</p></div>
            <table className="min-w-full divide-y divide-gray-200"><thead className="bg-gray-50"><tr><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Doctor</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Request Type</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Contact Info</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Status</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Date</th><th className="px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">Actions</th></tr></thead><tbody className="bg-white divide-y divide-gray-200">{contactRequests.map((request) => (<tr key={request.id} className={request.status === 'pending' ? 'bg-orange-50' : ''}><td className="px-6 py-4 whitespace-nowrap"><div><div className="text-sm font-medium text-slate-900">{request.doctor_name}</div><div className="text-sm text-slate-600">{request.doctor_email}</div>{request.clinic_name && (<div className="text-xs text-slate-500">{request.clinic_name}</div>)}</div></td><td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-slate-900">{request.request_type}</div></td><td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-slate-900">+91 8921628177</div><a href={`mailto:${request.doctor_email}`} className="text-xs text-blue-600 hover:text-blue-900">Send Email</a></td><td className="px-6 py-4 whitespace-nowrap"><span className={`inline-flex px-2 py-1 text-xs rounded-full font-medium ${request.status === 'pending' ? 'bg-orange-100 text-orange-800' : request.status === 'contacted' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>{request.status}</span></td><td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{new Date(request.created_at).toLocaleDateString()}</td><td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">{request.status === 'pending' && (<> <button onClick={() => handleUpdateRequestStatus(request.id, 'contacted')} disabled={processing === request.id} className="text-blue-600 hover:text-blue-900 disabled:opacity-50">Mark Contacted</button><button onClick={() => handleUpdateRequestStatus(request.id, 'resolved')} disabled={processing === request.id} className="text-green-600 hover:text-green-900 disabled:opacity-50">Mark Resolved</button> </>)}{request.status === 'contacted' && (<button onClick={() => handleUpdateRequestStatus(request.id, 'resolved')} disabled={processing === request.id} className="text-green-600 hover:text-green-900 disabled:opacity-50">Mark Resolved</button>)}</td></tr>))}</tbody></table>
            {contactRequests.length === 0 && !loading && (
              <div className="text-center py-12"><MessageCircle className="mx-auto h-12 w-12 text-gray-400" /><h3 className="mt-2 text-sm font-medium text-slate-900">No contact requests</h3><p className="mt-1 text-sm text-slate-600">No doctors have requested contact yet.</p></div>
            )}
          </div>
        </div>
      )}

      <PlanSelectionModal
        isOpen={showPlanModal}
        onClose={() => {
          setShowPlanModal(false)
          setSelectedDoctor(null)
        }}
        onSelectPlan={(planId, planPrice) => {
          if (selectedDoctor) {
            handleCreateBill(selectedDoctor.id, planId, planPrice)
          }
        }}
        plans={plans}
        doctorName={selectedDoctor?.name || ''}
        isLoading={!!processing}
      />
    </div>
  )
}