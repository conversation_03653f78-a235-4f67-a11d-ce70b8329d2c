'use client'

import { useState, useRef, useEffect, useCallback,useMemo } from 'react'
import { Mic, Square, Play, Pause, Upload, Wand2, Edit3, Copy, X, ChevronDown, ChevronUp, Check, Loader2, Trash2, Save, Plus } from 'lucide-react'
import { Consultation } from '@/lib/types'
// import { formatDuration } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { ContentEditableEditor } from './content-editable-editor'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { createConsultationWithFiles, approveConsultation, addAdditionalAudio, addAdditionalImages, updateConsultationType, saveEditedNote, clearEditedNote, deleteAdditionalAudio, deleteConsultationImage, updatePatientName } from '@/lib/actions/consultations'
import { useAutosave } from '@/hooks/useAutosave'
import { AutoSaveIndicator } from '@/components/ui/autosave-indicator'
import { trackConsultation } from '@/lib/analytics'

import Image from 'next/image'

// Convert basic markdown to HTML for display
function convertMarkdownToHtml(markdown: string): string {
  return markdown
    // Convert **bold** to <strong>bold</strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert *italic* to <em>italic</em>
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert line breaks to <br> tags
    .replace(/\n/g, '<br>')
    // Convert double line breaks to paragraphs
    .replace(/<br><br>/g, '</p><p>')
    // Split by paragraph breaks and wrap each section in <p> tags
    .split('</p><p>')
    .map(section => section.trim())
    .filter(section => section.length > 0)
    .map(section => `<p>${section}</p>`)
    .join('')
    // If no paragraphs were created, wrap the whole content in a single paragraph
    || `<p>${markdown.replace(/\n/g, '<br>')}</p>`
}

// Convert HTML back to markdown for editing
function convertHtmlToMarkdown(html: string): string {
  return html
    // Convert <strong> to **bold**
    .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
    // Convert <em> to *italic*
    .replace(/<em>(.*?)<\/em>/g, '*$1*')
    // Convert <br> tags to line breaks
    .replace(/<br\s*\/?>/g, '\n')
    // Convert paragraphs to double line breaks
    .replace(/<\/p><p>/g, '\n\n')
    // Remove paragraph tags
    .replace(/<\/?p>/g, '')
    // Clean up any remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Decode HTML entities
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
}

interface StreamlinedRecordingAreaProps {
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  doctorName?: string
  onConsultationUpdate: (consultation: Consultation) => void
  // State props
  patientName: string
  setPatientName: (name: string) => void
  selectedTemplate: string
  setSelectedTemplate: (template: string) => void
  isRecording: boolean
  isPaused: boolean
  recordingDuration: number
  audioBlob: Blob | null
  audioFile: File | null
  images: Array<{ id: string; file: File; preview?: string }>
  setImages: (images: Array<{ id: string; file: File; preview?: string }>) => void
  isGenerating: boolean
  setIsGenerating: (generating: boolean) => void
  summary: string
  setSummary: (summary: string) => void
  isEditing: boolean
  setIsEditing: (editing: boolean) => void
  additionalNotes: string
  setAdditionalNotes: (notes: string) => void
  // Function props
  startRecording: () => void
  pauseRecording: () => void
  stopRecording: () => void
  handleImageUpload: (files: FileList) => void
  removeImage: (id: string) => void
  clearAudio: () => void
}

const templates = [
  { id: 'outpatient', name: 'Outpatient Consultation', color: 'teal' },
  { id: 'discharge', name: 'Discharge Summary', color: 'purple' },
  { id: 'surgery', name: 'Operative Note', color: 'red' },
  { id: 'radiology', name: 'Radiology Report', color: 'blue' },
  { id: 'dermatology', name: 'Dermatology Note', color: 'green' },
  { id: 'cardiology_echo', name: 'Echocardiogram Report', color: 'pink' },
  { id: 'ivf_cycle', name: 'IVF Cycle Summary', color: 'orange' },
  { id: 'pathology', name: 'Histopathology Report', color: 'indigo' }
]

// Format duration in seconds to MM:SS format
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export function StreamlinedRecordingArea(props: StreamlinedRecordingAreaProps) {
  const {
    selectedConsultation, isDarkMode, patientName, setPatientName,
    selectedTemplate, setSelectedTemplate, isRecording, isPaused, recordingDuration,
    audioBlob, images, isGenerating, setIsGenerating, summary, setSummary,
    isEditing, setIsEditing, additionalNotes, setAdditionalNotes,
    startRecording, pauseRecording, stopRecording, handleImageUpload, removeImage,
    clearAudio, doctorName
  } = props

  const [isTemplateOpen, setIsTemplateOpen] = useState(false)
  const [isNotesOpen, setIsNotesOpen] = useState(false)
  const [audioPlaying, setAudioPlaying] = useState<string | null>(null)
  const [isLoadingAudio, setIsLoadingAudio] = useState<string | null>(null)

  // Web Audio API refs for Safari
  const audioContextRef = useRef<AudioContext | null>(null)
  const audioSourceRef = useRef<AudioBufferSourceNode | null>(null)
  const [copySuccess, setCopySuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isApproving, setIsApproving] = useState(false)
  const [streamingText, setStreamingText] = useState('')
  const [submitMessage, setSubmitMessage] = useState('')
  const [editedMarkdown, setEditedMarkdown] = useState('')
  const [isSavingEdit, setIsSavingEdit] = useState(false)
  const [messageTimeoutRef, setMessageTimeoutRef] = useState<NodeJS.Timeout | null>(null)

  // Autosave hook - OPTIMISTIC: Only show error states, no loading/success indicators
  const {
    autoSaveStatus,
    autoSaveImages,
    autoSaveAudio,
    autoSaveText,
    retryAutoSave,
    forceSave: _forceSave,
    hasPendingSaves: _hasPendingSaves
  } = useAutosave({
    onSuccess: (type) => {
      console.log(`Autosave successful: ${type}`)
      // OPTIMISTIC: No success messages - users see final state immediately
    },
    onError: (type, error) => {
      console.error(`Autosave failed: ${type}`, error)
      // Only show error states for retry functionality
    }
  })

  // Delete functionality state
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)
  const [showDeleteMenu, setShowDeleteMenu] = useState<{ type: 'audio' | 'image', url: string } | null>(null)

  // Additional upload states for generated consultations (unused but kept for future features)
  const [_additionalImages, _setAdditionalImages] = useState<Array<{ id: string, url: string, preview?: string }>>([])
  const [_isRecordingAdditional, _setIsRecordingAdditional] = useState(false)
  const [_additionalAudioBlob, _setAdditionalAudioBlob] = useState<Blob | null>(null)
  const [_mediaRecorder, _setMediaRecorder] = useState<MediaRecorder | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement }>({})
  const primaryAudioUrl = useMemo(() => selectedConsultation?.primary_audio_url, 
   [selectedConsultation?.primary_audio_url]) 
  const additionalAudioUrls = useMemo(() => selectedConsultation?.additional_audio_urls,
   [selectedConsultation?.additional_audio_urls]) 
  const prevPrimaryUrlRef = useRef<string | undefined>(undefined)
  const prevAdditionalUrlsRef = useRef<string[] | undefined>(undefined)

  // Autosave wrapper functions
  const handleAutoSaveImages = useCallback(async (imageFiles: File[]) => {
    if (!selectedConsultation) return

    const { addAdditionalImages } = await import('@/lib/actions/consultations')
    return addAdditionalImages(selectedConsultation.id, imageFiles)
  }, [selectedConsultation])

  const handleAutoSaveAudio = useCallback(async (audioBlob: Blob) => {
    if (!patientName.trim() || !selectedTemplate) {
      throw new Error('Patient name and template required')
    }

    if (selectedConsultation) {
      // Add additional audio to existing consultation
      // FIXED: Use unique timestamp to prevent overwriting in Cloudflare R2
      const audioFile = new File([audioBlob], `additional_audio_${Date.now()}.webm`, { type: 'audio/webm' })
      const { addAdditionalAudio } = await import('@/lib/actions/consultations')
      return addAdditionalAudio(selectedConsultation.id, audioFile)
    } else {
      // Create new consultation with seamless transition
      const audioFile = new File([audioBlob], 'audio.webm', { type: 'audio/webm' })
      const result = await createConsultationWithFiles(
        audioFile,
        images.map(img => img.file), // Include current images
        [],
        'doctor',
        additionalNotes || undefined,
        selectedTemplate as any,
        patientName || undefined
      )

      // SEAMLESS TRANSITION: If successful, trigger consultation update
      if (result && result.success && result.data) {
        // This will trigger the parent's onConsultationUpdate which clears local state
        props.onConsultationUpdate(result.data)
      }

      return result
    }
  }, [patientName, selectedTemplate, selectedConsultation, additionalNotes, images, props])

  const handleAutoSaveText = useCallback(async (field: string, value: string) => {
    if (!selectedConsultation) return

    if (field === 'notes') {
      const { updateAdditionalNotes } = await import('@/lib/actions/consultations')
      return updateAdditionalNotes(selectedConsultation.id, value)
    } else if (field === 'patient_name') {
      const { updatePatientName } = await import('@/lib/actions/consultations')
      return updatePatientName(selectedConsultation.id, value)
    }
  }, [selectedConsultation])

  // Helper function for seamless consultation refresh
  const refreshConsultationData = useCallback(async () => {
    if (!selectedConsultation) return

    try {
      const { getConsultations } = await import('@/lib/actions/consultations')
      const consultationsResult = await getConsultations()

      if (consultationsResult.success && consultationsResult.data) {
        const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)
        if (updatedConsultation && props.onConsultationUpdate) {
          props.onConsultationUpdate(updatedConsultation)
        }
      }
    } catch (error) {
      console.error('Failed to refresh consultation data:', error)
    }
  }, [selectedConsultation, props])

  // OPTIMISTIC IMAGE UPLOAD: Wait for final state, no local preview
  const handleImageUploadWithAutosave = useCallback(async (files: FileList) => {
    // Skip local state completely - wait for final state only
    if (selectedConsultation && files.length > 0) {
      const imageFiles = Array.from(files)

      try {
        // Upload and wait for final state - no local preview
        const result = await autoSaveImages(imageFiles, async (files: File[]) => {
          const uploadResult = await handleAutoSaveImages(files)

          // WAIT FOR FINAL STATE: Only refresh after successful upload
          if (uploadResult && typeof uploadResult === 'object' && 'success' in uploadResult && uploadResult.success) {
            // Refresh consultation data to show final URLs only
            await refreshConsultationData()
          }

          return uploadResult
        })

        console.log('Image autosave completed:', result)
      } catch (error) {
        console.error('Image autosave failed:', error)
        // On error, show local state as fallback
        handleImageUpload(files)
      }
    } else {
      // For new consultations without selectedConsultation, use local state as before
      handleImageUpload(files)
    }
  }, [selectedConsultation, autoSaveImages, handleAutoSaveImages, refreshConsultationData, handleImageUpload])

  // Clear messages when consultation changes
  useEffect(() => {
    setSubmitMessage('')
    setMessageTimeoutRef(prevTimeout => {
      if (prevTimeout) {
        clearTimeout(prevTimeout)
      }
      return null
    })
  }, [selectedConsultation?.id])

  // EVENT-DRIVEN AUTOSAVE: Called directly from user actions, not reactive useEffect

  // Track previous audioBlob to detect when recording just finished
  const prevAudioBlobRef = useRef<Blob | null>(null)

  // OPTIMISTIC AUDIO UPLOAD: Skip local blob state, show final state immediately
  useEffect(() => {
    const hadNoAudio = prevAudioBlobRef.current === null
    const nowHasAudio = audioBlob !== null

    if (hadNoAudio && nowHasAudio && patientName.trim() && selectedTemplate) {
      // Recording just finished, trigger autosave with optimistic transition
      autoSaveAudio(audioBlob, async (blob: Blob) => {
        const result = await handleAutoSaveAudio(blob)

        // OPTIMISTIC: Clear local state immediately and show final state
        if (result && typeof result === 'object' && 'success' in result && result.success) {
          // Clear local audio state immediately (optimistic)
          clearAudio()

          // Refresh consultation data to show final audio URL
          await refreshConsultationData()
        }

        return result
      })
    }

    prevAudioBlobRef.current = audioBlob
  }, [audioBlob, patientName, selectedTemplate, autoSaveAudio, handleAutoSaveAudio, clearAudio, refreshConsultationData])

  // Wrapper for text input with autosave
  const handleNotesChangeWithAutosave = useCallback((value: string) => {
    setAdditionalNotes(value)

    // Trigger autosave for text (only if we have an existing consultation and actual content)
    if (selectedConsultation && value.trim()) {
      autoSaveText('notes', value, handleAutoSaveText)
    }
  }, [setAdditionalNotes, selectedConsultation, autoSaveText, handleAutoSaveText])

  // Wrapper for patient name input with autosave
  const handlePatientNameChangeWithAutosave = useCallback((value: string) => {
    setPatientName(value)

    // Trigger autosave for patient name (only if we have an existing consultation and actual content)
    if (selectedConsultation && value.trim()) {
      autoSaveText('patient_name', value, handleAutoSaveText)
    }
  }, [setPatientName, selectedConsultation, autoSaveText, handleAutoSaveText])

  // Cleanup long press timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer)
      }
    }
  }, [longPressTimer])

  // Function to set message with auto-clear timeout
  const setSubmitMessageWithTimeout = (message: string, timeout = 3000) => {
    // Clear any existing timeout
    if (messageTimeoutRef) {
      clearTimeout(messageTimeoutRef)
    }

    setSubmitMessage(message)

    // Set new timeout to clear message
    if (message) {
      const timeoutId = setTimeout(() => {
        setSubmitMessage('')
        setMessageTimeoutRef(null)
      }, timeout)
      setMessageTimeoutRef(timeoutId)
    }
  }

  const selectedTemplateData = templates.find(t => t.id === selectedTemplate) || templates[0]
  const isApproved = selectedConsultation?.status === 'approved'
  const canEdit = !isApproved
  const hasContent = summary || selectedConsultation?.ai_generated_note || selectedConsultation?.edited_note

  // Strip all HTML and markdown formatting for clean copy
  const stripAllFormatting = (content: string): string => {
    // First convert HTML to plain text using DOM
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = content
    const plainText = tempDiv.textContent || tempDiv.innerText || ''

    // Then strip any remaining markdown syntax
    return plainText
      .replace(/\*\*(.*?)\*\*/g, '$1')  // **bold** → bold
      .replace(/\*(.*?)\*/g, '$1')      // *italic* → italic
      .replace(/#{1,6}\s/g, '')         // # headers → text
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // [link](url) → link
      .replace(/`([^`]+)`/g, '$1')      // `code` → code
      .replace(/^\s*[-*+]\s/gm, '')     // - list items → text
      .replace(/^\s*\d+\.\s/gm, '')     // 1. numbered lists → text
      .replace(/\n{3,}/g, '\n\n')       // Multiple newlines → double newline
      .trim()
  }

  const handleCopy = async () => {
    try {
      const plainText = stripAllFormatting(summary)
      await navigator.clipboard.writeText(plainText)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  // Handle template change with immediate database sync
  const handleTemplateChange = async (newTemplate: string) => {
    setSelectedTemplate(newTemplate)

    // If we have a selected consultation, update it in the database immediately
    if (selectedConsultation) {
      try {
        const result = await updateConsultationType(
          selectedConsultation.id,
          newTemplate as 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'
        )

        if (result.success) {
          // Update the consultation object to reflect the change
          const updatedConsultation = {
            ...selectedConsultation,
            consultation_type: newTemplate
          }
          props.onConsultationUpdate(updatedConsultation)
        } else {
          console.error('Failed to update consultation type:', result.error)
          setSubmitMessageWithTimeout(`Failed to update template: ${result.error}`)
        }
      } catch (error) {
        console.error('Error updating consultation type:', error)
        setSubmitMessageWithTimeout('Failed to update template')
      }
    }
  }

  // Handle editing - convert HTML to markdown for editing
  const handleStartEdit = () => {
    const currentContent = summary || selectedConsultation?.edited_note || selectedConsultation?.ai_generated_note || ''
    // If content is HTML (from rich text editor), convert to markdown
    const markdownContent = currentContent.includes('<') ? convertHtmlToMarkdown(currentContent) : currentContent
    setEditedMarkdown(markdownContent)
    setIsEditing(true)
  }

  // Handle saving edited content
  const handleSaveEdit = async () => {
    if (!selectedConsultation || !editedMarkdown.trim()) {
      setSubmitMessage('No content to save')
      return
    }

    setIsSavingEdit(true)
    setSubmitMessage('')

    try {
      const result = await saveEditedNote(selectedConsultation.id, editedMarkdown)

      if (result.success) {
        setSubmitMessageWithTimeout('Changes saved successfully!')
        // Update the summary display with the edited content (convert to HTML for display)
        setSummary(convertMarkdownToHtml(editedMarkdown))
        setIsEditing(false)

        // Update the consultation object to reflect the change
        const updatedConsultation = {
          ...selectedConsultation,
          edited_note: editedMarkdown
        }
        props.onConsultationUpdate(updatedConsultation)
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to save changes')
      }
    } catch (error) {
      console.error('Error saving edited note:', error)
      setSubmitMessageWithTimeout('Failed to save changes')
    } finally {
      setIsSavingEdit(false)
    }
  }

  // Handle cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditedMarkdown('')
  }

  // Delete handlers
  const handleDeleteAudio = async (audioUrl: string) => {
    if (!selectedConsultation) return

    try {
      setSubmitMessage('Deleting audio...')
      const result = await deleteAdditionalAudio(selectedConsultation.id, audioUrl)

      if (result.success) {
        setSubmitMessageWithTimeout('Audio deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)
          if (updatedConsultation) {
            props.onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to delete audio')
      }
    } catch (_error) {
      setSubmitMessageWithTimeout('Failed to delete audio')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  const handleDeleteImage = async (imageUrl: string) => {
    if (!selectedConsultation) return

    try {
      setSubmitMessage('Deleting image...')
      const result = await deleteConsultationImage(selectedConsultation.id, imageUrl)

      if (result.success) {
        setSubmitMessageWithTimeout('Image deleted successfully!')

        // Refresh consultation data
        const { getConsultations } = await import('@/lib/actions/consultations')
        const consultationsResult = await getConsultations()

        if (consultationsResult.success && consultationsResult.data) {
          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)
          if (updatedConsultation) {
            props.onConsultationUpdate(updatedConsultation)
          }
        }
      } else {
        setSubmitMessageWithTimeout(result.error || 'Failed to delete image')
      }
    } catch (_error) {
      setSubmitMessageWithTimeout('Failed to delete image')
    } finally {
      setShowDeleteMenu(null)
    }
  }

  // Long press handlers for mobile
  const handleLongPressStart = (type: 'audio' | 'image', url: string) => {
    const timer = setTimeout(() => {
      setShowDeleteMenu({ type, url })
    }, 500) // 500ms long press
    setLongPressTimer(timer)
  }

  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      setLongPressTimer(null)
    }
  }

  const handleSubmitConsultation = async () => {
    // Check if we're working with an existing consultation or creating a new one
    const isExistingConsultation = !!selectedConsultation



    // Validation logic differs for existing vs new consultations
    if (isExistingConsultation) {
      // For existing consultations: we need either new audio, new images, or additional notes
      if (!audioBlob && images.length === 0 && !additionalNotes.trim()) {
        setSubmitMessage('Please add new audio, images, or additional notes to update the consultation')
        return
      }
    } else {
      // For new consultations: we need both audio and patient name
      if (!audioBlob || !patientName.trim()) {
        setSubmitMessage('Please provide patient name and record audio')
        return
      }
    }

    setIsSubmitting(true)
    setSubmitMessage('')

    try {
      if (isExistingConsultation) {
        // Handle new images
        if (images.length > 0) {
          const imageFiles = images.map(img => img.file)

          const imageResult = await addAdditionalImages(selectedConsultation.id, imageFiles)
          if (!imageResult.success) {
            setSubmitMessage(imageResult.error || 'Failed to add additional images')
            return
          }

        }

        // Handle new audio
        if (audioBlob) {
          const audioFile = new File([audioBlob], `additional_audio_${Date.now()}.webm`, {
            type: 'audio/webm'
          })

          const audioResult = await addAdditionalAudio(selectedConsultation.id, audioFile)
          if (!audioResult.success) {
            setSubmitMessage(audioResult.error || 'Failed to add additional audio')
            return
          }

        }

        // Handle additional notes update
        if (additionalNotes.trim() !== (selectedConsultation.additional_notes || '').trim()) {
          const { updateAdditionalNotes } = await import('@/lib/actions/consultations')
          const notesResult = await updateAdditionalNotes(selectedConsultation.id, additionalNotes.trim())
          if (!notesResult.success) {
            setSubmitMessage(notesResult.error || 'Failed to update additional notes')
            return
          }
        }

        setSubmitMessageWithTimeout('Consultation updated successfully!')

        // FIX: Refetch the consultation from database to get the latest state
        try {
          const { getConsultations } = await import('@/lib/actions/consultations')
          const consultationsResult = await getConsultations()

          if (consultationsResult.success && consultationsResult.data) {
            // Find the updated consultation in the fresh data
            const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === selectedConsultation.id)

            if (updatedConsultation) {
              // Call onConsultationUpdate with the fresh data from database
              props.onConsultationUpdate(updatedConsultation)
              return { success: true, data: updatedConsultation }
            }
          }

          // Fallback if we can't find the updated consultation
          props.onConsultationUpdate(selectedConsultation)
          return { success: true, data: selectedConsultation }

        } catch (fetchError) {
          console.error('Failed to refetch consultations:', fetchError)
          // Fallback to original consultation if fetch fails
          props.onConsultationUpdate(selectedConsultation)
          return { success: true, data: selectedConsultation }
        }

      } else {
        // Convert blob to File
        if (!audioBlob) {
          setSubmitMessage('No audio recording available')
          return
        }
        const audioFile = new File([audioBlob], `recording_${Date.now()}.webm`, {
          type: 'audio/webm'
        })

        // Extract File objects from images
        const imageFiles = images.map(img => img.file)

        const result = await createConsultationWithFiles(
          audioFile,
          imageFiles,
          [], // No additional audio files
          'doctor',
          additionalNotes || undefined,
          selectedTemplate as "outpatient" | "discharge" | "surgery" | "radiology" | "dermatology" | "cardiology_echo" | "ivf_cycle" | "pathology",
          patientName || undefined
        )

        if (result.success && result.data) {
          setSubmitMessageWithTimeout(`Consultation submitted! ${result.data.patient_name}`)
          props.onConsultationUpdate(result.data)
          return result
        } else {
          setSubmitMessage('error' in result ? result.error : 'Failed to submit consultation')
          return result
        }
      }
    } catch (error) {
      console.error('❌ Save error:', error)
      setSubmitMessage(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`)
      return { success: false, error: 'Network error' }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGenerate = async (consultationToUse?: Consultation) => {
    // For existing consultations, use selectedConsultation directly (same as update function)
    // For new consultations, use the provided consultation from save result
    if (selectedConsultation) {
      // Existing consultation - use selectedConsultation directly like update function
      console.log('🎯 Using existing consultation:', selectedConsultation.id)
    } else if (consultationToUse) {
      // New consultation - use the provided consultation
      console.log('🎯 Using new consultation:', consultationToUse.id)
    } else if (audioBlob) {
      // No consultation but have audio - save first then generate
      const result = await handleSubmitConsultation()
      if (result && result.success && result.data) {
        return handleGenerate(result.data)
      }
      return
    } else {
      setSubmitMessage('Please record audio or select a consultation first')
      return
    }

    // Use the same pattern as update function - selectedConsultation for existing, consultationToUse for new
    const consultationId = selectedConsultation?.id || consultationToUse?.id
    // FIX: Use selectedTemplate from UI state instead of stored consultation_type
    const consultationType = selectedTemplate // This ensures the current UI selection is used
    const doctorNotes = selectedConsultation?.doctor_notes || consultationToUse?.doctor_notes

    if (!consultationId) {
      setSubmitMessage('No consultation ID available')
      return
    }

    setIsGenerating(true)
    setStreamingText('')
    setSummary('')

    // Clear edited note when regenerating (for existing consultations)
    if (selectedConsultation) {
      try {
        await clearEditedNote(selectedConsultation.id)
      } catch (error) {
        console.error('Failed to clear edited note:', error)
        // Continue with generation even if clearing fails
      }
    }

    try {
      console.log('🎯 Starting generation with consultation ID:', consultationId)
      console.log('🎯 Using template type:', consultationType)

      // Get the consultation data to extract file URLs (same as dashboard approach)
      const currentConsultation = selectedConsultation || consultationToUse
      if (!currentConsultation) {
        setSubmitMessage('No consultation data available')
        setIsGenerating(false)
        return
      }

      // Use Next.js API route with quota checking (this supports streaming properly)
      const response = await fetch('/api/generate-summary-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primary_audio_url: currentConsultation.primary_audio_url,
          additional_audio_urls: Array.isArray(currentConsultation.additional_audio_urls) ? currentConsultation.additional_audio_urls : [],
          image_urls: Array.isArray(currentConsultation.image_urls) ? currentConsultation.image_urls : [],
          submitted_by: currentConsultation.submitted_by || 'doctor',
          consultation_type: consultationType || 'outpatient',
          doctor_notes: doctorNotes || undefined,
          additional_notes: additionalNotes || undefined,
          patient_name: currentConsultation.patient_name || undefined,
          doctor_name: doctorName || undefined,
          created_at: currentConsultation.created_at || undefined,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No reader available')
      }

      const decoder = new TextDecoder()
      let fullSummary = ''
      setStreamingText('')

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'chunk' && data.text) {
                // Format text with proper line breaks
                const formattedText = data.text.replace(/\\n/g, '\n').replace(/\n\n+/g, '\n\n')
                fullSummary += formattedText
                // Instantly update streaming text with accumulated content
                setStreamingText(fullSummary)
              }
            } catch (_e) {
              // Ignore parse errors
            }
          }
        }
      }

      // Convert markdown to HTML for display
      const htmlSummary = convertMarkdownToHtml(fullSummary)
      setSummary(htmlSummary)
      setStreamingText('')
      setIsGenerating(false)
      setSubmitMessageWithTimeout('Summary generated successfully!')

      // FIX: Clear editing state since we have new AI-generated content
      setIsEditing(false)
      setEditedMarkdown('')

      // Save the streamed summary using the proper server action
      try {
        const { saveStreamingSummary } = await import('@/lib/actions/consultations')
        const saveResult = await saveStreamingSummary(consultationId, fullSummary)

        if (saveResult.success) {
          // Track successful consultation generation (PII-free)
          trackConsultation('generated', consultationType)

          // FIX: Update consultation with new AI content and cleared edited_note
          if (props.onConsultationUpdate) {
            props.onConsultationUpdate({
              ...currentConsultation,
              ai_generated_note: fullSummary,
              edited_note: null, // Important: clear this to reflect database state
              status: 'generated'
            })
          }
        } else {
          console.error('Failed to save streaming summary:', saveResult.error)
          setSubmitMessage(`Summary generated but failed to save: ${saveResult.error}`)
        }
      } catch (saveError) {
        console.error('Error saving streaming summary:', saveError)
        setSubmitMessage('Summary generated but failed to save. Please try regenerate.')
      }

    } catch (error) {
      console.error('❌ Generate error:', error)
      setSubmitMessage(`Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setIsGenerating(false)
    }
  }

  const handleApprove = async () => {
    if (!selectedConsultation || !summary.trim()) {
      setSubmitMessage('Please generate a summary before approving')
      return
    }

    setIsApproving(true)
    setSubmitMessage('')

    try {
      const result = await approveConsultation(selectedConsultation.id, summary)

      if (result.success) {
        setSubmitMessageWithTimeout('Consultation approved successfully!')

        // Track successful consultation approval (PII-free)
        trackConsultation('approved', selectedConsultation.consultation_type)

        // Update consultation status
        if (props.onConsultationUpdate) {
          props.onConsultationUpdate({
            ...selectedConsultation,
            status: 'approved',
            edited_note: summary
          })
        }
      } else {
        setSubmitMessage(result.error || 'Failed to approve consultation')
      }
    } catch (_error) {
      setSubmitMessage('Failed to approve consultation')
    } finally {
      setIsApproving(false)
    }
  }

  // Web Audio API playback for Safari
  const handleWebAudioPlayback = async (audioId: string, audioUrl: string) => {
    // Stop logic - if same audio is playing, stop it
    if (audioPlaying === audioId) {
      if (audioSourceRef.current) {
        audioSourceRef.current.stop()
        audioSourceRef.current = null
      }
      setAudioPlaying(null)
      return
    }

    // Stop any currently playing audio
    if (audioSourceRef.current) {
      audioSourceRef.current.stop()
    }

    setIsLoadingAudio(audioId)
    setAudioPlaying(null)

    try {
      // Create or resume AudioContext (must be in user gesture)
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume()
      }

      const audioContext = audioContextRef.current

      // Fetch audio through proxy
      const proxyUrl = `/api/audio-proxy?url=${encodeURIComponent(audioUrl)}`
      const response = await fetch(proxyUrl)
      if (!response.ok) {
        throw new Error(`Audio fetch failed: ${response.statusText}`)
      }
      const audioData = await response.arrayBuffer()

      // Decode audio data (handles format conversion)
      const audioBuffer = await audioContext.decodeAudioData(audioData)

      // Create source and play
      const source = audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(audioContext.destination)
      source.start(0)

      // Handle playback end
      source.onended = () => {
        setAudioPlaying(null)
        audioSourceRef.current = null
      }

      audioSourceRef.current = source
      setAudioPlaying(audioId)

    } catch (error) {
      console.error('Web Audio API Error:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      setSubmitMessage(`Unable to play audio: ${errorMessage}`)
      setAudioPlaying(null)
    } finally {
      setIsLoadingAudio(null)
    }
  }

  const toggleAudioPlayback = async (audioId: string) => {
    const audio = audioRefs.current[audioId]
    if (!audio) return

    // Detect Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)

    // For Safari, use Web Audio API
    if (isSafari && audio.src && !audio.src.startsWith('blob:')) {
      await handleWebAudioPlayback(audioId, audio.src)
      return
    }

    // For other browsers, use existing <audio> element logic
    if (audioPlaying === audioId) {
      audio.pause()
      setAudioPlaying(null)
    } else {
      // Pause other audios
      Object.values(audioRefs.current).forEach(a => a.pause())

      try {
        await audio.play()
        setAudioPlaying(audioId)
      } catch (error) {
        console.error('Audio playback error:', error)
        setAudioPlaying(null)
        const errorMessage = error instanceof Error ? error.message : String(error)
        setSubmitMessage(`Unable to play audio: ${errorMessage}`)
      }
    }
  }

  return (
    <div className="space-y-8 max-w-5xl">
      {/* Mobile: Patient Name, Template, Approve Button */}
      <div className="flex flex-col sm:hidden gap-4 pl-3">
        {/* Patient Name with Add Button */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <label className={`text-lg font-semibold ${
              isDarkMode ? 'text-gray-200' : 'text-slate-800'
            }`}>
              Patient Name
            </label>
            {/* Add Consultation Button - only show when viewing existing consultation, positioned on the right */}
            {selectedConsultation && (
              <button
                onClick={() => props.onConsultationUpdate(null as any)}
                className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 mr-8 ${
                  isDarkMode
                    ? 'bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg'
                    : 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'
                }`}
                title="Add New Consultation"
              >
                <Plus className="w-4 h-4" />
              </button>
            )}
          </div>
          <Input
            value={patientName}
            onChange={(e) => selectedConsultation ? handlePatientNameChangeWithAutosave(e.target.value) : setPatientName(e.target.value)}
            placeholder="Enter patient name"
            className={`h-10 px-4 py-2 rounded-xl border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500'
                : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          />
        </div>

        {/* Template and Approve Button on same row */}
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label className={`block text-lg font-semibold mb-4 ${
              isDarkMode ? 'text-gray-200' : 'text-slate-800'
            }`}>
              Template
            </label>
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => canEdit && setIsTemplateOpen(!isTemplateOpen)}
                disabled={!canEdit}
                className={`w-full justify-between h-10 px-4 py-2 rounded-xl border transition-colors ${
                  isDarkMode
                    ? 'bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-indigo-500'
                    : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 hover:bg-white/90 focus:border-indigo-500'
                } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
              >
                <span>{selectedTemplateData.name}</span>
                {canEdit && <ChevronDown className="h-4 w-4" />}
              </Button>

              <AnimatePresence>
                {isTemplateOpen && canEdit && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className={`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${
                      isDarkMode
                        ? 'bg-gray-950 border-gray-700'
                        : 'bg-white/90 backdrop-blur-xl border-white/30'
                    }`}
                  >
                    {templates.map((template) => (
                      <button
                        key={template.id}
                        onClick={() => {
                          handleTemplateChange(template.id)
                          setIsTemplateOpen(false)
                        }}
                        className={`w-full px-3 py-2 text-left transition-colors ${
                          selectedTemplate === template.id
                            ? isDarkMode
                              ? 'bg-indigo-900/30 text-indigo-400'
                              : 'bg-indigo-50 text-indigo-700'
                            : isDarkMode
                              ? 'hover:bg-gray-800 text-gray-200'
                              : 'hover:bg-indigo-50 text-slate-800'
                        }`}
                      >
                        {template.name}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Mobile Approve Button - Same level as template */}
          {hasContent && selectedConsultation && !isApproved && (
            <Button
              onClick={handleApprove}
              disabled={isApproving || !summary.trim()}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
            >
              {isApproving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Approve
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Desktop: Patient Name, Template, Approve Button */}
      <div className="hidden sm:block pl-3">
        <div className="flex gap-6 items-end">
        {/* Patient Name - 70% size */}
        <div className="w-[25%]">
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-slate-800'
          }`}>
            Patient Name
          </label>
          <Input
            value={patientName}
            onChange={(e) => selectedConsultation ? handlePatientNameChangeWithAutosave(e.target.value) : setPatientName(e.target.value)}
            placeholder="Enter patient name"
            className={`h-10 px-4 py-2 rounded-xl border transition-colors ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500'
                : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500'
            } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
          />
        </div>

        {/* Template - 70% size */}
        <div className="w-[25%]">
          <label className={`block text-lg font-semibold mb-4 ${
            isDarkMode ? 'text-gray-200' : 'text-slate-800'
          }`}>
            Template
          </label>
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => canEdit && setIsTemplateOpen(!isTemplateOpen)}
              disabled={!canEdit}
              className={`w-full justify-between h-10 px-4 py-2 rounded-xl border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-indigo-500'
                  : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 hover:bg-white/90 focus:border-indigo-500'
              } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
            >
              <span>{selectedTemplateData.name}</span>
              {canEdit && <ChevronDown className="h-4 w-4" />}
            </Button>

            <AnimatePresence>
              {isTemplateOpen && canEdit && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${
                    isDarkMode
                      ? 'bg-gray-950 border-gray-700'
                      : 'bg-white/90 backdrop-blur-xl border-white/30'
                  }`}
                >
                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() => {
                        handleTemplateChange(template.id)
                        setIsTemplateOpen(false)
                      }}
                      className={`w-full px-3 py-2 text-left transition-colors ${
                        selectedTemplate === template.id
                          ? isDarkMode
                            ? 'bg-indigo-900/30 text-indigo-400'
                            : 'bg-indigo-50 text-indigo-700'
                          : isDarkMode
                            ? 'hover:bg-gray-800 text-gray-200'
                            : 'hover:bg-indigo-50 text-slate-800'
                      }`}
                    >
                      {template.name}
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Action Buttons - Moved left with proper spacing */}
        <div className="flex items-end space-x-3 w-[50%]">
          {hasContent && selectedConsultation && !isApproved && (
            <Button
              onClick={handleApprove}
              disabled={isApproving || !summary.trim()}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
            >
              {isApproving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Approve
                </>
              )}
            </Button>
          )}



          {/* Desktop Generate/Regenerate Button */}
          <AnimatePresence>
            {(selectedConsultation || (audioBlob && autoSaveStatus === 'saved')) && !isApproved && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: -20 }}
                transition={{ type: "spring", damping: 20, stiffness: 300 }}
              >
                <Button
                  onClick={() => handleGenerate()}
                  disabled={isGenerating}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      {(selectedConsultation && (selectedConsultation.ai_generated_note || selectedConsultation.edited_note)) ? 'Regenerate' : 'Generate'}
                    </>
                  )}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        </div>
      </div>

      {/* Recording Controls */}
      <div className="pl-3">
        {/* Mobile: Recording controls with Save button positioned to the right */}
        <div className="flex sm:hidden items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Recording Controls */}
            {!isRecording ? (
              <Button
                onClick={startRecording}
                disabled={isApproved}
                size="sm"
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0"
              >
                <Mic className="w-4 h-4" />
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={pauseRecording}
                  size="sm"
                  variant="outline"
                  className="border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0"
                >
                  {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                </Button>
                <Button
                  onClick={stopRecording}
                  size="sm"
                  variant="outline"
                  className="border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0"
                >
                  <Square className="w-4 h-4" />
                </Button>
                {/* Mobile Recording Timer */}
                <div className="flex items-center gap-1 ml-2">
                  <Badge variant="secondary" className="font-mono text-sm px-2 py-1">
                    {formatDuration(recordingDuration)}
                  </Badge>
                  {isPaused && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-500 text-xs">
                      Paused
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Upload Button */}
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Chrome-specific fix: Use setTimeout to ensure click happens after event handling
                setTimeout(() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }, 0);
              }}
              disabled={isApproved}
              variant="outline"
              size="sm"
              className={`w-10 h-10 p-0 rounded-full transition-all duration-300 transform hover:scale-110 ${
                isDarkMode
                  ? 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                  : 'bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300'
              }`}
              title="Upload Images"
            >
              <Upload className="w-4 h-4" />
            </Button>
          </div>


        </div>

        {/* Desktop: Multi-column layout */}
        <div className="hidden sm:block">
          <div className="flex gap-4 items-center">
          {/* Left side - Recording Controls + Upload Button */}
          <div className="flex items-center gap-3 flex-1 max-w-xs">
            {/* Recording Controls */}
            {!isRecording ? (
              <Button
                onClick={startRecording}
                disabled={isApproved}
                size="sm"
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0"
              >
                <Mic className="w-4 h-4" />
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={pauseRecording}
                  size="sm"
                  variant="outline"
                  className="border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0"
                >
                  {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                </Button>
                <Button
                  onClick={stopRecording}
                  size="sm"
                  variant="outline"
                  className="border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0"
                >
                  <Square className="w-4 h-4" />
                </Button>
                <div className="flex items-center gap-2 ml-3">
                  <Badge variant="secondary" className="font-mono text-lg px-3 py-1">
                    {formatDuration(recordingDuration)}
                  </Badge>
                  {isPaused && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-500">
                      Paused
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Upload Button */}
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Chrome-specific fix: Use setTimeout to ensure click happens after event handling
                setTimeout(() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }, 0);
              }}
              disabled={isApproved}
              variant="outline"
              size="sm"
              className={`w-10 h-10 p-0 rounded-full transition-all duration-300 transform hover:scale-110 ${
                isDarkMode
                  ? 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                  : 'bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300'
              }`}
              title="Upload Images"
            >
              <Upload className="w-4 h-4" />
            </Button>

            {/* Desktop: Saving animation next to buttons */}
            <AutoSaveIndicator
              status={autoSaveStatus}
              onRetry={() => retryAutoSave(async () => {
                if (audioBlob && patientName.trim() && selectedTemplate) {
                  return handleAutoSaveAudio(audioBlob)
                }
                throw new Error('Nothing to save')
              })}
              showText={false}
              className="ml-2"
            />
          </div>




          </div>
        </div>
      </div>



      {/* Status Message */}
        {submitMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`mt-4 p-3 rounded-md ${
              isDarkMode
                ? 'bg-blue-950/50 border border-blue-800'
                : 'bg-blue-50 border border-blue-200'
            }`}
          >
            <p className={`text-sm ${
              isDarkMode ? 'text-blue-200' : 'text-blue-800'
            }`}>{submitMessage}</p>
          </motion.div>
        )}

      {/* Audio Recordings */}
      {(audioBlob || selectedConsultation?.primary_audio_url || (selectedConsultation?.additional_audio_urls && Array.isArray(selectedConsultation.additional_audio_urls) && selectedConsultation.additional_audio_urls.filter((url): url is string => typeof url === 'string' && url.trim() !== '').length > 0)) && (
        <div className="flex flex-wrap gap-3 pl-0">
          {audioBlob && (
            <div className={`flex items-center gap-2 rounded-lg px-3 py-2 ${
              isDarkMode ? 'bg-black' : 'bg-transparent'
            }`}>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleAudioPlayback('new')}
                className="!h-10 !w-10 p-0 rounded-full bg-blue-600 hover:bg-blue-700 text-white"
                style={{ height: '40px', width: '40px' }}
              >
                {audioPlaying === 'new' ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  setAudioPlaying(null)
                  clearAudio()
                }}
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
              <audio
                ref={(el) => { if (el) audioRefs.current['new'] = el }}
                src={URL.createObjectURL(audioBlob)}
                onEnded={() => setAudioPlaying(null)}
              />
            </div>
          )}

          {selectedConsultation?.primary_audio_url && (
            <div className={`flex items-center gap-2 rounded-lg px-3 py-2 ${
              isDarkMode ? 'bg-black' : 'bg-transparent'
            }`}>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toggleAudioPlayback('primary')}
                disabled={isLoadingAudio === 'primary'}
                className="!h-10 !w-10 p-0 rounded-full bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50"
                style={{ height: '40px', width: '40px' }}
              >
                {isLoadingAudio === 'primary' ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : audioPlaying === 'primary' ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <audio
                key={`primary-audio-${selectedConsultation.primary_audio_url}`}
             ref={(el) => {   
                if (el) {  
                    audioRefs.current['primary'] = el 
                    if (primaryAudioUrl !== prevPrimaryUrlRef.current) { 
                        el.load()
                        prevPrimaryUrlRef.current = primaryAudioUrl || undefined 
                    }  
                }  
            }}
                src={selectedConsultation.primary_audio_url}
                onEnded={() => setAudioPlaying(null)}
                onLoadedData={() => {
                  // Audio is ready to play
                  console.log('Primary audio loaded:', selectedConsultation.primary_audio_url)
                }}
              />
            </div>
          )}

          {/* Additional Audio Files */}
          {selectedConsultation?.additional_audio_urls &&
           Array.isArray(selectedConsultation.additional_audio_urls) &&
           selectedConsultation.additional_audio_urls
             .filter((url): url is string => typeof url === 'string' && url.trim() !== '')
             .map((url, index) => (
               <div
                 key={`additional-${index}`}
                 className={`flex items-center gap-2 rounded-lg px-3 py-2 relative group ${
                   isDarkMode ? 'bg-black' : 'bg-transparent'
                 }`}
                 onMouseEnter={() => selectedConsultation?.status !== 'approved' && setShowDeleteMenu({ type: 'audio', url })}
                 onMouseLeave={() => setShowDeleteMenu(null)}
                 onTouchStart={() => selectedConsultation?.status !== 'approved' && handleLongPressStart('audio', url)}
                 onTouchEnd={handleLongPressEnd}
                 onTouchCancel={handleLongPressEnd}
               >
                 <Button
                   size="sm"
                   variant="ghost"
                   onClick={() => toggleAudioPlayback(`additional-${index}`)}
                   disabled={isLoadingAudio === `additional-${index}`}
                   className="!h-10 !w-10 p-0 rounded-full bg-teal-600 hover:bg-teal-700 text-white disabled:opacity-50"
                   style={{ height: '40px', width: '40px' }}
                 >
                   {isLoadingAudio === `additional-${index}` ? (
                     <Loader2 className="w-4 h-4 animate-spin" />
                   ) : audioPlaying === `additional-${index}` ? (
                     <Pause className="w-4 h-4" />
                   ) : (
                     <Play className="w-4 h-4" />
                   )}
                 </Button>

                 {/* Delete button - Desktop hover / Mobile long press */}
                 {selectedConsultation?.status !== 'approved' && showDeleteMenu?.type === 'audio' && showDeleteMenu?.url === url && (
                   <Button
                     size="sm"
                     onClick={() => handleDeleteAudio(url)}
                     className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200"
                     title="Delete audio"
                   >
                     <Trash2 className="w-3 h-3" />
                   </Button>
                 )}

                 <audio
                   key={`additional-audio-${url}`}
                   ref={(el) => {  
                    if (el) {    
                        audioRefs.current[`additional-${index}`] = el
                        const urlsChanged = JSON.stringify(additionalAudioUrls as string[]) !==  
                         JSON.stringify(prevAdditionalUrlsRef.current)  
                         if (urlsChanged) {  
                            el.load()  
                            prevAdditionalUrlsRef.current = additionalAudioUrls as string[]    
                        } 
} 
 }}  
                   src={url}
                   onEnded={() => setAudioPlaying(null)}
                   onLoadedData={() => {
                     // Audio is ready to play
                     console.log(`Additional audio ${index} loaded:`, url)
                   }}
                 />
               </div>
             ))}
        </div>
      )}

      {/* Images Preview - OPTIMISTIC: Only show final state (saved images) */}
      {(selectedConsultation?.image_urls && Array.isArray(selectedConsultation.image_urls) && selectedConsultation.image_urls.length > 0) && (
        <div className={`p-4 rounded-lg transition-colors ${
          isDarkMode
            ? 'bg-black'
            : 'bg-transparent'
        }`}>
          <div className="flex flex-wrap gap-3">
            {/* OPTIMISTIC: Removed local images display - only show final state below */}

            {Array.isArray(selectedConsultation?.image_urls) &&
             selectedConsultation.image_urls
               .filter((url): url is string => typeof url === 'string' && url.trim() !== '')
               .map((url, index) => (
              <div
                key={`existing-${index}`}
                className={`w-20 h-20 rounded-lg overflow-hidden border-2 relative group ${
                  isDarkMode
                    ? 'bg-black border-gray-700'
                    : 'bg-white/70 backdrop-blur-sm border-white/30'
                }`}
                onMouseEnter={() => selectedConsultation?.status !== 'approved' && setShowDeleteMenu({ type: 'image', url })}
                onMouseLeave={() => setShowDeleteMenu(null)}
                onTouchStart={() => selectedConsultation?.status !== 'approved' && handleLongPressStart('image', url)}
                onTouchEnd={handleLongPressEnd}
                onTouchCancel={handleLongPressEnd}
              >
                <Image
                  src={url}
                  alt={`Image ${index + 1}`}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />

                {/* Delete button - Desktop hover / Mobile long press */}
                {selectedConsultation?.status !== 'approved' && showDeleteMenu?.type === 'image' && showDeleteMenu?.url === url && (
                  <Button
                    size="sm"
                    onClick={() => handleDeleteImage(url)}
                    className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200"
                    title="Delete image"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional Notes Dropdown with Mobile Regenerate Button */}
      <div className="w-full">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => setIsNotesOpen(!isNotesOpen)}
            className={`flex items-center gap-2 transition-colors ${
              isDarkMode
                ? 'text-gray-300 hover:text-gray-200'
                : 'text-slate-600 hover:text-slate-700'
            }`}
          >
            <span>Additional Notes</span>
            {isNotesOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>

          {/* Mobile Regenerate Button - Only show when consultation exists or autosave complete */}
          <div className="sm:hidden">
            {(selectedConsultation || (audioBlob && autoSaveStatus === 'saved')) && !isApproved && (
              <Button
                onClick={() => handleGenerate()}
                disabled={isGenerating}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 mr-2" />
                    {(selectedConsultation && (selectedConsultation.ai_generated_note || selectedConsultation.edited_note)) ? 'Regenerate' : 'Generate'}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        <AnimatePresence>
          {isNotesOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3"
            >
              <div className={`p-4 pr-4 sm:pr-20 rounded-lg transition-colors ${
                isDarkMode
                  ? 'bg-black'
                  : 'bg-transparent'
              }`}>
                <textarea
                  value={additionalNotes}
                  onChange={(e) => handleNotesChangeWithAutosave(e.target.value)}
                  placeholder="Add any additional notes or observations..."
                  rows={6}
                  className={`w-full px-3 py-2 border rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors ${
                    isDarkMode
                      ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400'
                      : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400'
                  }`}
                />

                {/* Mobile: Cute saving animation in same location */}
                <div className="mt-2 flex justify-end">
                  <AutoSaveIndicator
                    status={autoSaveStatus}
                    onRetry={() => retryAutoSave(async () => {
                      if (audioBlob && patientName.trim() && selectedTemplate) {
                        return handleAutoSaveAudio(audioBlob)
                      }
                      throw new Error('Nothing to save')
                    })}
                    showText={false}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Summary Section - Mobile friendly with extended width */}
      {(summary || streamingText || isGenerating || hasContent) && (
        <div className={`py-6 pr-4 sm:pr-20 pl-2 rounded-lg transition-colors ${
          isDarkMode
            ? 'bg-black'
            : 'bg-transparent'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${
              isDarkMode ? 'text-gray-100' : 'text-slate-800'
            }`}>Consultation Summary</h3>
            <div className="flex items-center gap-2">
              {(summary || hasContent) && (
                <>
                  {!isEditing ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleStartEdit}
                      className={`transition-colors ${
                        isDarkMode
                          ? 'text-gray-300 hover:text-gray-200'
                          : 'text-slate-600 hover:text-slate-700'
                      }`}
                    >
                      <Edit3 className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSaveEdit}
                        disabled={isSavingEdit}
                        className={`transition-colors ${
                          isDarkMode
                            ? 'text-green-300 hover:text-green-200'
                            : 'text-green-600 hover:text-green-700'
                        }`}
                      >
                        {isSavingEdit ? (
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                        ) : (
                          <Save className="w-4 h-4 mr-1" />
                        )}
                        Save
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCancelEdit}
                        className={`transition-colors ${
                          isDarkMode
                            ? 'text-gray-300 hover:text-gray-200'
                            : 'text-slate-600 hover:text-slate-700'
                        }`}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopy}
                    className={`transition-colors ${
                      isDarkMode
                        ? 'text-gray-300 hover:text-gray-200'
                        : 'text-slate-600 hover:text-slate-700'
                    }`}
                  >
                    {copySuccess ? (
                      <>
                        <Check className="w-4 h-4 mr-1 text-green-500" />
                        Copied
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-1" />
                        Copy
                      </>
                    )}
                  </Button>
                </>
              )}
            </div>
          </div>

          {isGenerating && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-3" />
                <p className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-slate-600'
                }`}>Generating AI summary...</p>
              </div>
            </div>
          )}

          {streamingText && (
            <div className={`rounded-lg p-4 border-l-4 border-blue-500 ${
              isDarkMode ? 'bg-black' : 'bg-blue-50'
            }`}>
              <div className="prose max-w-none">
                <div className={`whitespace-pre-wrap ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-700'
                }`}>
                  {streamingText}
                  <span className="inline-block w-2 h-5 bg-blue-500 animate-pulse ml-1"></span>
                </div>
              </div>
            </div>
          )}

          {(summary || hasContent) && !isGenerating && (
            <>
              {isEditing ? (
                // Show markdown editor when editing
                <div className={`rounded-lg border ${
                  isDarkMode
                    ? 'bg-black border-gray-700'
                    : 'bg-amber-50/50 border-orange-200'
                }`}>
                  <textarea
                    value={editedMarkdown}
                    onChange={(e) => setEditedMarkdown(e.target.value)}
                    placeholder="Edit your consultation summary in markdown..."
                    rows={15}
                    className={`w-full px-4 py-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-teal-500 transition-colors font-mono text-sm ${
                      isDarkMode
                        ? 'bg-black text-gray-100 placeholder-gray-400'
                        : 'bg-amber-50/50 text-amber-800 placeholder-amber-600/60'
                    }`}
                  />
                </div>
              ) : (
                // Show rich text display when not editing
                <ContentEditableEditor
                  content={
                    // Use original markdown content from database
                    selectedConsultation?.edited_note ||
                    selectedConsultation?.ai_generated_note ||
                    (summary ? convertHtmlToMarkdown(summary) : '') ||
                    ''
                  }
                  onChange={(markdown) => {
                    // Convert markdown to HTML for internal state consistency
                    setSummary(convertMarkdownToHtml(markdown))
                  }}
                  isEditing={false}
                  isDarkMode={isDarkMode}
                />
              )}
            </>
          )}
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={(e) => e.target.files && handleImageUploadWithAutosave(e.target.files)}
        className="hidden"
      />
    </div>
  )
}
