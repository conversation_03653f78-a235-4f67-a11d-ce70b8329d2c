'use client'

import { useState, useEffect } from 'react'
import { X, Download } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image' // <--- ADD THIS LINE

// This declaration is for the `navigator.standalone` fix from the previous response.
declare global {
  interface Navigator {
    standalone?: boolean;
  }
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showPrompt, setShowPrompt] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Check if already installed
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      const isInWebAppiOS = window.navigator.standalone === true
      const isInWebAppChrome = window.matchMedia('(display-mode: standalone)').matches
      
      setIsInstalled(isStandalone || isInWebAppiOS || isInWebAppChrome)
    }

    // Check if mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkInstalled()
    checkMobile()

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      const promptEvent = e as BeforeInstallPromptEvent
      setDeferredPrompt(promptEvent)
      
      // Check if user has dismissed this before
      const hasSeenPrompt = localStorage.getItem('pwa_install_dismissed')
      const lastDismissed = localStorage.getItem('pwa_install_last_dismissed')
      
      // Show prompt if:
      // 1. Never seen before, OR
      // 2. Last dismissed more than 7 days ago
      if (!hasSeenPrompt || (lastDismissed && Date.now() - parseInt(lastDismissed) > 7 * 24 * 60 * 60 * 1000)) {
        // Delay showing prompt to avoid interfering with welcome modal
        setTimeout(() => {
          setShowPrompt(true)
        }, 3000) // 3 second delay
      }
    }

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowPrompt(false)
      setDeferredPrompt(null)
      localStorage.setItem('pwa_installed', 'true')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    window.addEventListener('resize', checkMobile)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('resize', checkMobile)
    }
  }, [])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('PWA install accepted')
        localStorage.setItem('pwa_install_accepted', 'true')
      } else {
        console.log('PWA install dismissed')
        localStorage.setItem('pwa_install_dismissed', 'true')
        localStorage.setItem('pwa_install_last_dismissed', Date.now().toString())
      }
      
      setShowPrompt(false)
      setDeferredPrompt(null)
    } catch (error) {
      console.error('Error during PWA install:', error)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    localStorage.setItem('pwa_install_dismissed', 'true')
    localStorage.setItem('pwa_install_last_dismissed', Date.now().toString())
  }

  // Don't show if already installed, not mobile, or no prompt available
  if (isInstalled || !isMobile || !deferredPrompt || !showPrompt) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100, scale: 0.8 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 100, scale: 0.8 }}
        transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        className="fixed bottom-4 right-4 z-50 max-w-sm"
      >
        <div className="bg-white rounded-xl shadow-2xl border border-gray-200 p-4 mx-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded-lg overflow-hidden">
                {/* <img
                  src="/icons/icon-72x72.png"
                  alt="Celer AI"
                  className="w-full h-full object-cover"
                /> */}
                <Image // <--- CHANGE THIS LINE
                  src="/icons/icon-72x72.png"
                  alt="Celer AI"
                  width={72} // <--- ADD THIS LINE (based on filename)
                  height={72} // <--- ADD THIS LINE (based on filename)
                  className="w-full h-full object-cover"
                  priority // <--- OPTIONAL: Add for images visible on initial load to improve LCP
                />
              </div>
              <div>
                <h3 className="text-sm font-semibold text-gray-900">Install Celer AI</h3>
                <p className="text-xs text-gray-500">Add to home screen</p>
              </div>
            </div>
            <button
              onClick={handleDismiss}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Content */}
          <p className="text-xs text-gray-600 mb-3 leading-relaxed">
            Get faster access and a better experience with our app.
          </p>

          {/* Actions */}
          <div className="flex space-x-2">
            <button
              onClick={handleInstallClick}
              className="flex-1 bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white text-xs font-medium py-2 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1"
            >
              <Download className="w-3 h-3" />
              <span>Install</span>
            </button>
            <button
              onClick={handleDismiss}
              className="px-3 py-2 text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              Not now
            </button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}