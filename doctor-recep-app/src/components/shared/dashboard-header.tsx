'use client'

import Link from 'next/link'
import { LogOut, Settings, Phone, Stethoscope, X, Home } from 'lucide-react'
import { logout } from '@/lib/actions/auth'
import { createContactRequest } from '@/lib/actions/contact-requests'
import { <PERSON> } from '@/lib/types'
import { useState } from 'react'

interface DashboardHeaderProps {
  user: Doctor | null
}

export function DashboardHeader({ user }: DashboardHeaderProps) {
  const [showContactModal, setShowContactModal] = useState(false)
  const [contactError, setContactError] = useState<string | null>(null)

  const [contactSubject, setContactSubject] = useState('general')
  const [contactMessage, setContactMessage] = useState('')
  const [isSubmittingContact, setIsSubmittingContact] = useState(false)

  const handleContactFounder = async () => {
    // Just show modal - don't create contact request yet
    setShowContactModal(true)
    setContactError(null)
    setContactSubject('general')
    setContactMessage('')
  }

  const handleSubmitContactForm = async () => {
    if (!contactMessage.trim()) {
      setContactError('Please enter a message')
      return
    }

    setIsSubmittingContact(true)
    setContactError(null)

    try {
      if (user?.id) {
        const result = await createContactRequest(
          user.id,
          `Subject: ${contactSubject}\n\nMessage: ${contactMessage}`,
          contactSubject
        )
        if (!result.success) {
          setContactError(result.error || 'Failed to create contact request')
          console.error('Contact request failed:', result.error)
        } else {
          console.log('Contact request created successfully')
          // Close modal after successful submission
          setTimeout(() => setShowContactModal(false), 2000)
        }
      } else {
        setContactError('User not found')
      }
    } catch (error) {
      setContactError(error instanceof Error ? error.message : 'Unknown error')
      console.error('Error creating contact request:', error)
    } finally {
      setIsSubmittingContact(false)
    }
  }

  return (
    <>
    <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-orange-200/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 sm:py-6">
          {/* Left side - User info (hidden on mobile) */}
          <div className="hidden sm:block flex-1 min-w-0">
            <div className="text-lg sm:text-xl font-bold text-slate-800">
              {/* Desktop: Single line with truncate */}
              <h2 className="truncate">
                Dr. {user?.name || 'Doctor'}
              </h2>
            </div>
            <p className="text-sm text-slate-600 truncate">
              {user?.clinic_name || 'Medical Practice'}
            </p>
          </div>

          {/* Center - Celer AI Logo */}
          <Link href="/dashboard" className="flex items-center space-x-4 flex-shrink-0 mx-4 hover:opacity-80 transition-opacity">
            <div className="relative">
              <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                <Stethoscope className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping"></div>
              <div className="absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full"></div>
            </div>
            <div className="hidden sm:block text-center">
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent">
                Celer AI
              </h1>
              <p className="text-sm text-teal-600/80">AI-Powered Healthcare</p>
            </div>
          </Link>

          {/* Right side - Navigation & Actions */}
          <div className="flex items-center gap-2 sm:gap-4 flex-1 justify-end">
            {/* Navigation Menu */}
            <nav className="hidden md:flex items-center space-x-1">
              <Link
                href="/dashboard"
                className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-orange-50 hover:text-teal-700 transition-all duration-150 transform hover:scale-105 active:scale-95"
              >
                <Home className="w-4 h-4 mr-2" />
                Dashboard
              </Link>
            </nav>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-2 sm:gap-3">
              {/* Contact Founder */}
              <button
                type="button"
                onClick={handleContactFounder}
                className="inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95"
              >
                <Phone className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">Contact</span>
              </button>

              {/* Settings Link */}
              <Link
                href="/settings"
                prefetch={true}
                className="inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95"
              >
                <Settings className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">Settings</span>
              </Link>

              {/* Logout */}
              <form action={logout}>
                <button
                  type="submit"
                  className="inline-flex items-center px-2 sm:px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-md hover:shadow-lg transition-all duration-150 transform hover:scale-105 active:scale-95"
                >
                  <LogOut className="w-4 h-4 sm:mr-2" />
                  <span className="hidden sm:inline">Logout</span>
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </header>

    {/* Contact Modal */}
    {showContactModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 ease-out scale-100">
          <div className="relative">
            {/* Header */}
            <div className="bg-gradient-to-r from-teal-500 to-emerald-600 rounded-t-2xl p-6 text-white">
              <button
                onClick={() => setShowContactModal(false)}
                className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <Phone className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Contact Founder</h3>
                  <p className="text-emerald-100">Get in touch with us</p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              {/* Contact Form */}
              <div className="space-y-4">
                {/* Subject Selection */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Subject
                  </label>
                  <select
                    value={contactSubject}
                    onChange={(e) => setContactSubject(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-black bg-white"
                  >
                    <option value="general">General Feedback</option>
                    <option value="technical">Technical Issue</option>
                    <option value="billing">Billing Question</option>
                    <option value="feature">Feature Request</option>
                    <option value="bug">Bug Report</option>
                  </select>
                </div>

                {/* Message Input */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Message
                  </label>
                  <textarea
                    value={contactMessage}
                    onChange={(e) => setContactMessage(e.target.value)}
                    placeholder="Please describe your issue or feedback..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none text-black bg-white placeholder-gray-500"
                  />
                </div>

                {/* Error/Success Messages */}
                {contactError ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="text-sm text-red-700 font-medium">
                      ❌ {contactError}
                    </div>
                  </div>
                ) : isSubmittingContact ? (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="text-sm text-blue-700 font-medium">
                      📤 Submitting your request...
                    </div>
                  </div>
                ) : null}

                {/* Submit Button */}
                <button
                  onClick={handleSubmitContactForm}
                  disabled={isSubmittingContact || !contactMessage.trim()}
                  className="w-full bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  {isSubmittingContact ? 'Submitting...' : 'Submit Request'}
                </button>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">or contact directly</span>
                  </div>
                </div>

                {/* Direct Contact Options */}
                <div className="text-center space-y-3">
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="text-lg font-semibold text-slate-800 mb-2">
                      📞 +91 8921628177
                    </div>
                    <div className="text-sm text-slate-600">
                      Available: 9 AM - 9 PM IST
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <a
                      href="tel:+918921628177"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      📞 Call Now
                    </a>
                    <a
                      href="https://wa.me/918921628177"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      💬 WhatsApp
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )}

    </>
  )
}