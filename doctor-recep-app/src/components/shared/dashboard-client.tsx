'use client'

import { useState, useEffect } from 'react'
import { ReferredWelcomeModal } from './referred-welcome-modal'
import { getReferralInfo } from '@/lib/actions/referrals'
import { ReferralInfo } from '@/lib/types'

interface DashboardClientProps {
  doctorId: string
}

export function DashboardClient({ doctorId }: DashboardClientProps) {
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null)

  useEffect(() => {
    const checkWelcomeModal = async () => {
      // Check if user has seen welcome modal before
      const hasSeenWelcome = localStorage.getItem(`welcome_seen_${doctorId}`)
      
      if (!hasSeenWelcome) {
        // Get referral info
        const result = await getReferralInfo(doctorId)
        if (result.success && result.data.referred_by) {
          setReferralInfo(result.data)
          setShowWelcomeModal(true)
        }
      }
    }

    checkWelcomeModal()
  }, [doctorId])

  const handleCloseWelcome = () => {
    setShowWelcomeModal(false)
    // Mark as seen so it doesn't show again
    localStorage.setItem(`welcome_seen_${doctorId}`, 'true')
  }

  return (
    <ReferredWelcomeModal
      isOpen={showWelcomeModal}
      onClose={handleCloseWelcome}
      referralInfo={referralInfo}
    />
  )
}