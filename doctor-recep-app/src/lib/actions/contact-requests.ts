'use server'

import { createClient } from '@/lib/supabase/server'
import { ApiResponse } from '@/lib/types'
import { revalidatePath } from 'next/cache'

export interface ContactRequest {
  id: string
  doctor_id: string
  doctor_name: string
  doctor_email: string
  clinic_name: string
  phone_number: string
  request_type: string
  message: string | null
  status: string
  contacted_at: string | null
  resolved_at: string | null
  created_at: string
  updated_at: string
}

export async function createContactRequest(
  doctorId: string,
  message?: string,
  subject?: string
): Promise<ApiResponse<string>> {
  try {
    console.log('Creating contact request for doctorId:', doctorId)
    const supabase = await createClient()

    // Get doctor information first
    console.log('Fetching doctor info...')
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('name, email, clinic_name, phone, quota_used, monthly_quota')
      .eq('id', doctorId)
      .single()

    console.log('Doctor fetch result:', { doctor, doctorError })

    if (doctor<PERSON>rror || !doctor) {
      console.error('Doctor not found:', { doctorId, doctorError })
      return { success: false, error: `Doctor not found: ${doctorError?.message || 'No doctor data'}` }
    }

    // Simple insert without checking duplicates for now
    const insertData = {
      doctor_id: doctorId,
      doctor_name: doctor.name,
      doctor_email: doctor.email,
      clinic_name: doctor.clinic_name || '',
      phone_number: doctor.phone || '',
      current_quota_used: doctor.quota_used || 0,
      monthly_quota: doctor.monthly_quota || 0,
      request_type: 'general_contact',
      message: message || 'Contact request from dashboard',
      subject: subject || 'general'
    }
    
    console.log('Creating contact request with data:', insertData)

    const { data, error } = await supabase
      .from('contact_requests')
      .insert(insertData)
      .select('id')
      .single()

    console.log('Insert result:', { data, error })

    if (error) {
      console.error('Failed to create contact request:', error)
      return { success: false, error: `Database error: ${error.message}` }
    }

    // Force revalidation of admin paths
    revalidatePath('/admin/dashboard')
    revalidatePath('/admin')
    
    console.log('Contact request created successfully with ID:', data.id)
    
    return { success: true, data: data.id }
  } catch (error) {
    console.error('Unexpected error creating contact request:', error)
    return { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` }
  }
}

export async function getContactRequests(): Promise<ApiResponse<ContactRequest[]>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error fetching contact requests:', error)
      return { success: false, error: 'Failed to fetch contact requests' }
    }

    // Only log if this is called with explicit debug flag or if there are new requests
    if (process.env.NODE_ENV === 'development') {
      console.log('Fetched contact requests:', {
        count: data?.length || 0,
        pending: data?.filter(r => r.status === 'pending').length || 0
      })
    }

    return { success: true, data: data || [] }
  } catch (error) {
    console.error('Error fetching contact requests:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getPendingContactRequests(): Promise<ApiResponse<ContactRequest[]>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    if (error) {
      return { success: false, error: 'Failed to fetch pending contact requests' }
    }

    return { success: true, data: data || [] }
  } catch (error) {
    console.error('Error fetching pending contact requests:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function updateContactRequestStatus(
  requestId: string,
  status: 'pending' | 'contacted' | 'resolved'
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('contact_requests')
      .update({ status })
      .eq('id', requestId)

    if (error) {
      return { success: false, error: 'Failed to update contact request status' }
    }

    revalidatePath('/admin/dashboard')
    return { success: true, data: true }
  } catch (error) {
    console.error('Error updating contact request status:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getContactRequestsCount(): Promise<ApiResponse<{
  total: number;
  pending: number;
  contacted: number;
  resolved: number;
}>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('status')

    if (error) {
      return { success: false, error: 'Failed to fetch contact requests count' }
    }

    const counts = {
      total: data?.length || 0,
      pending: data?.filter(r => r.status === 'pending').length || 0,
      contacted: data?.filter(r => r.status === 'contacted').length || 0,
      resolved: data?.filter(r => r.status === 'resolved').length || 0
    }

    return { success: true, data: counts }
  } catch (error) {
    console.error('Error fetching contact requests count:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function hasActiveContactRequest(doctorId: string): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('contact_requests')
      .select('id')
      .eq('doctor_id', doctorId)
      .eq('status', 'pending')
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      return { success: false, error: 'Failed to check contact request status' }
    }

    return { success: true, data: !!data }
  } catch (error) {
    console.error('Error checking contact request status:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}