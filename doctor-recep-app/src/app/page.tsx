import Link from 'next/link'
import Image from 'next/image'
import { <PERSON><PERSON>, ArrowR<PERSON>, Clock, Sparkles, Wand2, FileCheck, Zap, ChevronDown, Timer, Headphones } from 'lucide-react'
import { checkSession } from '@/lib/auth/dal'
import { redirect } from 'next/navigation'

export default async function Home() {
  // Check if user is already logged in and redirect to dashboard
  const session = await checkSession()
  if (session) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/blog"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Blog
            </Link>
            <Link
              href="/guide"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Guides
            </Link>
            <Link
              href="/login"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/signup"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Start Free
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="relative">
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-6 pt-32 pb-16">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            
            {/* Left Column */}
            <div className="space-y-8">
              <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2">
                <Sparkles className="w-4 h-4 text-indigo-600 animate-pulse" />
                <span className="text-indigo-700 text-sm font-medium">AI Magic for Medical Docs</span>
                <Wand2 className="w-4 h-4 text-purple-600" />
              </div>

              <div className="space-y-4">
                <h1 className="text-6xl md:text-7xl font-black text-slate-900 leading-none">
                  Speak.
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
                    Generate.
                  </span>
                  <span className="block text-slate-700">
                    Done.
                  </span>
                </h1>
                
                <div className="flex items-center space-x-3 text-lg text-slate-600">
                  <Timer className="w-5 h-5 text-emerald-500" />
                  <span>Medical reports in 30 seconds</span>
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-ping"></div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-6 border border-slate-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
                      <Mic className="w-6 h-6 text-white animate-pulse" />
                    </div>
                    <div>
                      <p className="font-semibold text-slate-800">Try saying:</p>
                      <p className="text-slate-600 italic">&ldquo;Patient has fever, cough, prescribed antibiotics...&rdquo;</p>
                    </div>
                  </div>
                  <ArrowRight className="w-6 h-6 text-slate-400" />
                </div>
              </div>

              <div>
                <Link
                  href="/signup"
                  className="group relative bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2"
                >
                  <Zap className="w-5 h-5" />
                  <span>Start Creating Magic</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>

              <div className="grid grid-cols-2 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-600">30s</div>
                  <div className="text-sm text-slate-600">Average Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">90%</div>
                  <div className="text-sm text-slate-600">Accuracy</div>
                </div>
              </div>
            </div>

            {/* Right Column - Video */}
            <div className="relative -mt-35">
              <div className="relative">
                <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-30 animate-pulse"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden">
                  <div style={{position: 'relative', paddingBottom: '52.708333333333336%', height: 0}}>
                    <iframe
                      src="https://www.loom.com/embed/dd8974adc0334209aee1cbe10757926d?sid=7f9a7f03-fcc1-40f8-9cd0-96115f2474f7"
                      className="absolute top-0 left-0 w-full h-full border-0"
                      allowFullScreen
                      title="Celer AI Demo"
                    />
                  </div>
                </div>
              </div>

              <div className="absolute -top-6 -left-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200">
                <div className="flex items-center space-x-2">
                  <Headphones className="w-4 h-4 text-indigo-600" />
                  <span className="text-sm font-medium text-slate-700">Voice Input</span>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200">
                <div className="flex items-center space-x-2">
                  <FileCheck className="w-4 h-4 text-emerald-600" />
                  <span className="text-sm font-medium text-slate-700">Perfect Report</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="w-6 h-6 text-slate-400" />
        </div>
      </main>

      {/* Time Section - Side Layout Like Hero */}
      <section className="py-32 relative overflow-hidden">
        {/* Same background style as hero but darker */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-slate-900"></div>
        
        {/* Floating elements like hero */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-emerald-200/20 to-cyan-200/20 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-cyan-200/10 to-blue-200/10 rounded-full blur-xl animate-pulse delay-2000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            
            {/* Left Column - Problem & Solution */}
            <div className="space-y-8 text-white">
              {/* Time Badge - Same style as hero */}
              <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-emerald-50/10 to-cyan-50/10 border border-emerald-200/20 rounded-full px-4 py-2">
                <Clock className="w-4 h-4 text-emerald-400 animate-spin" />
                <span className="text-emerald-300 text-sm font-medium">Reclaim Your Time</span>
                <Timer className="w-4 h-4 text-cyan-400" />
              </div>

              {/* Headline - Same style as hero */}
              <div className="space-y-4">
                <h2 className="text-6xl md:text-7xl font-black text-white leading-none">
                  Get Your
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 via-cyan-400 to-purple-400 animate-pulse">
                    Life Back
                  </span>
                </h2>
                
                <div className="flex items-center space-x-3 text-lg text-slate-300">
                  <Timer className="w-5 h-5 text-emerald-400" />
                  <span>Save 2+ hours daily with voice documentation</span>
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-ping"></div>
                </div>
              </div>

              {/* Time Comparison - Same style as hero demo box */}
              <div className="bg-gradient-to-r from-slate-50/5 to-emerald-50/5 rounded-2xl p-6 border border-white/10">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-red-400 text-4xl font-black mb-2">15-20</div>
                    <div className="text-slate-300 text-sm">minutes per patient</div>
                    <div className="text-red-400 text-xs mt-2">😰 Stress & paperwork</div>
                  </div>
                  <div className="text-center">
                    <div className="text-emerald-400 text-4xl font-black mb-2">30</div>
                    <div className="text-slate-300 text-sm">seconds total</div>
                    <div className="text-emerald-400 text-xs mt-2">😌 Perfect & effortless</div>
                  </div>
                </div>
              </div>

              {/* CTA - Same style as hero */}
              <div>
                <Link
                  href="/signup"
                  className="group relative bg-gradient-to-r from-emerald-600 to-cyan-600 hover:from-emerald-700 hover:to-cyan-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2"
                >
                  <Timer className="w-5 h-5" />
                  <span>Start Saving Time Now</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>

              {/* Trust indicators - Same style as hero */}
              <div className="grid grid-cols-2 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-400">2+ hrs</div>
                  <div className="text-sm text-slate-300">Daily Savings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-cyan-400">90%</div>
                  <div className="text-sm text-slate-300">Accuracy</div>
                </div>
              </div>
            </div>

            {/* Right Column - Life Activities */}
            <div className="relative">
              {/* Container with same style as video frame */}
              <div className="relative">
                <div className="absolute -inset-4 bg-gradient-to-r from-emerald-500 via-cyan-500 to-purple-500 rounded-3xl blur-lg opacity-30 animate-pulse"></div>
                <div className="relative bg-white/10 backdrop-blur-xl rounded-2xl shadow-2xl overflow-hidden border border-white/20">
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-8 text-center">
                      What will you do with 2 extra hours daily?
                    </h3>
                    
                    <div className="grid grid-cols-2 gap-6">
                      <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors">
                        <div className="text-4xl mb-3">👨‍👩‍👧‍👦</div>
                        <div className="text-white font-medium">Family Time</div>
                        <div className="text-slate-300 text-sm mt-1">Quality moments</div>
                      </div>
                      <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors">
                        <div className="text-4xl mb-3">🏃‍♂️</div>
                        <div className="text-white font-medium">Exercise</div>
                        <div className="text-slate-300 text-sm mt-1">Stay healthy</div>
                      </div>
                      <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors">
                        <div className="text-4xl mb-3">📚</div>
                        <div className="text-white font-medium">Learning</div>
                        <div className="text-slate-300 text-sm mt-1">Grow skills</div>
                      </div>
                      <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors">
                        <div className="text-4xl mb-3">😴</div>
                        <div className="text-white font-medium">Rest</div>
                        <div className="text-slate-300 text-sm mt-1">Recharge</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating elements like hero */}
              <div className="absolute -top-6 -left-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-emerald-400" />
                  <span className="text-sm font-medium text-white">2+ Hours Saved</span>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20">
                <div className="flex items-center space-x-2">
                  <Timer className="w-4 h-4 text-cyan-400" />
                  <span className="text-sm font-medium text-white">Every Day</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Minimal Footer */}
      <footer className="bg-slate-50 border-t border-slate-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <div className="relative w-8 h-8">
                <Image
                  src="/celer-ai-logo.svg"
                  alt="Celer AI"
                  width={32}
                  height={32}
                  className="rounded-lg"
                />
              </div>
              <span className="font-semibold text-slate-800">Celer AI</span>
              <span className="text-slate-500 text-sm">• Built for Indian doctors</span>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-slate-500">
              <Link href="/privacy" className="hover:text-slate-700 transition-colors">Privacy</Link>
              <Link href="/terms" className="hover:text-slate-700 transition-colors">Terms</Link>
              <a href="mailto:<EMAIL>" className="hover:text-slate-700 transition-colors">Support</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
