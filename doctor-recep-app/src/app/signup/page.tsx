import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { SignupForm } from '@/components/auth/signup-form'
import { <PERSON><PERSON><PERSON>, Wand2, ArrowRight, Users, Shield } from 'lucide-react'
import { validateReferralCode } from '@/lib/actions/referrals'

export const metadata: Metadata = {
  title: 'Sign Up - Celer AI',
  description: 'Create your Celer AI account',
}

export default async function SignupPage({ 
  searchParams 
}: { 
  searchParams: Promise<{ ref?: string }> 
}) {
  const resolvedSearchParams = await searchParams
  const referralCode = resolvedSearchParams.ref
  let referrerInfo: { valid: boolean; referrer_name?: string } = { valid: false }
  
  if (referralCode) {
    const result = await validateReferralCode(referralCode)
    if (result.success) {
      referrerInfo = result.data
    }
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/login"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
          </div>
        </div>
      </nav>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Main Content */}
      <div className="relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-6">
              <Sparkles className="w-4 h-4 text-indigo-600 animate-pulse" />
              <span className="text-indigo-700 text-sm font-medium">Join the Magic</span>
              <Wand2 className="w-4 h-4 text-purple-600" />
            </div>

            <h2 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4">
              {referrerInfo.valid ? (
                <>
                  Join
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
                    {referrerInfo.referrer_name}&apos;s
                  </span>
                  <span className="block">Network</span>
                </>
              ) : (
                <>
                  Start Your
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse relative z-10">
                     Journey
                  </span>
                </>
              )}
            </h2>

            <p className="text-lg text-slate-600 mb-6">
              {referrerInfo.valid
                ? `You're here because ${referrerInfo.referrer_name} trusts Celer AI. Happy Consulting!`
                : 'Create magical medical reports in 30 seconds'
              }
            </p>

            <div className="flex justify-center items-center space-x-6 text-slate-500 text-sm">
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4 text-indigo-600" />
                <span>Trusted by doctors</span>
              </div>
              <div className="flex items-center space-x-1">
                <Shield className="w-4 h-4 text-emerald-500" />
                <span>Secure & Private</span>
              </div>
            </div>
          </div>

          {/* Signup Form Card */}
          <div className="relative">
            <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20">
              {referrerInfo.valid && (
                <div className="mb-6 p-4 bg-gradient-to-r from-emerald-50 to-cyan-50 border border-emerald-200 rounded-xl">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-emerald-400 rounded-full animate-ping"></div>
                    <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                    <p className="text-sm text-emerald-700 font-medium">
                      Referred by Dr. {referrerInfo.referrer_name}
                    </p>
                  </div>
                </div>
              )}
              <SignupForm referralCode={referralCode} />
            </div>
          </div>

          {/* Sign in link */}
          <div className="text-center">
            <div className="bg-white/60 backdrop-blur-xl rounded-xl p-4 border border-white/30">
              <p className="text-sm text-slate-600">
                Already have an account?{' '}
                <Link
                  href="/login"
                  className="font-medium text-indigo-600 hover:text-purple-600 transition-colors duration-200 inline-flex items-center space-x-1"
                >
                  <span>Sign in here</span>
                  <ArrowRight className="w-3 h-3" />
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}