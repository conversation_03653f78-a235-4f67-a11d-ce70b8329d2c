<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the main background (matching your navbar: from-teal-500 to-emerald-600) -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Main background with rounded corners (matching rounded-xl) -->
  <rect x="32" y="32" width="448" height="448" rx="64" ry="64" fill="url(#bgGradient)" />

  <!-- Shadow effect -->
  <rect x="36" y="36" width="448" height="448" rx="64" ry="64" fill="rgba(0,0,0,0.1)" />
  <rect x="32" y="32" width="448" height="448" rx="64" ry="64" fill="url(#bgGradient)" />

  <!-- Lucide Stethoscope icon (scaled to fit nicely in container) -->
  <g transform="translate(256,256) scale(13)" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <!-- Original Lucide stethoscope paths, centered at origin -->
    <g transform="translate(-12,-12)">
      <path d="M11 2v2"/>
      <path d="M5 2v2"/>
      <path d="M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1"/>
      <path d="M8 15a6 6 0 0 0 12 0v-3"/>
      <circle cx="20" cy="10" r="2"/>
    </g>
  </g>

  <!-- Pinging dot (top-right corner, half outside/half inside like navbar) -->
  <g transform="translate(468,44)">
    <!-- Static orange dot (bigger) -->
    <circle cx="0" cy="0" r="20" fill="#fb923c"/>

    <!-- Animated ping rings -->
    <circle cx="0" cy="0" r="20" fill="#fb923c" opacity="0.7">
      <animate attributeName="r" values="20;40;20" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="20" fill="#fb923c" opacity="0.5">
      <animate attributeName="r" values="20;50;20" dur="2s" repeatCount="indefinite" begin="1s"/>
      <animate attributeName="opacity" values="0.5;0;0.5" dur="2s" repeatCount="indefinite" begin="1s"/>
    </circle>
  </g>
</svg>
