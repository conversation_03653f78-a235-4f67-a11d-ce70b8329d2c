-- Fix referral discount calculation from 10% to 20%

-- Update the create_referral_discount function to use 20% instead of 10%
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount (changed from 10%)
    discount_amount := referred_amount * 0.20;
    
    -- Only create discount record if referral_discounts table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referral_discounts') THEN
        INSERT INTO referral_discounts (
            doctor_id,
            referral_analytics_id,
            discount_amount,
            original_amount,
            valid_until,
            discount_percentage
        ) VALUES (
            referrer_doctor_id,
            referral_analytics_id,
            discount_amount,
            referred_amount,
            NOW() + INTERVAL '12 months',
            20.00
        ) RETURNING id INTO discount_id;
        
        -- Update doctor's available discount and earned amount
        UPDATE doctors 
        SET available_discount_amount = available_discount_amount + discount_amount,
            referral_discount_earned = referral_discount_earned + discount_amount
        WHERE id = referrer_doctor_id;
    END IF;
    
    RETURN discount_id;
END;
$$ LANGUAGE plpgsql;

-- Update the handle_referral_conversion function to use 20%
CREATE OR REPLACE FUNCTION handle_referral_conversion(doctor_uuid UUID)
RETURNS VOID AS $$
DECLARE
    referrer_uuid UUID;
    discount_amount DECIMAL(10,2);
    referred_amount DECIMAL(10,2);
BEGIN
    -- Get the referrer and the amount from the latest transaction
    SELECT referred_by INTO referrer_uuid
    FROM doctors 
    WHERE id = doctor_uuid;
    
    -- Get the referred amount from the latest successful transaction
    SELECT final_amount INTO referred_amount
    FROM billing_transactions 
    WHERE doctor_id = doctor_uuid 
      AND payment_status = 'completed'
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- If there's a referrer and a valid amount, process the discount
    IF referrer_uuid IS NOT NULL AND referred_amount IS NOT NULL THEN
        -- Calculate 20% discount (changed from 10%)
        discount_amount := referred_amount * 0.20;
        
        -- Update referrer's stats
        UPDATE doctors
        SET successful_referrals = successful_referrals + 1,
            referral_discount_earned = referral_discount_earned + discount_amount,
            available_discount_amount = available_discount_amount + discount_amount
        WHERE id = referrer_uuid;
        
        -- Update conversion date for the referred doctor
        UPDATE doctors
        SET conversion_date = NOW()
        WHERE id = doctor_uuid;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Update default discount percentage in referral_discounts table
ALTER TABLE referral_discounts 
ALTER COLUMN discount_percentage SET DEFAULT 20.00;

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'Referral discount calculation updated from 10%% to 20%%';
    RAISE NOTICE 'Updated functions:';
    RAISE NOTICE '- create_referral_discount(UUID, UUID, DECIMAL)';
    RAISE NOTICE '- handle_referral_conversion(UUID)';
    RAISE NOTICE 'Updated referral_discounts table default percentage to 20%%';
END $$;