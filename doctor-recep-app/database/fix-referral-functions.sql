-- Fix Missing Referral Functions
-- Run this in Supabase SQL Editor to restore referral functionality

-- Function 1: Handle referral conversion
CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    referrer_uuid UUID;
    discount_amount DECIMAL(10,2) := 20.00;
BEGIN
    -- Get referrer information
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
    
    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Update conversion date for referred doctor
    UPDATE doctors
    SET conversion_date = NOW()
    WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
    
    -- Update referral analytics
    UPDATE referral_analytics
    SET status = 'converted',
        conversion_date = NOW(),
        discount_earned = discount_amount
    WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
    
    -- Update referrer stats and add available discount
    UPDATE doctors
    SET successful_referrals = successful_referrals + 1,
        referral_discount_earned = referral_discount_earned + discount_amount,
        available_discount_amount = available_discount_amount + discount_amount
    WHERE id = referrer_uuid;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Function 2: Create referral discount record
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount
    discount_amount := referred_amount * 0.20;
    
    -- Only create discount record if referral_discounts table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referral_discounts') THEN
        INSERT INTO referral_discounts (
            doctor_id,
            referral_analytics_id,
            discount_amount,
            original_amount,
            valid_until
        ) VALUES (
            referrer_doctor_id,
            referral_analytics_id,
            discount_amount,
            referred_amount,
            NOW() + INTERVAL '12 months'
        ) RETURNING id INTO discount_id;
    END IF;
    
    RETURN discount_id;
END;
$$ language 'plpgsql';

-- Function 3: Complete payment with referral handling
CREATE OR REPLACE FUNCTION complete_payment(transaction_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
    referral_analytics_id UUID;
BEGIN
    -- Get transaction details with referral info
    SELECT bt.*, d.referred_by INTO transaction_record
    FROM billing_transactions bt
    JOIN doctors d ON bt.doctor_id = d.id
    WHERE bt.id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Update transaction status
    UPDATE billing_transactions
    SET payment_status = 'paid',
        payment_date = NOW(),
        updated_at = NOW()
    WHERE id = transaction_id;
    
    -- Update doctor billing status
    UPDATE doctors
    SET billing_status = 'active',
        last_payment_date = NOW(),
        next_billing_date = transaction_record.billing_period_end
    WHERE id = transaction_record.doctor_id;
    
    -- Handle referral conversion if this is a referred doctor's first payment
    IF transaction_record.referred_by IS NOT NULL THEN
        -- Check if this is their first payment
        IF NOT EXISTS (
            SELECT 1 FROM billing_transactions 
            WHERE doctor_id = transaction_record.doctor_id 
            AND payment_status = 'paid' 
            AND id != transaction_id
        ) THEN
            -- Get referral analytics ID
            SELECT id INTO referral_analytics_id 
            FROM referral_analytics 
            WHERE referred_doctor_id = transaction_record.doctor_id 
            AND status = 'pending'
            LIMIT 1;
            
            -- Mark referral as converted and update referrer stats
            PERFORM handle_referral_conversion(transaction_record.doctor_id);
            
            -- Create discount record if referral analytics exists
            IF referral_analytics_id IS NOT NULL THEN
                PERFORM create_referral_discount(
                    transaction_record.referred_by,
                    referral_analytics_id,
                    transaction_record.final_amount
                );
            END IF;
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Test the functions exist
DO $$
BEGIN
    RAISE NOTICE 'Referral functions created successfully!';
    RAISE NOTICE 'Available functions:';
    RAISE NOTICE '- handle_referral_conversion(UUID)';
    RAISE NOTICE '- create_referral_discount(UUID, UUID, DECIMAL)';
    RAISE NOTICE '- complete_payment(UUID)';
END $$;