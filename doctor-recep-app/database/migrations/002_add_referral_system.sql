-- Add referral system to the database
-- This migration adds referral tracking capabilities

-- First, change the default monthly quota from 100 to 50
ALTER TABLE doctors ALTER COLUMN monthly_quota SET DEFAULT 50;

-- Add referral columns to doctors table
ALTER TABLE doctors 
ADD COLUMN IF NOT EXISTS referral_code TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS referred_by UUID REFERENCES doctors(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS conversion_date TIMESTAMPTZ NULL,
ADD COLUMN IF NOT EXISTS referral_discount_earned DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS total_referrals INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS successful_referrals INT DEFAULT 0;

-- Create referral_analytics table for detailed tracking
CREATE TABLE IF NOT EXISTS referral_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  referrer_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  referred_doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  referral_code TEXT NOT NULL,
  signup_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  conversion_date TIMESTAMPTZ NULL,
  discount_earned DECIMAL(10,2) DEFAULT 0.00,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'converted', 'expired')),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_doctors_referral_code ON doctors(referral_code);
CREATE INDEX IF NOT EXISTS idx_doctors_referred_by ON doctors(referred_by);
CREATE INDEX IF NOT EXISTS idx_referral_analytics_referrer_id ON referral_analytics(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referral_analytics_referred_doctor_id ON referral_analytics(referred_doctor_id);
CREATE INDEX IF NOT EXISTS idx_referral_analytics_status ON referral_analytics(status);
CREATE INDEX IF NOT EXISTS idx_referral_analytics_created_at ON referral_analytics(created_at);

-- Enable RLS for referral_analytics table
ALTER TABLE referral_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policy for referral_analytics
CREATE POLICY "Allow all operations on referral_analytics" ON referral_analytics
  FOR ALL USING (true);

-- Function to generate unique referral code
CREATE OR REPLACE FUNCTION generate_referral_code(doctor_name TEXT, doctor_email TEXT)
RETURNS TEXT AS $$
DECLARE
    base_code TEXT;
    final_code TEXT;
    counter INT := 0;
BEGIN
    -- Create base code from name and email
    base_code := LOWER(
        REGEXP_REPLACE(
            SUBSTRING(doctor_name FROM 1 FOR 8) || 
            SUBSTRING(MD5(doctor_email) FROM 1 FOR 4),
            '[^a-z0-9]', '', 'g'
        )
    );
    
    final_code := base_code;
    
    -- Ensure uniqueness
    WHILE EXISTS (SELECT 1 FROM doctors WHERE referral_code = final_code) LOOP
        counter := counter + 1;
        final_code := base_code || counter::TEXT;
    END LOOP;
    
    RETURN final_code;
END;
$$ language 'plpgsql';

-- Function to update referral code for existing doctors
CREATE OR REPLACE FUNCTION update_existing_doctors_referral_codes()
RETURNS INT AS $$
DECLARE
    doctor_record RECORD;
    updated_count INT := 0;
BEGIN
    FOR doctor_record IN
        SELECT id, name, email
        FROM doctors
        WHERE referral_code IS NULL
    LOOP
        UPDATE doctors
        SET referral_code = generate_referral_code(doctor_record.name, doctor_record.email)
        WHERE id = doctor_record.id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ language 'plpgsql';

-- Function to handle referral conversion
CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    referrer_uuid UUID;
    referral_record RECORD;
    discount_amount DECIMAL(10,2) := 10.00; -- 10% discount
BEGIN
    -- Get referrer information
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
    
    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Update conversion date for referred doctor
    UPDATE doctors
    SET conversion_date = NOW()
    WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
    
    -- Update referral analytics
    UPDATE referral_analytics
    SET status = 'converted',
        conversion_date = NOW(),
        discount_earned = discount_amount
    WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
    
    -- Update referrer stats
    UPDATE doctors
    SET successful_referrals = successful_referrals + 1,
        referral_discount_earned = referral_discount_earned + discount_amount
    WHERE id = referrer_uuid;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Trigger to automatically set referral code for new doctors
CREATE OR REPLACE FUNCTION set_referral_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.referral_code IS NULL THEN
        NEW.referral_code := generate_referral_code(NEW.name, NEW.email);
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_doctor_referral_code BEFORE INSERT ON doctors
    FOR EACH ROW EXECUTE FUNCTION set_referral_code();

-- Update existing doctors with referral codes
SELECT update_existing_doctors_referral_codes();

-- Trigger for updated_at on referral_analytics
CREATE TRIGGER update_referral_analytics_updated_at BEFORE UPDATE ON referral_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();