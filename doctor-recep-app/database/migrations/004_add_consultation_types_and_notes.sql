-- Migration: Add consultation types and notes functionality
-- This adds support for different consultation types (outpatient, discharge, surgery)
-- and optional text notes from doctors

BEGIN;

-- Add consultation type enum
CREATE TYPE consultation_type_enum AS ENUM ('outpatient', 'discharge', 'surgery');

-- Add new columns to consultations table
ALTER TABLE consultations 
ADD COLUMN consultation_type consultation_type_enum DEFAULT 'outpatient' NOT NULL,
ADD COLUMN doctor_notes TEXT NULL,
ADD COLUMN additional_notes TEXT NULL;

-- Add index for consultation type for efficient filtering
CREATE INDEX idx_consultations_consultation_type ON consultations(consultation_type);

-- Update existing consultations to have default type
UPDATE consultations SET consultation_type = 'outpatient' WHERE consultation_type IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN consultations.consultation_type IS 'Type of consultation: outpatient (regular visit), discharge (patient leaving), surgery (surgical procedure)';
COMMENT ON COLUMN consultations.doctor_notes IS 'Optional notes added by doctor during recording';
COMMENT ON COLUMN consultations.additional_notes IS 'Optional additional notes added in consultation modal';

COMMIT;