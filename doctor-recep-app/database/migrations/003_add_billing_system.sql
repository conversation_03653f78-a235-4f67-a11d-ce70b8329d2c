-- Add billing system to track payments and referral discounts
-- This allows admin to manually track who paid and apply referral discounts

-- Create billing_plans table for different subscription plans
CREATE TABLE IF NOT EXISTS billing_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  monthly_price DECIMAL(10,2) NOT NULL,
  quota_limit INT NOT NULL,
  features JSONB DEFAULT '{}'::jsonb,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create billing_transactions table to track payments
CREATE TABLE IF NOT EXISTS billing_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  plan_id UUID REFERENCES billing_plans(id),
  amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0.00,
  final_amount DECIMAL(10,2) NOT NULL,
  payment_method TEXT,
  payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_date TIMESTAMPTZ NULL,
  billing_period_start TIMESTAMPTZ NOT NULL,
  billing_period_end TIMESTAMPTZ NOT NULL,
  payment_reference TEXT,
  notes TEXT,
  created_by UUID REFERENCES admins(id),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create referral_discounts table to track referral-based discounts
CREATE TABLE IF NOT EXISTS referral_discounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  referral_analytics_id UUID REFERENCES referral_analytics(id) ON DELETE CASCADE,
  discount_percentage DECIMAL(5,2) DEFAULT 20.00,
  discount_amount DECIMAL(10,2) NOT NULL,
  original_amount DECIMAL(10,2) NOT NULL,
  applied_to_transaction_id UUID REFERENCES billing_transactions(id),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'applied', 'expired')),
  valid_until TIMESTAMPTZ NOT NULL,
  applied_at TIMESTAMPTZ NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add billing-related columns to doctors table
ALTER TABLE doctors 
ADD COLUMN IF NOT EXISTS current_plan_id UUID REFERENCES billing_plans(id),
ADD COLUMN IF NOT EXISTS billing_status TEXT DEFAULT 'trial' CHECK (billing_status IN ('trial', 'active', 'suspended', 'cancelled')),
ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
ADD COLUMN IF NOT EXISTS last_payment_date TIMESTAMPTZ NULL,
ADD COLUMN IF NOT EXISTS next_billing_date TIMESTAMPTZ NULL,
ADD COLUMN IF NOT EXISTS available_discount_amount DECIMAL(10,2) DEFAULT 0.00;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_billing_transactions_doctor_id ON billing_transactions(doctor_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_payment_status ON billing_transactions(payment_status);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_payment_date ON billing_transactions(payment_date);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_billing_period ON billing_transactions(billing_period_start, billing_period_end);
CREATE INDEX IF NOT EXISTS idx_referral_discounts_doctor_id ON referral_discounts(doctor_id);
CREATE INDEX IF NOT EXISTS idx_referral_discounts_status ON referral_discounts(status);
CREATE INDEX IF NOT EXISTS idx_referral_discounts_valid_until ON referral_discounts(valid_until);
CREATE INDEX IF NOT EXISTS idx_doctors_billing_status ON doctors(billing_status);
CREATE INDEX IF NOT EXISTS idx_doctors_trial_ends_at ON doctors(trial_ends_at);

-- Enable RLS for new tables
ALTER TABLE billing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_discounts ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Allow all operations on billing_plans" ON billing_plans FOR ALL USING (true);
CREATE POLICY "Allow all operations on billing_transactions" ON billing_transactions FOR ALL USING (true);
CREATE POLICY "Allow all operations on referral_discounts" ON referral_discounts FOR ALL USING (true);

-- Insert default billing plans
INSERT INTO billing_plans (name, description, monthly_price, quota_limit, features) VALUES
('Starter', 'Perfect for small clinics', 999.00, 50, '{"features": ["50 AI Consultations", "Basic Templates", "Email Support"]}'),
('Professional', 'For growing practices', 1999.00, 150, '{"features": ["150 AI Consultations", "Advanced Templates", "Priority Support", "Analytics"]}'),
('Enterprise', 'For large healthcare facilities', 3999.00, 500, '{"features": ["500 AI Consultations", "Custom Templates", "24/7 Support", "Advanced Analytics", "API Access"]}');

-- Function to create referral discount when referral converts
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount, max 100% of next bill
    discount_amount := LEAST(referred_amount * 0.20, referred_amount);
    
    -- Create referral discount
    INSERT INTO referral_discounts (
        doctor_id,
        referral_analytics_id,
        discount_amount,
        original_amount,
        valid_until
    ) VALUES (
        referrer_doctor_id,
        referral_analytics_id,
        discount_amount,
        referred_amount,
        NOW() + INTERVAL '12 months'
    ) RETURNING id INTO discount_id;
    
    -- Update doctor's available discount
    UPDATE doctors 
    SET available_discount_amount = available_discount_amount + discount_amount
    WHERE id = referrer_doctor_id;
    
    RETURN discount_id;
END;
$$ language 'plpgsql';

-- Function to apply discount to transaction
CREATE OR REPLACE FUNCTION apply_referral_discount(transaction_id UUID, discount_amount DECIMAL)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
    doctor_available_discount DECIMAL(10,2);
    applied_amount DECIMAL(10,2);
BEGIN
    -- Get transaction details
    SELECT * INTO transaction_record
    FROM billing_transactions 
    WHERE id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Get doctor's available discount
    SELECT available_discount_amount INTO doctor_available_discount
    FROM doctors
    WHERE id = transaction_record.doctor_id;
    
    -- Calculate amount to apply (minimum of requested, available, and transaction amount)
    applied_amount := LEAST(discount_amount, doctor_available_discount, transaction_record.amount);
    
    -- Update transaction
    UPDATE billing_transactions
    SET discount_amount = applied_amount,
        final_amount = amount - applied_amount
    WHERE id = transaction_id;
    
    -- Update doctor's available discount
    UPDATE doctors
    SET available_discount_amount = available_discount_amount - applied_amount
    WHERE id = transaction_record.doctor_id;
    
    -- Update referral discounts status
    UPDATE referral_discounts
    SET status = 'applied',
        applied_to_transaction_id = transaction_id,
        applied_at = NOW()
    WHERE doctor_id = transaction_record.doctor_id 
    AND status = 'pending'
    AND discount_amount <= applied_amount;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Function to mark payment as completed and handle referral conversion
CREATE OR REPLACE FUNCTION complete_payment(transaction_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
BEGIN
    -- Get transaction details
    SELECT bt.*, d.referred_by INTO transaction_record
    FROM billing_transactions bt
    JOIN doctors d ON bt.doctor_id = d.id
    WHERE bt.id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Update transaction status
    UPDATE billing_transactions
    SET payment_status = 'paid',
        payment_date = NOW()
    WHERE id = transaction_id;
    
    -- Update doctor billing status
    UPDATE doctors
    SET billing_status = 'active',
        last_payment_date = NOW(),
        next_billing_date = transaction_record.billing_period_end
    WHERE id = transaction_record.doctor_id;
    
    -- If this is a referred doctor's first payment, handle referral conversion
    IF transaction_record.referred_by IS NOT NULL THEN
        -- Check if this is their first payment
        IF NOT EXISTS (
            SELECT 1 FROM billing_transactions 
            WHERE doctor_id = transaction_record.doctor_id 
            AND payment_status = 'paid' 
            AND id != transaction_id
        ) THEN
            -- Mark referral as converted
            PERFORM handle_referral_conversion(transaction_record.doctor_id);
            
            -- Create discount for referrer
            PERFORM create_referral_discount(
                transaction_record.referred_by,
                (SELECT id FROM referral_analytics WHERE referred_doctor_id = transaction_record.doctor_id LIMIT 1),
                transaction_record.final_amount
            );
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_billing_transactions_updated_at BEFORE UPDATE ON billing_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_plans_updated_at BEFORE UPDATE ON billing_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_referral_discounts_updated_at BEFORE UPDATE ON referral_discounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();