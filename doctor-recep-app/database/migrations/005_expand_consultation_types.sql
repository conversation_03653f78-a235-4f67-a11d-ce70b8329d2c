-- Migration: Expand consultation types to support all 8 types
-- This adds support for 5 additional consultation types: radiology, dermatology, cardiology_echo, ivf_cycle, pathology

BEGIN;

-- Add new values to the existing enum
ALTER TYPE consultation_type_enum ADD VALUE 'radiology';
ALTER TYPE consultation_type_enum ADD VALUE 'dermatology';
ALTER TYPE consultation_type_enum ADD VALUE 'cardiology_echo';
ALTER TYPE consultation_type_enum ADD VALUE 'ivf_cycle';
ALTER TYPE consultation_type_enum ADD VALUE 'pathology';

-- Update the comment to reflect all supported types
COMMENT ON COLUMN consultations.consultation_type IS 'Type of consultation: outpatient (regular visit), discharge (patient leaving), surgery (surgical procedure), radiology (imaging interpretation), dermatology (SOAP note format), cardiology_echo (echocardiogram report), ivf_cycle (reproductive medicine summary), pathology (histopathology report)';

COMMIT;
