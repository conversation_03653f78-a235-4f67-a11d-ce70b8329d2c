-- Doctor Voice & Image-Based Patient Summary System Database Schema
-- Multi-tenant architecture with Row Level Security

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Doctors table
CREATE TABLE doctors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  name TEXT NOT NULL,
  phone TEXT,
  clinic_name TEXT,
  template_config JSONB NOT NULL DEFAULT '{
    "prescription_format": "standard",
    "language": "english",
    "tone": "professional",
    "sections": ["symptoms", "diagnosis", "prescription", "advice", "follow_up"]
  }'::jsonb,
  -- Quota system fields
  monthly_quota INT NOT NULL DEFAULT 50,
  quota_used INT NOT NULL DEFAULT 0,
  quota_reset_at TIMESTAMPTZ NOT NULL DEFAULT DATE_TRUNC('month', NOW()) + INTERVAL '1 month',
  -- Admin approval system
  approved BOOLEAN NOT NULL DEFAULT FALSE,
  approved_by UUI<PERSON> NULL,
  approved_at TIMESTAMPTZ NULL,
  -- Referral system fields
  referral_code TEXT UNIQUE,
  referred_by UUID REFERENCES doctors(id) ON DELETE SET NULL,
  conversion_date TIMESTAMPTZ NULL,
  referral_discount_earned DECIMAL(10,2) DEFAULT 0.00,
  total_referrals INT DEFAULT 0,
  successful_referrals INT DEFAULT 0,
  -- Billing system fields
  current_plan_id UUID,
  billing_status TEXT DEFAULT 'trial' CHECK (billing_status IN ('trial', 'active', 'suspended', 'cancelled')),
  trial_ends_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
  last_payment_date TIMESTAMPTZ NULL,
  next_billing_date TIMESTAMPTZ NULL,
  available_discount_amount DECIMAL(10,2) DEFAULT 0.00,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Admin users table
CREATE TABLE admins (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Usage logs table for quota tracking
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  consultation_id UUID REFERENCES consultations(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL CHECK (action_type IN ('ai_generation', 'quota_reset', 'quota_update')),
  quota_before INT,
  quota_after INT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Referral analytics table for detailed tracking
CREATE TABLE referral_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  referrer_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  referred_doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  referral_code TEXT NOT NULL,
  signup_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  conversion_date TIMESTAMPTZ NULL,
  discount_earned DECIMAL(10,2) DEFAULT 0.00,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'converted', 'expired')),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Billing plans table for different subscription plans
CREATE TABLE billing_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  monthly_price DECIMAL(10,2) NOT NULL,
  quota_limit INT NOT NULL,
  features JSONB DEFAULT '{}'::jsonb,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Billing transactions table to track payments
CREATE TABLE billing_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  plan_id UUID REFERENCES billing_plans(id),
  amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0.00,
  final_amount DECIMAL(10,2) NOT NULL,
  payment_method TEXT,
  payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_date TIMESTAMPTZ NULL,
  billing_period_start TIMESTAMPTZ NOT NULL,
  billing_period_end TIMESTAMPTZ NOT NULL,
  payment_reference TEXT,
  notes TEXT,
  created_by UUID REFERENCES admins(id),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Referral discounts table to track referral-based discounts
CREATE TABLE referral_discounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  referral_analytics_id UUID REFERENCES referral_analytics(id) ON DELETE CASCADE,
  discount_percentage DECIMAL(5,2) DEFAULT 10.00,
  discount_amount DECIMAL(10,2) NOT NULL,
  original_amount DECIMAL(10,2) NOT NULL,
  applied_to_transaction_id UUID REFERENCES billing_transactions(id),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'applied', 'expired')),
  valid_until TIMESTAMPTZ NOT NULL,
  applied_at TIMESTAMPTZ NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Contact requests table for quota upgrade requests
CREATE TABLE contact_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  doctor_name TEXT NOT NULL,
  doctor_email TEXT NOT NULL,
  clinic_name TEXT,
  phone TEXT,
  current_quota_used INT NOT NULL,
  monthly_quota INT NOT NULL,
  message TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'contacted', 'resolved')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Consultations table (one per patient visit) - Updated for Supabase Storage URLs
CREATE TABLE consultations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
  submitted_by TEXT CHECK (submitted_by IN ('doctor','receptionist')) NOT NULL,
  -- Replace base64 fields with storage URLs
  primary_audio_url TEXT NOT NULL, -- URL to primary audio file in Supabase Storage
  additional_audio_urls JSONB NULL DEFAULT '[]'::jsonb, -- Array of additional audio URLs
  image_urls JSONB NULL DEFAULT '[]'::jsonb, -- Array of image URLs
  -- Keep existing fields
  ai_generated_note TEXT,
  edited_note TEXT,
  status TEXT CHECK (status IN ('pending','generated','approved')) NOT NULL DEFAULT 'pending',
  patient_number INTEGER,
  -- Add metadata for file management
  total_file_size_bytes BIGINT DEFAULT 0, -- Track total file size for quota management
  file_retention_until TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'), -- Auto-cleanup date
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_consultations_doctor_id_status ON consultations(doctor_id, status);
CREATE INDEX idx_consultations_created_at ON consultations(created_at);
CREATE INDEX idx_consultations_file_retention ON consultations(file_retention_until); -- For cleanup jobs
CREATE INDEX idx_doctors_email ON doctors(email);
CREATE INDEX idx_doctors_approved ON doctors(approved);
CREATE INDEX idx_doctors_quota_reset_at ON doctors(quota_reset_at);
CREATE INDEX idx_doctors_referral_code ON doctors(referral_code);
CREATE INDEX idx_doctors_referred_by ON doctors(referred_by);
CREATE INDEX idx_doctors_billing_status ON doctors(billing_status);
CREATE INDEX idx_doctors_trial_ends_at ON doctors(trial_ends_at);
CREATE INDEX idx_admins_email ON admins(email);
CREATE INDEX idx_usage_logs_doctor_id ON usage_logs(doctor_id);
CREATE INDEX idx_usage_logs_created_at ON usage_logs(created_at);
CREATE INDEX idx_usage_logs_action_type ON usage_logs(action_type);
CREATE INDEX idx_referral_analytics_referrer_id ON referral_analytics(referrer_id);
CREATE INDEX idx_referral_analytics_referred_doctor_id ON referral_analytics(referred_doctor_id);
CREATE INDEX idx_referral_analytics_status ON referral_analytics(status);
CREATE INDEX idx_referral_analytics_created_at ON referral_analytics(created_at);
CREATE INDEX idx_billing_transactions_doctor_id ON billing_transactions(doctor_id);
CREATE INDEX idx_billing_transactions_payment_status ON billing_transactions(payment_status);
CREATE INDEX idx_billing_transactions_payment_date ON billing_transactions(payment_date);
CREATE INDEX idx_billing_transactions_billing_period ON billing_transactions(billing_period_start, billing_period_end);
CREATE INDEX idx_referral_discounts_doctor_id ON referral_discounts(doctor_id);
CREATE INDEX idx_referral_discounts_status ON referral_discounts(status);
CREATE INDEX idx_referral_discounts_valid_until ON referral_discounts(valid_until);

-- Enable Row Level Security
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultations ENABLE ROW LEVEL SECURITY;
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_discounts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for doctors table
-- Doctors can only access their own data
CREATE POLICY "Doctors can view their own profile" ON doctors
  FOR SELECT USING (true);  -- Allow reading for authentication

CREATE POLICY "Doctors can update their own profile" ON doctors
  FOR UPDATE USING (true);  -- Allow updates for profile management

CREATE POLICY "Allow doctor registration" ON doctors
  FOR INSERT WITH CHECK (true);  -- Allow new doctor registration

-- RLS Policies for consultations table
-- Consultations are strictly scoped by doctor_id for multitenancy
CREATE POLICY "Doctors can access their own consultations" ON consultations
  FOR ALL USING (true);  -- Simplified for custom auth - app layer handles doctor_id filtering

-- RLS Policies for admins table
-- Allow all operations for now since we're using custom auth
CREATE POLICY "Allow all operations on admins" ON admins
  FOR ALL USING (true);

-- RLS Policies for usage_logs table
-- Allow all operations for now since we're using custom auth
CREATE POLICY "Allow all operations on usage_logs" ON usage_logs
  FOR ALL USING (true);

-- RLS Policies for new tables
CREATE POLICY "Allow all operations on referral_analytics" ON referral_analytics
  FOR ALL USING (true);

CREATE POLICY "Allow all operations on billing_plans" ON billing_plans
  FOR ALL USING (true);

CREATE POLICY "Allow all operations on billing_transactions" ON billing_transactions
  FOR ALL USING (true);

CREATE POLICY "Allow all operations on referral_discounts" ON referral_discounts
  FOR ALL USING (true);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_doctors_updated_at BEFORE UPDATE ON doctors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_consultations_updated_at BEFORE UPDATE ON consultations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admins_updated_at BEFORE UPDATE ON admins
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Triggers for updated_at on new tables
CREATE TRIGGER update_referral_analytics_updated_at BEFORE UPDATE ON referral_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_plans_updated_at BEFORE UPDATE ON billing_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_transactions_updated_at BEFORE UPDATE ON billing_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_referral_discounts_updated_at BEFORE UPDATE ON referral_discounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Auto-increment patient number per doctor per day
CREATE OR REPLACE FUNCTION set_patient_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.patient_number IS NULL THEN
        SELECT COALESCE(MAX(patient_number), 0) + 1
        INTO NEW.patient_number
        FROM consultations
        WHERE doctor_id = NEW.doctor_id
        AND DATE(created_at) = DATE(NOW());
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_consultation_patient_number BEFORE INSERT ON consultations
    FOR EACH ROW EXECUTE FUNCTION set_patient_number();

-- Quota management functions
CREATE OR REPLACE FUNCTION check_and_update_quota(doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    current_quota_used INT;
    monthly_quota_limit INT;
    quota_reset_date TIMESTAMPTZ;
BEGIN
    -- Get current quota info
    SELECT quota_used, monthly_quota, quota_reset_at
    INTO current_quota_used, monthly_quota_limit, quota_reset_date
    FROM doctors
    WHERE id = doctor_uuid AND approved = true;

    -- Check if doctor exists and is approved
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Check if quota needs to be reset (monthly reset)
    IF quota_reset_date <= NOW() THEN
        UPDATE doctors
        SET quota_used = 0,
            quota_reset_at = DATE_TRUNC('month', NOW()) + INTERVAL '1 month'
        WHERE id = doctor_uuid;
        current_quota_used = 0;

        -- Log quota reset
        INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
        VALUES (doctor_uuid, 'quota_reset', current_quota_used, 0, '{"reason": "monthly_reset"}'::jsonb);
    END IF;

    -- Check if quota is available
    IF current_quota_used >= monthly_quota_limit THEN
        RETURN FALSE;
    END IF;

    -- Update quota usage
    UPDATE doctors
    SET quota_used = quota_used + 1
    WHERE id = doctor_uuid;

    -- Log quota usage
    INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
    VALUES (doctor_uuid, 'ai_generation', current_quota_used, current_quota_used + 1, '{"timestamp": "' || NOW() || '"}'::jsonb);

    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Function to reset all doctor quotas (for monthly cron job)
CREATE OR REPLACE FUNCTION reset_all_quotas()
RETURNS INT AS $$
DECLARE
    reset_count INT := 0;
    doctor_record RECORD;
BEGIN
    FOR doctor_record IN
        SELECT id, quota_used
        FROM doctors
        WHERE quota_reset_at <= NOW() AND approved = true
    LOOP
        UPDATE doctors
        SET quota_used = 0,
            quota_reset_at = DATE_TRUNC('month', NOW()) + INTERVAL '1 month'
        WHERE id = doctor_record.id;

        -- Log quota reset
        INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
        VALUES (doctor_record.id, 'quota_reset', doctor_record.quota_used, 0, '{"reason": "monthly_batch_reset"}'::jsonb);

        reset_count := reset_count + 1;
    END LOOP;

    RETURN reset_count;
END;
$$ language 'plpgsql';

-- Function to generate unique referral code
CREATE OR REPLACE FUNCTION generate_referral_code(doctor_name TEXT, doctor_email TEXT)
RETURNS TEXT AS $$
DECLARE
    base_code TEXT;
    final_code TEXT;
    counter INT := 0;
BEGIN
    -- Create base code from name and email
    base_code := LOWER(
        REGEXP_REPLACE(
            SUBSTRING(doctor_name FROM 1 FOR 8) || 
            SUBSTRING(MD5(doctor_email) FROM 1 FOR 4),
            '[^a-z0-9]', '', 'g'
        )
    );
    
    final_code := base_code;
    
    -- Ensure uniqueness
    WHILE EXISTS (SELECT 1 FROM doctors WHERE referral_code = final_code) LOOP
        counter := counter + 1;
        final_code := base_code || counter::TEXT;
    END LOOP;
    
    RETURN final_code;
END;
$$ language 'plpgsql';

-- Function to handle referral conversion
CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    referrer_uuid UUID;
    referral_record RECORD;
    discount_amount DECIMAL(10,2) := 10.00; -- 10% discount
BEGIN
    -- Get referrer information
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
    
    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Update conversion date for referred doctor
    UPDATE doctors
    SET conversion_date = NOW()
    WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
    
    -- Update referral analytics
    UPDATE referral_analytics
    SET status = 'converted',
        conversion_date = NOW(),
        discount_earned = discount_amount
    WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
    
    -- Update referrer stats
    UPDATE doctors
    SET successful_referrals = successful_referrals + 1,
        referral_discount_earned = referral_discount_earned + discount_amount
    WHERE id = referrer_uuid;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Trigger to automatically set referral code for new doctors
CREATE OR REPLACE FUNCTION set_referral_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.referral_code IS NULL THEN
        NEW.referral_code := generate_referral_code(NEW.name, NEW.email);
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_doctor_referral_code BEFORE INSERT ON doctors
    FOR EACH ROW EXECUTE FUNCTION set_referral_code();

-- Function to create referral discount when referral converts
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 10% discount, max 100% of next bill
    discount_amount := LEAST(referred_amount * 0.10, referred_amount);
    
    -- Create referral discount
    INSERT INTO referral_discounts (
        doctor_id,
        referral_analytics_id,
        discount_amount,
        original_amount,
        valid_until
    ) VALUES (
        referrer_doctor_id,
        referral_analytics_id,
        discount_amount,
        referred_amount,
        NOW() + INTERVAL '12 months'
    ) RETURNING id INTO discount_id;
    
    -- Update doctor's available discount
    UPDATE doctors 
    SET available_discount_amount = available_discount_amount + discount_amount
    WHERE id = referrer_doctor_id;
    
    RETURN discount_id;
END;
$$ language 'plpgsql';

-- Function to apply discount to transaction
CREATE OR REPLACE FUNCTION apply_referral_discount(transaction_id UUID, discount_amount DECIMAL)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
    doctor_available_discount DECIMAL(10,2);
    applied_amount DECIMAL(10,2);
BEGIN
    -- Get transaction details
    SELECT * INTO transaction_record
    FROM billing_transactions 
    WHERE id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Get doctor's available discount
    SELECT available_discount_amount INTO doctor_available_discount
    FROM doctors
    WHERE id = transaction_record.doctor_id;
    
    -- Calculate amount to apply (minimum of requested, available, and transaction amount)
    applied_amount := LEAST(discount_amount, doctor_available_discount, transaction_record.amount);
    
    -- Update transaction
    UPDATE billing_transactions
    SET discount_amount = applied_amount,
        final_amount = amount - applied_amount
    WHERE id = transaction_id;
    
    -- Update doctor's available discount
    UPDATE doctors
    SET available_discount_amount = available_discount_amount - applied_amount
    WHERE id = transaction_record.doctor_id;
    
    -- Update referral discounts status
    UPDATE referral_discounts
    SET status = 'applied',
        applied_to_transaction_id = transaction_id,
        applied_at = NOW()
    WHERE doctor_id = transaction_record.doctor_id 
    AND status = 'pending'
    AND discount_amount <= applied_amount;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Function to mark payment as completed and handle referral conversion
CREATE OR REPLACE FUNCTION complete_payment(transaction_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
BEGIN
    -- Get transaction details
    SELECT bt.*, d.referred_by INTO transaction_record
    FROM billing_transactions bt
    JOIN doctors d ON bt.doctor_id = d.id
    WHERE bt.id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Update transaction status
    UPDATE billing_transactions
    SET payment_status = 'paid',
        payment_date = NOW()
    WHERE id = transaction_id;
    
    -- Update doctor billing status
    UPDATE doctors
    SET billing_status = 'active',
        last_payment_date = NOW(),
        next_billing_date = transaction_record.billing_period_end
    WHERE id = transaction_record.doctor_id;
    
    -- If this is a referred doctor's first payment, handle referral conversion
    IF transaction_record.referred_by IS NOT NULL THEN
        -- Check if this is their first payment
        IF NOT EXISTS (
            SELECT 1 FROM billing_transactions 
            WHERE doctor_id = transaction_record.doctor_id 
            AND payment_status = 'paid' 
            AND id != transaction_id
        ) THEN
            -- Mark referral as converted
            PERFORM handle_referral_conversion(transaction_record.doctor_id);
            
            -- Create discount for referrer
            PERFORM create_referral_discount(
                transaction_record.referred_by,
                (SELECT id FROM referral_analytics WHERE referred_doctor_id = transaction_record.doctor_id LIMIT 1),
                transaction_record.final_amount
            );
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Add foreign key constraint for current_plan_id
ALTER TABLE doctors ADD CONSTRAINT doctors_current_plan_id_fkey 
    FOREIGN KEY (current_plan_id) REFERENCES billing_plans(id);

-- Insert default billing plans
INSERT INTO billing_plans (name, description, monthly_price, quota_limit, features) VALUES
('Starter', 'Perfect for small clinics', 999.00, 50, '{"features": ["50 AI Consultations", "Basic Templates", "Email Support"]}'),
('Professional', 'For growing practices', 1999.00, 150, '{"features": ["150 AI Consultations", "Advanced Templates", "Priority Support", "Analytics"]}'),
('Enterprise', 'For large healthcare facilities', 3999.00, 500, '{"features": ["500 AI Consultations", "Custom Templates", "24/7 Support", "Advanced Analytics", "API Access"]}');

-- Function to update doctor quota (admin only)
CREATE OR REPLACE FUNCTION update_doctor_quota(doctor_uuid UUID, new_quota INT, admin_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    old_quota INT;
BEGIN
    -- Get current quota
    SELECT monthly_quota INTO old_quota
    FROM doctors
    WHERE id = doctor_uuid;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Update quota
    UPDATE doctors
    SET monthly_quota = new_quota
    WHERE id = doctor_uuid;

    -- Log quota update
    INSERT INTO usage_logs (doctor_id, action_type, quota_before, quota_after, metadata)
    VALUES (doctor_uuid, 'quota_update', old_quota, new_quota,
            ('{"admin_id": "' || admin_uuid || '", "timestamp": "' || NOW() || '"}')::jsonb);

    RETURN TRUE;
END;
$$ language 'plpgsql';
