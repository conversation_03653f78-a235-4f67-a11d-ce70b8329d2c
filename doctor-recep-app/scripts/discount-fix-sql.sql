-- Fix Referral Discount Calculation to 20%
-- Run this SQL directly in Supabase SQL Editor

-- 1. Update create_referral_discount function to use 20%
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount (updated from 10%)
    discount_amount := referred_amount * 0.20;
    
    INSERT INTO referral_discounts (
        doctor_id,
        referral_analytics_id,
        discount_amount,
        original_amount,
        valid_until,
        discount_percentage
    ) VALUES (
        referrer_doctor_id,
        referral_analytics_id,
        discount_amount,
        referred_amount,
        NOW() + INTERVAL '12 months',
        20.00
    ) RETURNING id INTO discount_id;
    
    UPDATE doctors 
    SET available_discount_amount = available_discount_amount + discount_amount,
        referral_discount_earned = referral_discount_earned + discount_amount
    WHERE id = referrer_doctor_id;
    
    RETURN discount_id;
END;
$$ LANGUAGE plpgsql;

-- 2. Update handle_referral_conversion function to use 20%
CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    referrer_uuid UUID;
    discount_amount DECIMAL(10,2);
    referred_amount DECIMAL(10,2);
BEGIN
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
    
    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Get the last bill amount of the referred doctor
    SELECT final_amount INTO referred_amount
    FROM billing_transactions 
    WHERE doctor_id = referred_doctor_uuid 
      AND payment_status = 'paid'
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- Calculate 20% discount of referee's last bill amount
    discount_amount := COALESCE(referred_amount * 0.20, 20.00);
    
    UPDATE doctors
    SET conversion_date = NOW()
    WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
    
    UPDATE referral_analytics
    SET status = 'converted',
        conversion_date = NOW(),
        discount_earned = discount_amount
    WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
    
    UPDATE doctors
    SET successful_referrals = successful_referrals + 1,
        referral_discount_earned = referral_discount_earned + discount_amount,
        available_discount_amount = available_discount_amount + discount_amount
    WHERE id = referrer_uuid;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 3. Update complete_payment function to work with 20% calculation
CREATE OR REPLACE FUNCTION complete_payment(transaction_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
    referral_analytics_id UUID;
BEGIN
    SELECT bt.*, d.referred_by INTO transaction_record
    FROM billing_transactions bt
    JOIN doctors d ON bt.doctor_id = d.id
    WHERE bt.id = transaction_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    UPDATE billing_transactions
    SET payment_status = 'paid',
        payment_date = NOW(),
        updated_at = NOW()
    WHERE id = transaction_id;
    
    UPDATE doctors
    SET billing_status = 'active',
        last_payment_date = NOW(),
        next_billing_date = transaction_record.billing_period_end
    WHERE id = transaction_record.doctor_id;
    
    -- Handle referral conversion if this is a referred doctor's first payment
    IF transaction_record.referred_by IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM billing_transactions 
            WHERE doctor_id = transaction_record.doctor_id 
            AND payment_status = 'paid' 
            AND id != transaction_id
        ) THEN
            SELECT id INTO referral_analytics_id 
            FROM referral_analytics 
            WHERE referred_doctor_id = transaction_record.doctor_id 
            AND status = 'pending'
            LIMIT 1;
            
            PERFORM handle_referral_conversion(transaction_record.doctor_id);
            
            IF referral_analytics_id IS NOT NULL THEN
                PERFORM create_referral_discount(
                    transaction_record.referred_by,
                    referral_analytics_id,
                    transaction_record.final_amount
                );
            END IF;
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 4. Update default discount percentage in referral_discounts table
ALTER TABLE referral_discounts 
ALTER COLUMN discount_percentage SET DEFAULT 20.00;

-- 5. Verification query to check functions are updated
DO $$
BEGIN
    RAISE NOTICE 'Discount calculation updated to 20%!';
    RAISE NOTICE 'Functions updated:';
    RAISE NOTICE '- create_referral_discount: 20% of referee last bill amount';
    RAISE NOTICE '- handle_referral_conversion: 20% discount calculation';
    RAISE NOTICE '- complete_payment: Updated with new discount logic';
    RAISE NOTICE 'Default discount percentage set to 20%';
END $$;