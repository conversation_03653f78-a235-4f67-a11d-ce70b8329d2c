#!/usr/bin/env node

/**
 * Test script to verify Cloudflare R2 connection and configuration
 */

const { S3Client, ListObjectsV2Command, PutObjectCommand } = require('@aws-sdk/client-s3');
require('dotenv').config({ path: '.env.local' });

// R2 Configuration
const R2_CONFIG = {
  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',
  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',
  ACCESS_KEY_ID: process.env.R2_ACCESS_KEY_ID || 'f4602d83fa66b1d9e0a8179230274235',
  SECRET_ACCESS_KEY: process.env.R2_SECRET_ACCESS_KEY || 'e7442c01067ed9a42a424c150040131e79879dbe5b17dc6858dd461e76f5bfde',
  ACCOUNT_ID: process.env.R2_ACCOUNT_ID || 'your-account-id'
};

// Create R2 Client
const createR2Client = () => {
  return new S3Client({
    region: 'auto',
    endpoint: `https://${R2_CONFIG.ACCOUNT_ID}.r2.cloudflarestorage.com`,
    credentials: {
      accessKeyId: R2_CONFIG.ACCESS_KEY_ID,
      secretAccessKey: R2_CONFIG.SECRET_ACCESS_KEY,
    },
  });
};

async function testR2Connection() {
  console.log('🧪 Testing Cloudflare R2 Connection...\n');
  
  console.log('📋 Configuration:');
  console.log(`   Bucket: ${R2_CONFIG.BUCKET_NAME}`);
  console.log(`   Public URL: ${R2_CONFIG.PUBLIC_URL}`);
  console.log(`   Access Key: ${R2_CONFIG.ACCESS_KEY_ID.substring(0, 8)}...`);
  console.log(`   Account ID: ${R2_CONFIG.ACCOUNT_ID}\n`);

  try {
    const r2Client = createR2Client();

    // Test 1: List objects in bucket
    console.log('🔍 Test 1: Listing bucket contents...');
    const listCommand = new ListObjectsV2Command({
      Bucket: R2_CONFIG.BUCKET_NAME,
      MaxKeys: 10
    });

    const listResult = await r2Client.send(listCommand);
    console.log(`✅ Success! Found ${listResult.Contents?.length || 0} objects in bucket`);
    
    if (listResult.Contents && listResult.Contents.length > 0) {
      console.log('   Sample objects:');
      listResult.Contents.slice(0, 3).forEach(obj => {
        console.log(`   - ${obj.Key} (${obj.Size} bytes)`);
      });
    }

    // Test 2: Upload a test file
    console.log('\n📤 Test 2: Uploading test file...');
    const testContent = `Test file uploaded at ${new Date().toISOString()}`;
    const testKey = 'test/connection-test.txt';

    const uploadCommand = new PutObjectCommand({
      Bucket: R2_CONFIG.BUCKET_NAME,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain',
    });

    await r2Client.send(uploadCommand);
    console.log(`✅ Success! Test file uploaded to: ${testKey}`);
    console.log(`   Public URL: ${R2_CONFIG.PUBLIC_URL}/${testKey}`);

    // Test 3: Verify folder structure
    console.log('\n📁 Test 3: Checking folder structure...');
    const audioPrefix = 'consultation-audio/';
    const imagePrefix = 'consultation-images/';

    const audioListCommand = new ListObjectsV2Command({
      Bucket: R2_CONFIG.BUCKET_NAME,
      Prefix: audioPrefix,
      MaxKeys: 5
    });

    const imageListCommand = new ListObjectsV2Command({
      Bucket: R2_CONFIG.BUCKET_NAME,
      Prefix: imagePrefix,
      MaxKeys: 5
    });

    const [audioResult, imageResult] = await Promise.all([
      r2Client.send(audioListCommand),
      r2Client.send(imageListCommand)
    ]);

    console.log(`   Audio files: ${audioResult.Contents?.length || 0} found`);
    console.log(`   Image files: ${imageResult.Contents?.length || 0} found`);

    console.log('\n🎉 All tests passed! R2 connection is working correctly.');
    
    return true;
  } catch (error) {
    console.error('\n❌ R2 Connection Test Failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    
    if (error.message.includes('InvalidAccessKeyId')) {
      console.error('\n💡 Suggestion: Check your R2_ACCESS_KEY_ID');
    } else if (error.message.includes('SignatureDoesNotMatch')) {
      console.error('\n💡 Suggestion: Check your R2_SECRET_ACCESS_KEY');
    } else if (error.message.includes('NoSuchBucket')) {
      console.error('\n💡 Suggestion: Verify bucket name "celerai-storage" exists');
    } else if (error.message.includes('getaddrinfo ENOTFOUND')) {
      console.error('\n💡 Suggestion: Check your R2_ACCOUNT_ID');
    }
    
    return false;
  }
}

// Run the test
if (require.main === module) {
  testR2Connection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testR2Connection };
