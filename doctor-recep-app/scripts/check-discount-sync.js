const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkDiscountSync() {
  console.log('🔍 Checking discount synchronization between tables...\n');

  try {
    // 1. Get all applied discounts and see which doctors should have available amounts
    const { data: appliedDiscounts } = await supabase
      .from('referral_discounts')
      .select('id, doctor_id, discount_amount, status, applied_at, applied_to_transaction_id')
      .eq('status', 'applied');

    console.log('📊 APPLIED DISCOUNTS:');
    console.log(`Found ${appliedDiscounts?.length || 0} applied discounts\n`);

    if (!appliedDiscounts || appliedDiscounts.length === 0) {
      console.log('No applied discounts found. Checking pending/other statuses...\n');
      
      const { data: allDiscounts } = await supabase
        .from('referral_discounts')
        .select('id, doctor_id, discount_amount, status');
      
      if (allDiscounts && allDiscounts.length > 0) {
        const statusGroups = allDiscounts.reduce((groups, d) => {
          const status = d.status || 'null';
          groups[status] = groups[status] || [];
          groups[status].push(d);
          return groups;
        }, {});
        
        Object.entries(statusGroups).forEach(([status, discounts]) => {
          const total = discounts.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
          console.log(`${status.toUpperCase()}: ${discounts.length} records, ₹${total.toFixed(2)}`);
        });
      }
      return;
    }

    // 2. For each applied discount, check if the doctor's available_discount_amount reflects it
    for (const discount of appliedDiscounts) {
      console.log(`🔍 Checking discount ${discount.id.substring(0, 8)}...:`);
      console.log(`   Amount: ₹${discount.discount_amount}`);
      console.log(`   Applied to doctor: ${discount.doctor_id.substring(0, 8)}...`);
      console.log(`   Applied at: ${discount.applied_at}`);
      
      // Get the doctor's current available_discount_amount
      const { data: doctor } = await supabase
        .from('doctors')
        .select('id, name, available_discount_amount, referral_discount_earned')
        .eq('id', discount.doctor_id)
        .single();

      if (doctor) {
        console.log(`   Doctor: ${doctor.name}`);
        console.log(`   Available discount: ₹${doctor.available_discount_amount || 0}`);
        console.log(`   Total earned: ₹${doctor.referral_discount_earned || 0}`);
        
        // Check if this discount has been used/deducted
        if (discount.applied_to_transaction_id) {
          const { data: transaction } = await supabase
            .from('billing_transactions')
            .select('id, discount_amount, final_amount, amount')
            .eq('id', discount.applied_to_transaction_id)
            .single();
          
          if (transaction) {
            console.log(`   Applied to transaction: ₹${transaction.amount} - ₹${transaction.discount_amount} = ₹${transaction.final_amount}`);
            
            // If discount was used in transaction, available_discount_amount should be reduced
            if (transaction.discount_amount > 0) {
              console.log(`   ⚠️  Discount was USED in transaction, available amount should be reduced`);
            } else {
              console.log(`   💰 Discount not yet used in transaction`);
            }
          }
        } else {
          console.log(`   💰 Discount not yet applied to any transaction`);
        }
      } else {
        console.log(`   ❌ Doctor not found!`);
      }
      console.log('');
    }

    // 3. Calculate what each doctor's available_discount_amount SHOULD be
    console.log('🧮 EXPECTED vs ACTUAL AVAILABLE DISCOUNTS:');
    console.log('============================================');

    // Group applied discounts by doctor
    const discountsByDoctor = appliedDiscounts.reduce((groups, discount) => {
      const doctorId = discount.doctor_id;
      groups[doctorId] = groups[doctorId] || [];
      groups[doctorId].push(discount);
      return groups;
    }, {});

    for (const [doctorId, doctorDiscounts] of Object.entries(discountsByDoctor)) {
      const { data: doctor } = await supabase
        .from('doctors')
        .select('name, available_discount_amount, referral_discount_earned')
        .eq('id', doctorId)
        .single();

      if (doctor) {
        // Calculate total earned discounts
        const totalEarned = doctorDiscounts.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
        
        // Calculate total used discounts (where applied_to_transaction_id exists and transaction has discount_amount)
        let totalUsed = 0;
        for (const discount of doctorDiscounts) {
          if (discount.applied_to_transaction_id) {
            const { data: transaction } = await supabase
              .from('billing_transactions')
              .select('discount_amount')
              .eq('id', discount.applied_to_transaction_id)
              .single();
            
            if (transaction && transaction.discount_amount > 0) {
              totalUsed += parseFloat(transaction.discount_amount);
            }
          }
        }
        
        const expectedAvailable = totalEarned - totalUsed;
        const actualAvailable = parseFloat(doctor.available_discount_amount || 0);
        
        console.log(`👨‍⚕️ ${doctor.name}:`);
        console.log(`   Total earned: ₹${totalEarned.toFixed(2)}`);
        console.log(`   Total used: ₹${totalUsed.toFixed(2)}`);
        console.log(`   Expected available: ₹${expectedAvailable.toFixed(2)}`);
        console.log(`   Actual available: ₹${actualAvailable.toFixed(2)}`);
        
        const difference = Math.abs(expectedAvailable - actualAvailable);
        if (difference > 0.01) {
          console.log(`   ❌ MISMATCH: ₹${difference.toFixed(2)} difference`);
        } else {
          console.log(`   ✅ CORRECT`);
        }
        console.log('');
      }
    }

    // 4. Check if getBillingStats calculation matches applied discounts
    console.log('📈 BILLING STATS VERIFICATION:');
    console.log('==============================');
    
    const totalAppliedDiscounts = appliedDiscounts.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
    console.log(`Total applied discounts: ₹${totalAppliedDiscounts.toFixed(2)}`);
    console.log('This should match "Total Discounts Given" in admin dashboard');

    // 5. Check for any orphaned records
    console.log('\n🔍 CHECKING FOR DATA ISSUES:');
    console.log('=============================');
    
    // Check for discounts pointing to non-existent doctors
    for (const discount of appliedDiscounts) {
      const { count } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('id', discount.doctor_id);
      
      if (count === 0) {
        console.log(`❌ Orphaned discount: ${discount.id} points to non-existent doctor ${discount.doctor_id}`);
      }
    }

    // Check for discounts pointing to non-existent transactions
    for (const discount of appliedDiscounts) {
      if (discount.applied_to_transaction_id) {
        const { count } = await supabase
          .from('billing_transactions')
          .select('*', { count: 'exact', head: true })
          .eq('id', discount.applied_to_transaction_id);
        
        if (count === 0) {
          console.log(`❌ Orphaned discount: ${discount.id} points to non-existent transaction ${discount.applied_to_transaction_id}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error checking discount sync:', error);
  }
}

if (require.main === module) {
  checkDiscountSync();
}

module.exports = { checkDiscountSync };