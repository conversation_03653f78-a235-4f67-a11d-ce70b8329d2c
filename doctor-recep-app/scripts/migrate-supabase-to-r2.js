#!/usr/bin/env node

/**
 * Migration script to copy files from Supabase Storage to Cloudflare R2
 * Maintains exact folder structure and updates database URLs
 */

const { createClient } = require('@supabase/supabase-js');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
require('dotenv').config({ path: '.env.local' });

// Configuration
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL,
  serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY
};

const R2_CONFIG = {
  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',
  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',
  ACCESS_KEY_ID: process.env.R2_ACCESS_KEY_ID,
  SECRET_ACCESS_KEY: process.env.R2_SECRET_ACCESS_KEY,
  ACCOUNT_ID: process.env.R2_ACCOUNT_ID
};

// Create clients
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.serviceKey);

const createR2Client = () => {
  return new S3Client({
    region: 'auto',
    endpoint: `https://${R2_CONFIG.ACCOUNT_ID}.r2.cloudflarestorage.com`,
    credentials: {
      accessKeyId: R2_CONFIG.ACCESS_KEY_ID,
      secretAccessKey: R2_CONFIG.SECRET_ACCESS_KEY,
    },
  });
};

// Helper functions
function extractFilePathFromSupabaseUrl(url, bucket) {
  const bucketPath = `/storage/v1/object/public/${bucket}/`;
  const index = url.indexOf(bucketPath);
  if (index === -1) return null;
  return url.substring(index + bucketPath.length);
}

function generateR2Path(supabasePath, type) {
  const prefix = type === 'audio' ? 'consultation-audio' : 'consultation-images';
  return `${prefix}/${supabasePath}`;
}

function generateR2Url(r2Path) {
  return `${R2_CONFIG.PUBLIC_URL}/${r2Path}`;
}

async function downloadFromSupabase(bucket, filePath) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .download(filePath);

    if (error) {
      throw new Error(`Supabase download error: ${error.message}`);
    }

    return data;
  } catch (error) {
    throw new Error(`Failed to download from Supabase: ${error.message}`);
  }
}

async function uploadToR2(r2Path, fileData, contentType) {
  try {
    const r2Client = createR2Client();
    
    const uploadCommand = new PutObjectCommand({
      Bucket: R2_CONFIG.BUCKET_NAME,
      Key: r2Path,
      Body: new Uint8Array(await fileData.arrayBuffer()),
      ContentType: contentType,
      CacheControl: 'public, max-age=3600',
    });

    await r2Client.send(uploadCommand);
    return generateR2Url(r2Path);
  } catch (error) {
    throw new Error(`Failed to upload to R2: ${error.message}`);
  }
}

async function migrateFile(url, type) {
  try {
    console.log(`   📄 Migrating: ${url}`);
    
    // Extract file path from Supabase URL
    const bucket = type === 'audio' ? 'consultation-audio' : 'consultation-images';
    const supabasePath = extractFilePathFromSupabaseUrl(url, bucket);
    
    if (!supabasePath) {
      throw new Error('Could not extract file path from URL');
    }

    // Download from Supabase
    const fileData = await downloadFromSupabase(bucket, supabasePath);
    
    // Generate R2 path
    const r2Path = generateR2Path(supabasePath, type);
    
    // Determine content type
    const extension = supabasePath.split('.').pop().toLowerCase();
    let contentType = 'application/octet-stream';
    
    if (type === 'audio') {
      const audioTypes = {
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'webm': 'audio/webm',
        'm4a': 'audio/mp4',
        'ogg': 'audio/ogg'
      };
      contentType = audioTypes[extension] || 'audio/mpeg';
    } else {
      const imageTypes = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'webp': 'image/webp',
        'heic': 'image/heic'
      };
      contentType = imageTypes[extension] || 'image/jpeg';
    }

    // Upload to R2
    const r2Url = await uploadToR2(r2Path, fileData, contentType);
    
    console.log(`   ✅ Migrated to: ${r2Url}`);
    return r2Url;
  } catch (error) {
    console.error(`   ❌ Failed to migrate ${url}: ${error.message}`);
    return null;
  }
}

async function updateConsultationUrls(consultationId, updates) {
  try {
    const { error } = await supabase
      .from('consultations')
      .update(updates)
      .eq('id', consultationId);

    if (error) {
      throw new Error(`Database update error: ${error.message}`);
    }

    console.log(`   ✅ Updated database for consultation: ${consultationId}`);
  } catch (error) {
    console.error(`   ❌ Failed to update database: ${error.message}`);
  }
}

async function migrateConsultations() {
  console.log('🔍 Fetching consultations from database...');
  
  const { data: consultations, error } = await supabase
    .from('consultations')
    .select('id, primary_audio_url, additional_audio_urls, image_urls')
    .not('primary_audio_url', 'is', null);

  if (error) {
    throw new Error(`Failed to fetch consultations: ${error.message}`);
  }

  console.log(`📊 Found ${consultations.length} consultations to migrate\n`);

  let migrated = 0;
  let failed = 0;

  for (const consultation of consultations) {
    console.log(`🔄 Processing consultation: ${consultation.id}`);
    
    const updates = {};
    let consultationSuccess = true;

    try {
      // Migrate primary audio
      if (consultation.primary_audio_url) {
        const newUrl = await migrateFile(consultation.primary_audio_url, 'audio');
        if (newUrl) {
          updates.primary_audio_url = newUrl;
        } else {
          consultationSuccess = false;
        }
      }

      // Migrate additional audio files
      if (consultation.additional_audio_urls && consultation.additional_audio_urls.length > 0) {
        const newUrls = [];
        for (const url of consultation.additional_audio_urls) {
          const newUrl = await migrateFile(url, 'audio');
          if (newUrl) {
            newUrls.push(newUrl);
          } else {
            consultationSuccess = false;
          }
        }
        if (newUrls.length > 0) {
          updates.additional_audio_urls = newUrls;
        }
      }

      // Migrate image files
      if (consultation.image_urls && consultation.image_urls.length > 0) {
        const newUrls = [];
        for (const url of consultation.image_urls) {
          const newUrl = await migrateFile(url, 'image');
          if (newUrl) {
            newUrls.push(newUrl);
          } else {
            consultationSuccess = false;
          }
        }
        if (newUrls.length > 0) {
          updates.image_urls = newUrls;
        }
      }

      // Update database if we have any successful migrations
      if (Object.keys(updates).length > 0) {
        await updateConsultationUrls(consultation.id, updates);
      }

      if (consultationSuccess) {
        migrated++;
        console.log(`   🎉 Consultation migrated successfully\n`);
      } else {
        failed++;
        console.log(`   ⚠️  Consultation partially migrated\n`);
      }

    } catch (error) {
      failed++;
      console.error(`   ❌ Consultation migration failed: ${error.message}\n`);
    }
  }

  return { total: consultations.length, migrated, failed };
}

async function main() {
  console.log('🚀 Starting Supabase to Cloudflare R2 Migration...\n');
  
  console.log('📋 Configuration:');
  console.log(`   Supabase URL: ${SUPABASE_CONFIG.url}`);
  console.log(`   R2 Bucket: ${R2_CONFIG.BUCKET_NAME}`);
  console.log(`   R2 Public URL: ${R2_CONFIG.PUBLIC_URL}`);
  console.log(`   R2 Account ID: ${R2_CONFIG.ACCOUNT_ID}\n`);

  try {
    const results = await migrateConsultations();
    
    console.log('=' .repeat(60));
    console.log('🎯 MIGRATION SUMMARY:');
    console.log(`   Total consultations: ${results.total}`);
    console.log(`   Successfully migrated: ${results.migrated}`);
    console.log(`   Failed migrations: ${results.failed}`);
    console.log(`   Success rate: ${((results.migrated / results.total) * 100).toFixed(1)}%`);
    console.log('=' .repeat(60));

    if (results.failed === 0) {
      console.log('🎉 ALL MIGRATIONS COMPLETED SUCCESSFULLY!');
      console.log('\n📋 Next Steps:');
      console.log('   1. Test file access via R2 URLs');
      console.log('   2. Test application functionality');
      console.log('   3. Verify AI processing works');
      console.log('   4. Clean up Supabase storage (optional)');
    } else {
      console.log('⚠️  SOME MIGRATIONS FAILED');
      console.log('   Please review the errors above and retry if needed');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { main };
