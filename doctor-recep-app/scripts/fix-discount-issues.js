const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixDiscountIssues() {
  console.log('🔧 Fixing discount calculation and status issues...\n');

  try {
    // 1. Check current pending discounts and their calculation
    console.log('📊 ANALYZING CURRENT DISCOUNT ISSUES:');
    console.log('=====================================');
    
    const { data: pendingDiscounts, error: pendingError } = await supabase
      .from('referral_discounts')
      .select('*')
      .eq('status', 'pending');

    if (pendingError) {
      console.error('❌ Error fetching pending discounts:', pendingError);
      return;
    }

    console.log(`📋 Found ${pendingDiscounts?.length || 0} pending discounts`);

    if (pendingDiscounts && pendingDiscounts.length > 0) {
      console.log('\n🔍 DISCOUNT ANALYSIS:');
      pendingDiscounts.forEach((discount, index) => {
        const expectedDiscount20 = parseFloat(discount.original_amount) * 0.20;
        const expectedDiscount10 = parseFloat(discount.original_amount) * 0.10;
        const actualDiscount = parseFloat(discount.discount_amount);
        
        console.log(`${index + 1}. ID: ${discount.id}`);
        console.log(`   Original: ₹${discount.original_amount}`);
        console.log(`   Current: ₹${discount.discount_amount} (${discount.discount_percentage}%)`);
        console.log(`   Expected 20%: ₹${expectedDiscount20.toFixed(2)}`);
        console.log(`   Expected 10%: ₹${expectedDiscount10.toFixed(2)}`);
        
        if (Math.abs(actualDiscount - expectedDiscount10) < 0.01) {
          console.log('   🎯 This is calculated at 10% - NEEDS FIXING');
        } else if (Math.abs(actualDiscount - expectedDiscount20) < 0.01) {
          console.log('   ✅ This is correctly calculated at 20%');
        } else {
          console.log('   ❓ Unknown calculation method');
        }
        console.log('');
      });
    }

    // 2. Fix discounts calculated at 10% to be 20%
    console.log('💰 FIXING DISCOUNT CALCULATIONS:');
    console.log('=================================');
    
    if (pendingDiscounts && pendingDiscounts.length > 0) {
      for (const discount of pendingDiscounts) {
        const originalAmount = parseFloat(discount.original_amount);
        const currentAmount = parseFloat(discount.discount_amount);
        const expectedAmount20 = originalAmount * 0.20;
        const expectedAmount10 = originalAmount * 0.10;
        
        // Check if this discount was calculated at 10%
        if (Math.abs(currentAmount - expectedAmount10) < 0.01) {
          console.log(`🔧 Updating discount ${discount.id.substring(0, 8)}... from ₹${currentAmount} to ₹${expectedAmount20.toFixed(2)}`);
          
          const { error: updateError } = await supabase
            .from('referral_discounts')
            .update({
              discount_amount: expectedAmount20,
              discount_percentage: 20.00,
              updated_at: new Date().toISOString()
            })
            .eq('id', discount.id);
          
          if (updateError) {
            console.error(`❌ Failed to update discount ${discount.id}:`, updateError);
          } else {
            console.log(`✅ Updated discount ${discount.id.substring(0, 8)}... successfully`);
          }
        } else {
          console.log(`✅ Discount ${discount.id.substring(0, 8)}... already correctly calculated`);
        }
      }
    }

    // 3. Check if discounts should be auto-applied
    console.log('\n🎯 CHECKING DISCOUNT APPLICATION STATUS:');
    console.log('========================================');
    
    const { data: updatedDiscounts } = await supabase
      .from('referral_discounts')
      .select('*, referral_analytics!inner(*)')
      .eq('status', 'pending');

    if (updatedDiscounts && updatedDiscounts.length > 0) {
      for (const discount of updatedDiscounts) {
        // Check if the referral has been converted
        const referralAnalytics = discount.referral_analytics;
        
        console.log(`🔍 Discount ${discount.id.substring(0, 8)}...:`);
        console.log(`   Referral status: ${referralAnalytics?.status || 'unknown'}`);
        console.log(`   Valid until: ${discount.valid_until}`);
        console.log(`   Created: ${discount.created_at}`);
        
        // Check if referral is converted and discount should be applied
        if (referralAnalytics?.status === 'converted') {
          console.log(`   🎯 Referral is converted - checking if discount should be applied`);
          
          // Check if there's a related paid transaction
          const { data: relatedTransaction } = await supabase
            .from('billing_transactions')
            .select('*')
            .eq('doctor_id', referralAnalytics.referred_doctor_id)
            .eq('payment_status', 'paid')
            .limit(1);
          
          if (relatedTransaction && relatedTransaction.length > 0) {
            console.log(`   💳 Found paid transaction - discount should be applied`);
            
            // Apply the discount
            const { error: applyError } = await supabase
              .from('referral_discounts')
              .update({
                status: 'applied',
                applied_at: new Date().toISOString(),
                applied_to_transaction_id: relatedTransaction[0].id
              })
              .eq('id', discount.id);
            
            if (applyError) {
              console.error(`   ❌ Failed to apply discount:`, applyError);
            } else {
              console.log(`   ✅ Applied discount successfully`);
            }
          } else {
            console.log(`   ⏳ No paid transaction found - keeping as pending`);
          }
        } else {
          console.log(`   ⏳ Referral not converted yet - keeping as pending`);
        }
        console.log('');
      }
    }

    // 4. Final verification
    console.log('📊 FINAL VERIFICATION:');
    console.log('======================');
    
    const { data: finalDiscounts } = await supabase
      .from('referral_discounts')
      .select('discount_amount, status');
    
    if (finalDiscounts) {
      const appliedTotal = finalDiscounts
        .filter(d => d.status === 'applied')
        .reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
      
      const pendingTotal = finalDiscounts
        .filter(d => d.status === 'pending')
        .reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
      
      console.log(`✅ Applied discounts: ₹${appliedTotal.toFixed(2)}`);
      console.log(`⏳ Pending discounts: ₹${pendingTotal.toFixed(2)}`);
      console.log(`📈 Total discount records: ${finalDiscounts.length}`);
      
      console.log('\n🎯 WHAT THIS MEANS FOR "Total Discounts Given":');
      console.log(`   Current display value: ₹${appliedTotal.toFixed(2)}`);
      console.log(`   This should now show in the admin dashboard`);
    }

    console.log('\n✅ DISCOUNT FIXES COMPLETE');
    console.log('===========================');
    console.log('The "Total Discounts Given" should now show the correct value.');
    console.log('Refresh the admin dashboard to see the updated amounts.');

  } catch (error) {
    console.error('❌ Error fixing discount issues:', error);
  }
}

if (require.main === module) {
  fixDiscountIssues().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { fixDiscountIssues };