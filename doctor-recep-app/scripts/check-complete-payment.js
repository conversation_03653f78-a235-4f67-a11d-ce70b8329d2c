#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check the current complete_payment function implementation
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

async function checkCompletePaymentFunction() {
  try {
    console.log('🔍 Checking complete_payment Function Implementation');
    console.log('=================================================\n');

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing environment variables');
      console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
      return;
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check function definition
    console.log('📋 Getting function definition...');
    try {
      const { data, error } = await supabase
        .from('pg_proc')
        .select('proname, prosrc')
        .eq('proname', 'complete_payment');

      if (error) {
        console.log('ℹ️ Cannot query pg_proc (expected with anon key)');
        console.log('   Using service role key to check...');
      } else if (data && data.length > 0) {
        console.log('✅ Found complete_payment function');
        console.log('📝 Function source:');
        console.log(data[0].prosrc);
      }
    } catch (err) {
      console.log('ℹ️ Direct function query failed:', err.message);
    }

    // Test with actual billing transaction if exists
    console.log('\n🧪 Looking for test billing transaction...');
    
    const { data: transactions, error: transError } = await supabase
      .from('billing_transactions')
      .select('id, payment_status, doctor_id')
      .eq('payment_status', 'pending')
      .limit(1);

    if (transError) {
      console.log('❌ Error fetching transactions:', transError.message);
      return;
    }

    if (transactions && transactions.length > 0) {
      const testTransaction = transactions[0];
      console.log(`✅ Found test transaction: ${testTransaction.id}`);
      console.log(`   Doctor ID: ${testTransaction.doctor_id}`);
      console.log(`   Status: ${testTransaction.payment_status}`);

      // Check if doctor has referral setup
      const { data: doctor, error: docError } = await supabase
        .from('doctors')
        .select('referred_by, referral_code')
        .eq('id', testTransaction.doctor_id)
        .single();

      if (docError) {
        console.log('❌ Error fetching doctor:', docError.message);
      } else {
        console.log(`   Referred by: ${doctor.referred_by || 'None'}`);
        console.log(`   Referral code: ${doctor.referral_code || 'None'}`);

        if (doctor.referred_by) {
          console.log('🎯 This doctor was referred - payment completion should trigger referral conversion');
          
          // Check referral analytics
          const { data: analytics, error: analyticsError } = await supabase
            .from('referral_analytics')
            .select('*')
            .eq('referred_doctor_id', testTransaction.doctor_id);

          if (analyticsError) {
            console.log('❌ Error fetching referral analytics:', analyticsError.message);
          } else {
            console.log(`   Referral analytics records: ${analytics?.length || 0}`);
            if (analytics && analytics.length > 0) {
              analytics.forEach(record => {
                console.log(`     - Status: ${record.status}, Created: ${record.created_at}`);
              });
            }
          }
        }
      }

      console.log('\n⚠️  WARNING: Do not run complete_payment test on production data!');
      console.log('   To test manually, use: ');
      console.log(`   SELECT complete_payment('${testTransaction.id}');`);
    } else {
      console.log('ℹ️ No pending transactions found for testing');
    }

    // Check for missing functions that might be called
    console.log('\n🔍 Checking for referenced functions...');
    
    const functionsToCheck = [
      'handle_referral_conversion',
      'create_referral_discount',
      'apply_referral_discount'
    ];

    for (const funcName of functionsToCheck) {
      try {
        const { error } = await supabase.rpc(funcName, {});
        if (error && error.message.includes('function')) {
          console.log(`❌ ${funcName} function missing`);
        } else {
          console.log(`✅ ${funcName} function exists`);
        }
      } catch (err) {
        if (err.message.includes('function')) {
          console.log(`❌ ${funcName} function missing`);
        } else {
          console.log(`✅ ${funcName} function exists (error: ${err.message})`);
        }
      }
    }

    console.log('\n🎯 Complete payment function check completed!');

  } catch (error) {
    console.error('💥 Fatal error during check:', error);
  }
}

// Run the check
checkCompletePaymentFunction().catch(console.error);