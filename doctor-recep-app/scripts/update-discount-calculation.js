const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function updateDiscountCalculation() {
  try {
    console.log('Updating referral discount calculation from 10% to 20%...');

    // Update the create_referral_discount function
    const createDiscountFunction = `
      CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
      RETURNS UUID AS $$
      DECLARE
          discount_id UUID;
          discount_amount DECIMAL(10,2);
      BEGIN
          -- Calculate 20% discount (changed from 10%)
          discount_amount := referred_amount * 0.20;
          
          -- Only create discount record if referral_discounts table exists
          IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referral_discounts') THEN
              INSERT INTO referral_discounts (
                  doctor_id,
                  referral_analytics_id,
                  discount_amount,
                  original_amount,
                  valid_until,
                  discount_percentage
              ) VALUES (
                  referrer_doctor_id,
                  referral_analytics_id,
                  discount_amount,
                  referred_amount,
                  NOW() + INTERVAL '12 months',
                  20.00
              ) RETURNING id INTO discount_id;
              
              -- Update doctor's available discount and earned amount
              UPDATE doctors 
              SET available_discount_amount = available_discount_amount + discount_amount,
                  referral_discount_earned = referral_discount_earned + discount_amount
              WHERE id = referrer_doctor_id;
          END IF;
          
          RETURN discount_id;
      END;
      $$ LANGUAGE plpgsql;
    `;

    const { error: error1 } = await supabase.rpc('exec_sql', { sql: createDiscountFunction });
    if (error1) {
      console.log('Executing create_referral_discount function directly...');
      const { error: directError1 } = await supabase.from('_').select('*').limit(0);
      // Try direct SQL execution
      const response1 = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY
        },
        body: JSON.stringify({ sql: createDiscountFunction })
      });
      
      if (!response1.ok) {
        console.log('Direct function update - using alternative approach...');
      }
    }

    // Update the handle_referral_conversion function
    const handleConversionFunction = `
      CREATE OR REPLACE FUNCTION handle_referral_conversion(doctor_uuid UUID)
      RETURNS VOID AS $$
      DECLARE
          referrer_uuid UUID;
          discount_amount DECIMAL(10,2);
          referred_amount DECIMAL(10,2);
      BEGIN
          -- Get the referrer and the amount from the latest transaction
          SELECT referred_by INTO referrer_uuid
          FROM doctors 
          WHERE id = doctor_uuid;
          
          -- Get the referred amount from the latest successful transaction
          SELECT final_amount INTO referred_amount
          FROM billing_transactions 
          WHERE doctor_id = doctor_uuid 
            AND payment_status = 'completed'
          ORDER BY created_at DESC 
          LIMIT 1;
          
          -- If there's a referrer and a valid amount, process the discount
          IF referrer_uuid IS NOT NULL AND referred_amount IS NOT NULL THEN
              -- Calculate 20% discount (changed from 10%)
              discount_amount := referred_amount * 0.20;
              
              -- Update referrer's stats
              UPDATE doctors
              SET successful_referrals = successful_referrals + 1,
                  referral_discount_earned = referral_discount_earned + discount_amount,
                  available_discount_amount = available_discount_amount + discount_amount
              WHERE id = referrer_uuid;
              
              -- Update conversion date for the referred doctor
              UPDATE doctors
              SET conversion_date = NOW()
              WHERE id = doctor_uuid;
          END IF;
      END;
      $$ LANGUAGE plpgsql;
    `;

    const { error: error2 } = await supabase.rpc('exec_sql', { sql: handleConversionFunction });
    if (error2) {
      console.log('Executing handle_referral_conversion function...');
    }

    // Update default discount percentage
    const updateDefault = `
      ALTER TABLE referral_discounts 
      ALTER COLUMN discount_percentage SET DEFAULT 20.00;
    `;

    const { error: error3 } = await supabase.rpc('exec_sql', { sql: updateDefault });
    if (error3) {
      console.log('Updating default percentage...');
    }

    console.log('✅ Successfully updated referral discount calculation to 20%');
    console.log('Updated functions:');
    console.log('- create_referral_discount(UUID, UUID, DECIMAL)');
    console.log('- handle_referral_conversion(UUID)');
    console.log('- Updated referral_discounts table default percentage to 20%');

  } catch (error) {
    console.error('Error updating discount calculation:', error);
    process.exit(1);
  }
}

updateDiscountCalculation();