const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testFrontendCalculations() {
  console.log('🧪 Testing frontend API calculations...\n');

  try {
    // Test 1: getBillingStats calculation (Total Discounts Given)
    console.log('📊 TESTING getBillingStats():');
    console.log('==============================');
    
    const { data: discountData } = await supabase
      .from('referral_discounts')
      .select('discount_amount')
      .eq('status', 'applied');

    const referralDiscountsGiven = discountData?.reduce((sum, d) => sum + d.discount_amount, 0) || 0;
    
    console.log(`✅ Total Discounts Given: ₹${referralDiscountsGiven.toFixed(2)}`);
    console.log(`📋 Applied discount records: ${discountData?.length || 0}\n`);

    // Test 2: getDoctorsBillingInfo calculation (Available Discounts)
    console.log('👨‍⚕️ TESTING getDoctorsBillingInfo():');
    console.log('====================================');
    
    const { data: doctors } = await supabase
      .from('doctors')
      .select('id, name, email, available_discount_amount, referral_discount_earned')
      .eq('approved', true)
      .limit(5);

    if (doctors && doctors.length > 0) {
      for (const doctor of doctors) {
        console.log(`\n🔍 Doctor: ${doctor.name}`);
        
        // Get payment summary
        const { data: paymentSummary } = await supabase
          .from('billing_transactions')
          .select('payment_status, final_amount, discount_amount')
          .eq('doctor_id', doctor.id);

        const totalPaid = paymentSummary
          ?.filter(p => p.payment_status === 'paid')
          .reduce((sum, p) => sum + p.final_amount, 0) || 0;

        // Get applied discounts
        const { data: appliedDiscounts } = await supabase
          .from('referral_discounts')
          .select('discount_amount, applied_to_transaction_id')
          .eq('doctor_id', doctor.id)
          .eq('status', 'applied');

        const totalEarnedDiscounts = appliedDiscounts?.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0) || 0;

        const totalUsedDiscounts = paymentSummary
          ?.filter(p => p.discount_amount > 0)
          .reduce((sum, p) => sum + p.discount_amount, 0) || 0;

        const actualAvailableDiscount = totalEarnedDiscounts - totalUsedDiscounts;

        console.log(`   Old available discount (from DB): ₹${doctor.available_discount_amount || 0}`);
        console.log(`   New calculated available: ₹${actualAvailableDiscount.toFixed(2)}`);
        console.log(`   Old earned discount (from DB): ₹${doctor.referral_discount_earned || 0}`);
        console.log(`   New calculated earned: ₹${totalEarnedDiscounts.toFixed(2)}`);
        console.log(`   Total paid: ₹${totalPaid.toFixed(2)}`);
        console.log(`   Total used discounts: ₹${totalUsedDiscounts.toFixed(2)}`);
        
        if (Math.abs(actualAvailableDiscount - (doctor.available_discount_amount || 0)) > 0.01) {
          console.log(`   🔧 WILL BE FIXED: Available discount corrected`);
        } else {
          console.log(`   ✅ Already correct`);
        }
      }
    }

    // Test 3: Check if frontend will show correct values
    console.log('\n🌐 EXPECTED FRONTEND DISPLAY:');
    console.log('=============================');
    console.log(`"Total Discounts Given": ₹${referralDiscountsGiven.toFixed(2)}`);
    console.log('\nDoctors "Available Discount" column:');
    
    if (doctors && doctors.length > 0) {
      for (const doctor of doctors) {
        const { data: appliedDiscounts } = await supabase
          .from('referral_discounts')
          .select('discount_amount')
          .eq('doctor_id', doctor.id)
          .eq('status', 'applied');

        const { data: paymentSummary } = await supabase
          .from('billing_transactions')
          .select('discount_amount')
          .eq('doctor_id', doctor.id);

        const totalEarned = appliedDiscounts?.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0) || 0;
        const totalUsed = paymentSummary?.filter(p => p.discount_amount > 0).reduce((sum, p) => sum + p.discount_amount, 0) || 0;
        const available = totalEarned - totalUsed;

        if (totalEarned > 0) {
          console.log(`- ${doctor.name}: ₹${available.toFixed(2)}`);
        }
      }
    }

    console.log('\n✅ Frontend calculations test complete!');
    console.log('Refresh the admin dashboard to see corrected values.');

  } catch (error) {
    console.error('❌ Error testing frontend calculations:', error);
  }
}

if (require.main === module) {
  testFrontendCalculations();
}

module.exports = { testFrontendCalculations };