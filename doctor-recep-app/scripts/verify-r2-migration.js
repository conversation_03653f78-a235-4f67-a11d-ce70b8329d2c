#!/usr/bin/env node

/**
 * Verification script for Cloudflare R2 migration
 * Checks all components are properly configured
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Cloudflare R2 Migration Setup...\n');

// Check 1: Environment Variables
function checkEnvironmentVariables() {
  console.log('1️⃣ Checking Environment Variables:');
  
  const envPath = path.join(__dirname, '..', '.env.local');
  if (!fs.existsSync(envPath)) {
    console.log('   ❌ .env.local file not found');
    return false;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'R2_ACCOUNT_ID',
    'R2_ACCESS_KEY_ID', 
    'R2_SECRET_ACCESS_KEY',
    'R2_BUCKET_NAME',
    'R2_PUBLIC_URL'
  ];

  let allPresent = true;
  requiredVars.forEach(varName => {
    if (envContent.includes(varName)) {
      console.log(`   ✅ ${varName}: Present`);
    } else {
      console.log(`   ❌ ${varName}: Missing`);
      allPresent = false;
    }
  });

  return allPresent;
}

// Check 2: Package Dependencies
function checkPackageDependencies() {
  console.log('\n2️⃣ Checking Package Dependencies:');
  
  const packagePath = path.join(__dirname, '..', 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('   ❌ package.json not found');
    return false;
  }

  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const requiredDeps = ['@aws-sdk/client-s3'];

  let allPresent = true;
  requiredDeps.forEach(dep => {
    if (packageContent.dependencies && packageContent.dependencies[dep]) {
      console.log(`   ✅ ${dep}: ${packageContent.dependencies[dep]}`);
    } else {
      console.log(`   ❌ ${dep}: Missing`);
      allPresent = false;
    }
  });

  return allPresent;
}

// Check 3: Storage Module
function checkStorageModule() {
  console.log('\n3️⃣ Checking Storage Module:');
  
  const storagePath = path.join(__dirname, '..', 'src', 'lib', 'storage.ts');
  if (!fs.existsSync(storagePath)) {
    console.log('   ❌ storage.ts not found');
    return false;
  }

  const storageContent = fs.readFileSync(storagePath, 'utf8');
  
  const checks = [
    { name: 'AWS SDK Import', pattern: '@aws-sdk/client-s3' },
    { name: 'R2 Client Creation', pattern: 'createR2Client' },
    { name: 'Folder Prefixes', pattern: 'AUDIO_PREFIX.*consultation-audio' },
    { name: 'Custom Domain URL', pattern: 'celerai.tallyup.pro' },
    { name: 'Bucket Name', pattern: 'celerai-storage' }
  ];

  let allPresent = true;
  checks.forEach(check => {
    if (storageContent.includes(check.pattern) || new RegExp(check.pattern).test(storageContent)) {
      console.log(`   ✅ ${check.name}: Configured`);
    } else {
      console.log(`   ❌ ${check.name}: Missing`);
      allPresent = false;
    }
  });

  return allPresent;
}

// Check 4: Next.js Configuration
function checkNextConfig() {
  console.log('\n4️⃣ Checking Next.js Configuration:');
  
  const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
  if (!fs.existsSync(nextConfigPath)) {
    console.log('   ❌ next.config.js not found');
    return false;
  }

  const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');
  
  if (nextConfigContent.includes('celerai.tallyup.pro')) {
    console.log('   ✅ R2 domain added to image optimization');
  } else {
    console.log('   ❌ R2 domain missing from image optimization');
    return false;
  }

  return true;
}

// Check 5: File Structure
function checkFileStructure() {
  console.log('\n5️⃣ Checking File Structure:');
  
  const requiredFiles = [
    'src/lib/storage.ts',
    'src/lib/r2-storage.ts',
    'src/lib/actions/consultations.ts'
  ];

  let allPresent = true;
  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${file}: Present`);
    } else {
      console.log(`   ❌ ${file}: Missing`);
      allPresent = false;
    }
  });

  return allPresent;
}

// Check 6: Migration Readiness
function checkMigrationReadiness() {
  console.log('\n6️⃣ Migration Readiness Summary:');
  
  const checks = [
    { name: 'Environment Variables', fn: checkEnvironmentVariables },
    { name: 'Package Dependencies', fn: checkPackageDependencies },
    { name: 'Storage Module', fn: checkStorageModule },
    { name: 'Next.js Config', fn: checkNextConfig },
    { name: 'File Structure', fn: checkFileStructure }
  ];

  let allPassed = true;
  checks.forEach(check => {
    // We already ran these checks above, so just summarize
    console.log(`   ✅ ${check.name}: Verified`);
  });

  return allPassed;
}

// Main execution
async function main() {
  const results = [
    checkEnvironmentVariables(),
    checkPackageDependencies(), 
    checkStorageModule(),
    checkNextConfig(),
    checkFileStructure()
  ];

  const allPassed = results.every(result => result);

  console.log('\n' + '='.repeat(50));
  if (allPassed) {
    console.log('🎉 MIGRATION VERIFICATION PASSED!');
    console.log('\n✅ All components are properly configured for R2');
    console.log('✅ Ready to test R2 connection');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/test-r2-connection.js');
    console.log('   2. Test file uploads in the application');
    console.log('   3. Verify AI processing still works');
  } else {
    console.log('❌ MIGRATION VERIFICATION FAILED!');
    console.log('\n🔧 Please fix the issues above before proceeding');
  }
  console.log('='.repeat(50));

  return allPassed;
}

if (require.main === module) {
  main()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { main };
