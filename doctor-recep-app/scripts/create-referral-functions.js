#!/usr/bin/env node

/**
 * <PERSON>ript to create missing referral database functions
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

async function createReferralFunctions() {
  try {
    console.log('🔧 Creating Missing Referral Database Functions');
    console.log('===============================================\n');

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing environment variables');
      console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
      return;
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Function 1: handle_referral_conversion
    console.log('📝 Creating handle_referral_conversion function...');
    const handleReferralConversionSQL = `
      CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
      RETURNS BOOLEAN AS $$
      DECLARE
          referrer_uuid UUID;
          referral_record RECORD;
          discount_amount DECIMAL(10,2) := 10.00; -- 10% discount
      BEGIN
          -- Get referrer information
          SELECT referred_by INTO referrer_uuid
          FROM doctors
          WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
          
          IF referrer_uuid IS NULL THEN
              RETURN FALSE;
          END IF;
          
          -- Update conversion date for referred doctor
          UPDATE doctors
          SET conversion_date = NOW()
          WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
          
          -- Update referral analytics
          UPDATE referral_analytics
          SET status = 'converted',
              conversion_date = NOW(),
              discount_earned = discount_amount
          WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
          
          -- Update referrer stats
          UPDATE doctors
          SET successful_referrals = successful_referrals + 1,
              referral_discount_earned = referral_discount_earned + discount_amount,
              available_discount_amount = available_discount_amount + discount_amount
          WHERE id = referrer_uuid;
          
          RETURN TRUE;
      END;
      $$ language 'plpgsql';
    `;

    const { error: error1 } = await supabase.rpc('exec_sql', { sql: handleReferralConversionSQL });
    if (error1) {
      console.log('❌ Failed to create handle_referral_conversion function:', error1.message);
    } else {
      console.log('✅ handle_referral_conversion function created successfully');
    }

    // Function 2: create_referral_discount
    console.log('\n📝 Creating create_referral_discount function...');
    const createReferralDiscountSQL = `
      CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
      RETURNS UUID AS $$
      DECLARE
          discount_id UUID;
          discount_amount DECIMAL(10,2);
      BEGIN
          -- Calculate 10% discount, max 100% of next bill
          discount_amount := LEAST(referred_amount * 0.10, referred_amount);
          
          -- Create referral discount
          INSERT INTO referral_discounts (
              doctor_id,
              referral_analytics_id,
              discount_amount,
              original_amount,
              valid_until
          ) VALUES (
              referrer_doctor_id,
              referral_analytics_id,
              discount_amount,
              referred_amount,
              NOW() + INTERVAL '12 months'
          ) RETURNING id INTO discount_id;
          
          -- Update doctor's available discount
          UPDATE doctors 
          SET available_discount_amount = available_discount_amount + discount_amount
          WHERE id = referrer_doctor_id;
          
          RETURN discount_id;
      END;
      $$ language 'plpgsql';
    `;

    const { error: error2 } = await supabase.rpc('exec_sql', { sql: createReferralDiscountSQL });
    if (error2) {
      console.log('❌ Failed to create create_referral_discount function:', error2.message);
    } else {
      console.log('✅ create_referral_discount function created successfully');
    }

    // Function 3: Updated complete_payment function
    console.log('\n📝 Creating/updating complete_payment function...');
    const completePaymentSQL = `
      CREATE OR REPLACE FUNCTION complete_payment(transaction_id UUID)
      RETURNS BOOLEAN AS $$
      DECLARE
          transaction_record RECORD;
      BEGIN
          -- Get transaction details
          SELECT bt.*, d.referred_by INTO transaction_record
          FROM billing_transactions bt
          JOIN doctors d ON bt.doctor_id = d.id
          WHERE bt.id = transaction_id;
          
          IF NOT FOUND THEN
              RETURN FALSE;
          END IF;
          
          -- Update transaction status
          UPDATE billing_transactions
          SET payment_status = 'paid',
              payment_date = NOW()
          WHERE id = transaction_id;
          
          -- Update doctor billing status
          UPDATE doctors
          SET billing_status = 'active',
              last_payment_date = NOW(),
              next_billing_date = transaction_record.billing_period_end
          WHERE id = transaction_record.doctor_id;
          
          -- If this is a referred doctor's first payment, handle referral conversion
          IF transaction_record.referred_by IS NOT NULL THEN
              -- Check if this is their first payment
              IF NOT EXISTS (
                  SELECT 1 FROM billing_transactions 
                  WHERE doctor_id = transaction_record.doctor_id 
                  AND payment_status = 'paid' 
                  AND id != transaction_id
              ) THEN
                  -- Mark referral as converted
                  PERFORM handle_referral_conversion(transaction_record.doctor_id);
                  
                  -- Create discount for referrer
                  PERFORM create_referral_discount(
                      transaction_record.referred_by,
                      (SELECT id FROM referral_analytics WHERE referred_doctor_id = transaction_record.doctor_id LIMIT 1),
                      transaction_record.final_amount
                  );
              END IF;
          END IF;
          
          RETURN TRUE;
      END;
      $$ language 'plpgsql';
    `;

    const { error: error3 } = await supabase.rpc('exec_sql', { sql: completePaymentSQL });
    if (error3) {
      console.log('❌ Failed to create complete_payment function:', error3.message);
    } else {
      console.log('✅ complete_payment function created successfully');
    }

    // Test the functions
    console.log('\n🧪 Testing created functions...');
    
    const functionsToTest = [
      'handle_referral_conversion',
      'create_referral_discount',
      'complete_payment'
    ];

    for (const funcName of functionsToTest) {
      try {
        const { error } = await supabase.rpc(funcName, {
          referred_doctor_uuid: '00000000-0000-0000-0000-000000000000'
        });
        
        if (error && error.message.includes('function')) {
          console.log(`❌ ${funcName} function still missing`);
        } else {
          console.log(`✅ ${funcName} function exists and callable`);
        }
      } catch (err) {
        if (err.message.includes('function')) {
          console.log(`❌ ${funcName} function missing: ${err.message}`);
        } else {
          console.log(`✅ ${funcName} function exists (test error expected)`);
        }
      }
    }

    console.log('\n🎯 Referral functions creation completed!');
    console.log('\n💡 Note: Functions created using direct SQL execution.');
    console.log('   If exec_sql is not available, you may need to run these SQL commands manually in Supabase SQL Editor.');

  } catch (error) {
    console.error('💥 Fatal error during function creation:', error);
    console.log('\n📋 Manual SQL Commands to Run in Supabase SQL Editor:');
    console.log('Copy and paste these into your Supabase SQL Editor if the script fails:');
    console.log('\n-- 1. Handle Referral Conversion Function');
    console.log(handleReferralConversionSQL);
    console.log('\n-- 2. Create Referral Discount Function');
    console.log(createReferralDiscountSQL);
    console.log('\n-- 3. Complete Payment Function');
    console.log(completePaymentSQL);
  }
}

// Run the function creation
createReferralFunctions().catch(console.error);