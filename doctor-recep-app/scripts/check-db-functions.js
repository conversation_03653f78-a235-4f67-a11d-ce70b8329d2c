const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkDatabaseFunctions() {
  console.log('🔍 Checking database functions that should update doctors table...\n');

  try {
    // 1. Test if functions exist and what they do
    console.log('📋 TESTING DATABASE FUNCTIONS:');
    console.log('==============================');

    // Test create_referral_discount function
    console.log('🧪 Testing create_referral_discount function...');
    try {
      const { data, error } = await supabase.rpc('create_referral_discount', {
        referrer_doctor_id: '00000000-0000-0000-0000-000000000000',
        referral_analytics_id: '00000000-0000-0000-0000-000000000000',
        referred_amount: 100
      });
      
      if (error) {
        console.log(`   Function exists but test failed: ${error.message}`);
      } else {
        console.log(`   ✅ Function exists and returned: ${data}`);
      }
    } catch (e) {
      console.log(`   ❌ Function test error: ${e.message}`);
    }

    // Test handle_referral_conversion function
    console.log('\n🧪 Testing handle_referral_conversion function...');
    try {
      const { data, error } = await supabase.rpc('handle_referral_conversion', {
        referred_doctor_uuid: '00000000-0000-0000-0000-000000000000'
      });
      
      if (error) {
        console.log(`   Function exists but test failed: ${error.message}`);
      } else {
        console.log(`   ✅ Function exists and returned: ${data}`);
      }
    } catch (e) {
      console.log(`   ❌ Function test error: ${e.message}`);
    }

    // Test complete_payment function
    console.log('\n🧪 Testing complete_payment function...');
    try {
      const { data, error } = await supabase.rpc('complete_payment', {
        transaction_id: '00000000-0000-0000-0000-000000000000'
      });
      
      if (error) {
        console.log(`   Function exists but test failed: ${error.message}`);
      } else {
        console.log(`   ✅ Function exists and returned: ${data}`);
      }
    } catch (e) {
      console.log(`   ❌ Function test error: ${e.message}`);
    }

    // 2. Check when these functions were last called
    console.log('\n📅 CHECKING FUNCTION EXECUTION HISTORY:');
    console.log('=======================================');

    // Check if complete_payment was called for existing transactions
    const { data: paidTransactions } = await supabase
      .from('billing_transactions')
      .select('id, doctor_id, payment_status, payment_date, final_amount')
      .eq('payment_status', 'paid')
      .order('payment_date', { ascending: false })
      .limit(5);

    if (paidTransactions && paidTransactions.length > 0) {
      console.log('💳 Recent paid transactions:');
      for (const txn of paidTransactions) {
        console.log(`   ${txn.id.substring(0, 8)}... - ₹${txn.final_amount} - ${txn.payment_date}`);
        
        // Check if this doctor has applied discounts
        const { data: doctorDiscounts } = await supabase
          .from('referral_discounts')
          .select('id, discount_amount, status, applied_at')
          .eq('doctor_id', txn.doctor_id);

        if (doctorDiscounts && doctorDiscounts.length > 0) {
          console.log(`     Doctor has ${doctorDiscounts.length} discount records:`);
          doctorDiscounts.forEach(discount => {
            console.log(`       - ₹${discount.discount_amount} (${discount.status}) ${discount.applied_at || 'not applied'}`);
          });
        }
      }
    }

    // 3. Check what should happen vs what actually happened
    console.log('\n🔍 ANALYZING DISCOUNT APPLICATION LOGIC:');
    console.log('=========================================');

    // Get doctors who should have earned discounts
    const { data: referredDoctors } = await supabase
      .from('doctors')
      .select('id, name, referred_by, available_discount_amount, referral_discount_earned')
      .not('referred_by', 'is', null);

    if (referredDoctors && referredDoctors.length > 0) {
      for (const doctor of referredDoctors) {
        console.log(`\n👨‍⚕️ Referred Doctor: ${doctor.name}`);
        console.log(`   Current available: ₹${doctor.available_discount_amount || 0}`);
        console.log(`   Current earned: ₹${doctor.referral_discount_earned || 0}`);

        // Check if they have paid transactions
        const { data: doctorTransactions } = await supabase
          .from('billing_transactions')
          .select('id, payment_status, payment_date, final_amount')
          .eq('doctor_id', doctor.id)
          .eq('payment_status', 'paid');

        if (doctorTransactions && doctorTransactions.length > 0) {
          console.log(`   Has ${doctorTransactions.length} paid transactions:`);
          doctorTransactions.forEach(txn => {
            console.log(`     - ₹${txn.final_amount} on ${txn.payment_date}`);
          });

          // Check their referrer
          if (doctor.referred_by) {
            const { data: referrer } = await supabase
              .from('doctors')
              .select('id, name, available_discount_amount, referral_discount_earned, successful_referrals')
              .eq('id', doctor.referred_by)
              .single();

            if (referrer) {
              console.log(`   Referrer: ${referrer.name}`);
              console.log(`     Should have earned 20% of referred doctor's payments`);
              console.log(`     Current available: ₹${referrer.available_discount_amount || 0}`);
              console.log(`     Current earned: ₹${referrer.referral_discount_earned || 0}`);
              console.log(`     Successful referrals: ${referrer.successful_referrals || 0}`);

              // Calculate what the referrer should have earned
              const totalReferred = doctorTransactions.reduce((sum, txn) => sum + txn.final_amount, 0);
              const expectedEarnings = totalReferred * 0.20;
              console.log(`     Expected earnings (20% of ₹${totalReferred}): ₹${expectedEarnings.toFixed(2)}`);

              if (Math.abs(expectedEarnings - (referrer.referral_discount_earned || 0)) > 0.01) {
                console.log(`     ❌ MISMATCH: Function not updating referrer correctly`);
              } else {
                console.log(`     ✅ Referrer earnings are correct`);
              }
            }
          }
        } else {
          console.log(`   No paid transactions yet`);
        }
      }
    }

    // 4. Check if markPaymentPaid function calls complete_payment
    console.log('\n🔧 CHECKING markPaymentPaid INTEGRATION:');
    console.log('========================================');
    
    // Look for recent transactions marked as paid to see if complete_payment was called
    const { data: recentTransactions } = await supabase
      .from('billing_transactions')
      .select('id, doctor_id, payment_status, created_at, updated_at')
      .eq('payment_status', 'paid')
      .order('updated_at', { ascending: false })
      .limit(3);

    if (recentTransactions) {
      console.log('Recent transactions marked as paid:');
      for (const txn of recentTransactions) {
        console.log(`   ${txn.id.substring(0, 8)}... - Created: ${txn.created_at}, Updated: ${txn.updated_at}`);
        
        // Check if this triggered referral logic
        const { data: doctor } = await supabase
          .from('doctors')
          .select('referred_by')
          .eq('id', txn.doctor_id)
          .single();

        if (doctor && doctor.referred_by) {
          console.log(`     This doctor was referred - should have triggered complete_payment logic`);
          
          // Check if discount was created for the referrer
          const { data: createdDiscounts } = await supabase
            .from('referral_discounts')
            .select('id, discount_amount, status, created_at')
            .eq('applied_to_transaction_id', txn.id);

          if (createdDiscounts && createdDiscounts.length > 0) {
            console.log(`     ✅ Discount was created: ₹${createdDiscounts[0].discount_amount}`);
          } else {
            console.log(`     ❌ No discount created for this transaction`);
          }
        } else {
          console.log(`     Doctor was not referred - no referral logic needed`);
        }
      }
    }

    console.log('\n✅ DATABASE FUNCTION ANALYSIS COMPLETE');
    console.log('=====================================');
    console.log('This will help identify if functions exist but aren\'t being called,');
    console.log('or if they exist but have bugs in their logic.');

  } catch (error) {
    console.error('❌ Error checking database functions:', error);
  }
}

if (require.main === module) {
  checkDatabaseFunctions();
}

module.exports = { checkDatabaseFunctions };