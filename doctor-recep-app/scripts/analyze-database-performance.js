#!/usr/bin/env node

/**
 * Analyze database performance and identify optimization opportunities
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function analyzeDatabase() {
  console.log('🔍 Analyzing Database Performance...\n');

  try {
    // 1. Check table sizes and row counts
    console.log('📊 TABLE ANALYSIS:');
    console.log('==================');
    
    const tables = ['consultations', 'doctors', 'contact_requests', 'billing_transactions', 'referral_discounts'];
    
    for (const table of tables) {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (!error) {
        console.log(`   ${table}: ${count} rows`);
      }
    }

    // 2. Check for missing indexes on frequently queried columns
    console.log('\n🔍 INDEX ANALYSIS:');
    console.log('==================');
    
    // Check consultations table queries
    const { data: consultations } = await supabase
      .from('consultations')
      .select('doctor_id, status, created_at')
      .limit(1);
    
    if (consultations) {
      console.log('   ✅ Consultations table accessible');
      console.log('   📋 Frequently queried columns:');
      console.log('      - doctor_id (for multi-tenant filtering)');
      console.log('      - status (for filtering pending/approved)');
      console.log('      - created_at (for ordering)');
    }

    // 3. Check realtime subscriptions usage
    console.log('\n📡 REALTIME USAGE ANALYSIS:');
    console.log('===========================');
    
    const realtimeUsage = {
      'contact_requests': 'Admin dashboard notifications',
      'billing_transactions': 'Admin billing updates',
      'doctors': 'Admin doctor management',
      'referral_discounts': 'Admin referral tracking'
    };

    Object.entries(realtimeUsage).forEach(([table, usage]) => {
      console.log(`   📺 ${table}: ${usage}`);
    });

    // 4. Analyze query patterns
    console.log('\n🎯 QUERY PATTERN ANALYSIS:');
    console.log('==========================');
    
    console.log('   Most Common Queries:');
    console.log('   1. Get consultations by doctor_id (multi-tenant)');
    console.log('   2. Get pending contact requests (admin)');
    console.log('   3. Get billing stats (admin)');
    console.log('   4. Get doctor information');
    console.log('   5. Realtime subscriptions (admin only)');

    // 5. Check for potential optimizations
    console.log('\n💡 OPTIMIZATION OPPORTUNITIES:');
    console.log('==============================');
    
    // Check if we have proper indexes
    const indexRecommendations = [
      'consultations(doctor_id, created_at DESC) - for doctor dashboard',
      'consultations(status, created_at DESC) - for admin filtering',
      'contact_requests(status, created_at DESC) - for admin dashboard',
      'billing_transactions(doctor_id, created_at DESC) - for billing queries'
    ];

    indexRecommendations.forEach((rec, i) => {
      console.log(`   ${i + 1}. ${rec}`);
    });

    // 6. Realtime optimization recommendations
    console.log('\n🚨 REALTIME OPTIMIZATION:');
    console.log('=========================');
    
    console.log('   Current Issues:');
    console.log('   ❌ 69,583 realtime.list_changes calls (66.7% of DB time)');
    console.log('   ❌ Multiple realtime subscriptions in admin dashboard');
    console.log('   ❌ Realtime enabled for all tables');
    
    console.log('\n   Recommendations:');
    console.log('   ✅ Disable realtime for doctor-facing features');
    console.log('   ✅ Use polling for admin dashboard instead of realtime');
    console.log('   ✅ Limit realtime to critical notifications only');
    console.log('   ✅ Add debouncing to realtime handlers');

    // 7. Check current database configuration
    console.log('\n⚙️  CURRENT CONFIGURATION:');
    console.log('==========================');
    
    console.log('   Realtime Enabled Tables:');
    console.log('   - contact_requests (admin notifications)');
    console.log('   - billing_transactions (admin billing)');
    console.log('   - doctors (admin management)');
    console.log('   - referral_discounts (admin tracking)');
    console.log('   - consultations (NOT used for realtime)');

  } catch (error) {
    console.error('❌ Error analyzing database:', error.message);
  }
}

analyzeDatabase();
