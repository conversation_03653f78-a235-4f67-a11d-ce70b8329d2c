#!/usr/bin/env node

/**
 * <PERSON>ript to test referral database functions
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

async function testReferralFunctions() {
  try {
    console.log('🧪 Testing Referral Database Functions');
    console.log('=====================================\n');

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing environment variables');
      console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
      return;
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Test 1: Check if complete_payment function exists
    console.log('Test 1: Testing complete_payment function...');
    try {
      const { data, error } = await supabase.rpc('complete_payment', {
        transaction_id: '00000000-0000-0000-0000-000000000000' // Non-existent ID
      });
      
      if (error && error.message.includes('function')) {
        console.log('❌ complete_payment function does not exist');
        console.log('   Error:', error.message);
      } else {
        console.log('✅ complete_payment function exists');
      }
    } catch (err) {
      console.log('❌ Error calling complete_payment:', err.message);
    }

    // Test 2: Check if handle_referral_conversion function exists
    console.log('\nTest 2: Testing handle_referral_conversion function...');
    try {
      const { data, error } = await supabase.rpc('handle_referral_conversion', {
        referred_doctor_uuid: '00000000-0000-0000-0000-000000000000' // Non-existent ID
      });
      
      if (error && error.message.includes('function')) {
        console.log('❌ handle_referral_conversion function does not exist');
        console.log('   Error:', error.message);
      } else {
        console.log('✅ handle_referral_conversion function exists');
      }
    } catch (err) {
      console.log('❌ Error calling handle_referral_conversion:', err.message);
    }

    // Test 3: Check if create_referral_discount function exists
    console.log('\nTest 3: Testing create_referral_discount function...');
    try {
      const { data, error } = await supabase.rpc('create_referral_discount', {
        referrer_doctor_id: '00000000-0000-0000-0000-000000000000',
        referral_analytics_id: '00000000-0000-0000-0000-000000000000',
        referred_amount: 100.00
      });
      
      if (error && error.message.includes('function')) {
        console.log('❌ create_referral_discount function does not exist');
        console.log('   Error:', error.message);
      } else {
        console.log('✅ create_referral_discount function exists');
      }
    } catch (err) {
      console.log('❌ Error calling create_referral_discount:', err.message);
    }

    // Test 4: Check referral_analytics table structure
    console.log('\nTest 4: Checking referral_analytics table...');
    try {
      const { data, error } = await supabase
        .from('referral_analytics')
        .select('*')
        .limit(1);
      
      if (error) {
        console.log('❌ referral_analytics table not accessible:', error.message);
      } else {
        console.log('✅ referral_analytics table exists and accessible');
      }
    } catch (err) {
      console.log('❌ Error accessing referral_analytics:', err.message);
    }

    // Test 5: Check billing_transactions table
    console.log('\nTest 5: Checking billing_transactions table...');
    try {
      const { data, error } = await supabase
        .from('billing_transactions')
        .select('*')
        .limit(1);
      
      if (error) {
        console.log('❌ billing_transactions table not accessible:', error.message);
      } else {
        console.log('✅ billing_transactions table exists and accessible');
      }
    } catch (err) {
      console.log('❌ Error accessing billing_transactions:', err.message);
    }

    // Test 6: Check doctors table for referral columns
    console.log('\nTest 6: Checking doctors table referral columns...');
    try {
      const { data, error } = await supabase
        .from('doctors')
        .select('referral_code, referred_by, successful_referrals, referral_discount_earned, available_discount_amount')
        .limit(1);
      
      if (error) {
        console.log('❌ Doctors table referral columns not accessible:', error.message);
      } else {
        console.log('✅ Doctors table has all referral columns');
      }
    } catch (err) {
      console.log('❌ Error accessing doctors referral columns:', err.message);
    }

    console.log('\n🎯 Referral function tests completed!');

  } catch (error) {
    console.error('💥 Fatal error during testing:', error);
  }
}

// Run the test
testReferralFunctions().catch(console.error);