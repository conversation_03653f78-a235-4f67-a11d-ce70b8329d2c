const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function debugBillingStats() {
  console.log('🔍 Debugging Total Discounts Given calculation...\n');

  try {
    // 1. Check what getBillingStats() is actually calculating
    console.log('📊 CURRENT BILLING STATS CALCULATION:');
    console.log('======================================');
    
    // Replicate the exact logic from getBillingStats() function
    const { data: discountData, error: discountError } = await supabase
      .from('referral_discounts')
      .select('discount_amount, status, created_at, doctor_id')
      .eq('status', 'applied');

    if (discountError) {
      console.log(`❌ Error fetching discount data: ${discountError.message}`);
      return;
    }

    const referralDiscountsGiven = discountData?.reduce((sum, d) => sum + (parseFloat(d.discount_amount) || 0), 0) || 0;
    
    console.log(`🧮 getBillingStats() calculation: ₹${referralDiscountsGiven.toFixed(2)}`);
    console.log(`📋 Applied discount records: ${discountData?.length || 0}`);

    if (discountData && discountData.length > 0) {
      console.log('\n💰 BREAKDOWN OF APPLIED DISCOUNTS:');
      console.log('==================================');
      discountData.forEach((discount, index) => {
        console.log(`${index + 1}. ₹${discount.discount_amount} (Doctor: ${discount.doctor_id.substring(0, 8)}...) - ${discount.created_at}`);
      });
    }

    // 2. Check all discount statuses
    console.log('\n📈 ALL DISCOUNT RECORDS BY STATUS:');
    console.log('===================================');
    
    const { data: allDiscounts } = await supabase
      .from('referral_discounts')
      .select('discount_amount, status, created_at')
      .order('created_at', { ascending: false });

    if (allDiscounts) {
      const statusGroups = allDiscounts.reduce((groups, discount) => {
        const status = discount.status || 'unknown';
        if (!groups[status]) groups[status] = [];
        groups[status].push(discount);
        return groups;
      }, {});

      Object.entries(statusGroups).forEach(([status, discounts]) => {
        const total = discounts.reduce((sum, d) => sum + (parseFloat(d.discount_amount) || 0), 0);
        console.log(`📊 ${status.toUpperCase()}: ${discounts.length} records, ₹${total.toFixed(2)} total`);
      });
    }

    // 3. Check if there are transactions with discount_amount that aren't in referral_discounts
    console.log('\n💳 TRANSACTION DISCOUNTS vs REFERRAL DISCOUNTS:');
    console.log('================================================');
    
    const { data: transactionDiscounts } = await supabase
      .from('billing_transactions')
      .select('discount_amount, final_amount, amount, created_at, doctor_id')
      .gt('discount_amount', 0);

    if (transactionDiscounts && transactionDiscounts.length > 0) {
      const transactionTotal = transactionDiscounts.reduce((sum, t) => sum + (parseFloat(t.discount_amount) || 0), 0);
      console.log(`💰 Total discounts in billing_transactions: ₹${transactionTotal.toFixed(2)}`);
      console.log(`📋 Transactions with discounts: ${transactionDiscounts.length}`);
      
      console.log('\n🔍 Recent transaction discounts:');
      transactionDiscounts.slice(0, 5).forEach((txn, index) => {
        console.log(`${index + 1}. ₹${txn.amount} - ₹${txn.discount_amount} = ₹${txn.final_amount} (${txn.created_at})`);
      });
    } else {
      console.log('💰 No discounts found in billing_transactions table');
    }

    // 4. Cross-check: Find discrepancies
    console.log('\n🔍 DISCREPANCY ANALYSIS:');
    console.log('========================');
    
    const referralTotal = referralDiscountsGiven;
    const transactionTotal = transactionDiscounts?.reduce((sum, t) => sum + (parseFloat(t.discount_amount) || 0), 0) || 0;
    
    console.log(`📊 Referral discounts table total: ₹${referralTotal.toFixed(2)}`);
    console.log(`💳 Transaction discounts total: ₹${transactionTotal.toFixed(2)}`);
    console.log(`🎯 Difference: ₹${Math.abs(referralTotal - transactionTotal).toFixed(2)}`);
    
    if (Math.abs(referralTotal - transactionTotal) > 0.01) {
      console.log('⚠️  MISMATCH DETECTED: The totals don\'t match!');
      console.log('   This suggests the issue is in the data source or calculation logic.');
    } else {
      console.log('✅ Totals match - issue might be in frontend display or real-time updates');
    }

    // 5. Check if 20% calculation is being applied correctly
    console.log('\n🧮 DISCOUNT PERCENTAGE VERIFICATION:');
    console.log('====================================');
    
    const { data: discountDetails } = await supabase
      .from('referral_discounts')
      .select('discount_amount, original_amount, discount_percentage')
      .not('original_amount', 'is', null)
      .limit(10);

    if (discountDetails && discountDetails.length > 0) {
      console.log('🔍 Checking if discounts are calculated at 20%:');
      discountDetails.forEach((discount, index) => {
        const expectedDiscount = parseFloat(discount.original_amount) * 0.20;
        const actualDiscount = parseFloat(discount.discount_amount);
        const isCorrect = Math.abs(expectedDiscount - actualDiscount) < 0.01;
        
        console.log(`${index + 1}. ₹${discount.original_amount} × 20% = ₹${expectedDiscount.toFixed(2)} | Actual: ₹${actualDiscount.toFixed(2)} ${isCorrect ? '✅' : '❌'}`);
      });
    }

    // 6. Check the frontend getBillingStats API call
    console.log('\n🌐 FRONTEND API SIMULATION:');
    console.log('============================');
    
    try {
      // Simulate what the frontend API call does
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/referral_discounts?status=eq.applied&select=discount_amount`, {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        const apiTotal = data.reduce((sum, d) => sum + (parseFloat(d.discount_amount) || 0), 0);
        console.log(`🌐 Direct API call total: ₹${apiTotal.toFixed(2)}`);
        console.log(`📊 API returned ${data.length} records`);
      } else {
        console.log('❌ API call failed');
      }
    } catch (e) {
      console.log('⚠️  Could not simulate API call:', e.message);
    }

    console.log('\n✅ DEBUG COMPLETE');
    console.log('=================');
    console.log('If totals are correct here but wrong in UI, check:');
    console.log('1. Real-time subscription updates');
    console.log('2. Frontend state management');
    console.log('3. Data caching issues');

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  }
}

if (require.main === module) {
  debugBillingStats().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { debugBillingStats };