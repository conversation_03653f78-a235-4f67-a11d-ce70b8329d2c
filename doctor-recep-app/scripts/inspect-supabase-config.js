const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function inspectSupabaseConfig() {
  console.log('🔍 Inspecting current Supabase configuration...\n');

  try {
    // 1. Check existing functions
    console.log('📋 CHECKING DATABASE FUNCTIONS:');
    console.log('=====================================');
    
    const { data: functions, error: funcError } = await supabase
      .from('pg_proc')
      .select('proname, prosrc')
      .in('proname', [
        'create_referral_discount',
        'handle_referral_conversion', 
        'complete_payment',
        'apply_referral_discount'
      ]);

    if (funcError) {
      console.log('⚠️  Cannot access pg_proc directly. Trying alternative method...');
      
      // Try to call functions to see if they exist
      const functionTests = [
        'create_referral_discount',
        'handle_referral_conversion',
        'complete_payment',
        'apply_referral_discount'
      ];
      
      for (const funcName of functionTests) {
        try {
          await supabase.rpc(funcName, {}).then(() => {
            console.log(`✅ Function '${funcName}' exists`);
          }).catch((err) => {
            if (err.message.includes('does not exist')) {
              console.log(`❌ Function '${funcName}' does not exist`);
            } else {
              console.log(`✅ Function '${funcName}' exists (test call failed as expected)`);
            }
          });
        } catch (e) {
          console.log(`❓ Cannot determine status of function '${funcName}'`);
        }
      }
    } else {
      functions?.forEach(func => {
        console.log(`✅ Function: ${func.proname}`);
        if (func.prosrc && func.prosrc.includes('0.20')) {
          console.log(`   💰 Uses 20% calculation`);
        } else if (func.prosrc && func.prosrc.includes('0.10')) {
          console.log(`   ⚠️  Uses 10% calculation`);
        }
      });
    }

    // 2. Check referral_discounts table structure
    console.log('\n📊 REFERRAL DISCOUNTS TABLE:');
    console.log('==============================');
    
    const { data: discounts, error: discountError } = await supabase
      .from('referral_discounts')
      .select('*')
      .limit(5);

    if (discountError) {
      console.log(`❌ Error accessing referral_discounts: ${discountError.message}`);
    } else {
      console.log(`✅ referral_discounts table exists`);
      console.log(`📈 Sample records: ${discounts?.length || 0}`);
      
      if (discounts && discounts.length > 0) {
        const sample = discounts[0];
        console.log(`   Columns: ${Object.keys(sample).join(', ')}`);
        
        // Check discount percentages in use
        const percentages = discounts.map(d => d.discount_percentage).filter(p => p);
        if (percentages.length > 0) {
          console.log(`   Discount percentages in use: ${[...new Set(percentages)].join('%, ')}%`);
        }
      }
    }

    // 3. Check current discount calculations in practice
    console.log('\n💰 CURRENT DISCOUNT CALCULATIONS:');
    console.log('===================================');
    
    const { data: billingStats } = await supabase
      .from('referral_discounts')
      .select('discount_amount, original_amount, discount_percentage, status')
      .eq('status', 'applied');

    if (billingStats && billingStats.length > 0) {
      console.log(`📊 Applied discounts: ${billingStats.length}`);
      
      billingStats.forEach((discount, index) => {
        const calculatedPercent = discount.original_amount > 0 
          ? ((discount.discount_amount / discount.original_amount) * 100).toFixed(1)
          : 0;
        
        console.log(`   ${index + 1}. ₹${discount.discount_amount} on ₹${discount.original_amount} (${calculatedPercent}% actual, ${discount.discount_percentage}% recorded)`);
      });
      
      // Calculate total discounts given
      const totalDiscounts = billingStats.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
      console.log(`💸 Total discounts given: ₹${totalDiscounts.toFixed(2)}`);
    } else {
      console.log('📊 No applied discounts found');
    }

    // 4. Check doctors table for referral fields
    console.log('\n👨‍⚕️ DOCTORS REFERRAL DATA:');
    console.log('============================');
    
    const { data: doctorSample } = await supabase
      .from('doctors')
      .select('referral_discount_earned, available_discount_amount, successful_referrals, referred_by')
      .not('referred_by', 'is', null)
      .limit(3);

    if (doctorSample && doctorSample.length > 0) {
      console.log(`👥 Doctors with referrals: ${doctorSample.length}+ (showing first 3)`);
      doctorSample.forEach((doc, index) => {
        console.log(`   ${index + 1}. Earned: ₹${doc.referral_discount_earned || 0}, Available: ₹${doc.available_discount_amount || 0}, Successful referrals: ${doc.successful_referrals || 0}`);
      });
    } else {
      console.log('👥 No doctors with referrals found');
    }

    // 5. Check billing transactions for discount application
    console.log('\n💳 BILLING TRANSACTIONS WITH DISCOUNTS:');
    console.log('========================================');
    
    const { data: transactions } = await supabase
      .from('billing_transactions')
      .select('amount, discount_amount, final_amount, payment_status')
      .gt('discount_amount', 0)
      .limit(5);

    if (transactions && transactions.length > 0) {
      console.log(`💰 Transactions with discounts: ${transactions.length}+ (showing first 5)`);
      transactions.forEach((txn, index) => {
        const discountPercent = txn.amount > 0 ? ((txn.discount_amount / txn.amount) * 100).toFixed(1) : 0;
        console.log(`   ${index + 1}. ₹${txn.amount} - ₹${txn.discount_amount} (${discountPercent}%) = ₹${txn.final_amount} [${txn.payment_status}]`);
      });
    } else {
      console.log('💰 No transactions with discounts found');
    }

    // 6. Test billing stats calculation
    console.log('\n📈 BILLING STATS CALCULATION TEST:');
    console.log('===================================');
    
    const { data: allDiscounts } = await supabase
      .from('referral_discounts')
      .select('discount_amount, status');

    if (allDiscounts) {
      const appliedDiscounts = allDiscounts.filter(d => d.status === 'applied');
      const totalCalculated = appliedDiscounts.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
      console.log(`🧮 Manual calculation of total discounts: ₹${totalCalculated.toFixed(2)}`);
      console.log(`📊 Applied discount records: ${appliedDiscounts.length}`);
      console.log(`📋 Total discount records: ${allDiscounts.length}`);
    }

    console.log('\n✅ INSPECTION COMPLETE');
    console.log('=======================');
    console.log('Use this information to identify where the discount calculation issue lies.');

  } catch (error) {
    console.error('❌ Error during inspection:', error);
  }
}

if (require.main === module) {
  inspectSupabaseConfig().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { inspectSupabaseConfig };