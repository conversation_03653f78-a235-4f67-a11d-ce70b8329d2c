-- Fix Database Functions to <PERSON><PERSON><PERSON> Update Doctors Table
-- Run this SQL in Supabase SQL Editor to fix the referral discount logic

-- Function 1: Handle referral conversion with correct 20% calculation
CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    referrer_uuid UUID;
    discount_amount DECIMAL(10,2);
    referred_amount DECIMAL(10,2);
BEGIN
    -- Get referrer information
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
    
    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Get the last payment amount of the referred doctor
    SELECT final_amount INTO referred_amount
    FROM billing_transactions 
    WHERE doctor_id = referred_doctor_uuid 
      AND payment_status = 'paid'
    ORDER BY payment_date DESC 
    LIMIT 1;
    
    -- Calculate 20% discount of the actual payment amount
    discount_amount := COALESCE(referred_amount * 0.20, 0);
    
    -- Only proceed if there's an amount to process
    IF discount_amount > 0 THEN
        -- Update conversion date for referred doctor
        UPDATE doctors
        SET conversion_date = NOW()
        WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
        
        -- Update referral analytics
        UPDATE referral_analytics
        SET status = 'converted',
            conversion_date = NOW(),
            discount_earned = discount_amount
        WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
        
        -- Update referrer stats with the actual calculated discount
        UPDATE doctors
        SET successful_referrals = successful_referrals + 1,
            referral_discount_earned = referral_discount_earned + discount_amount,
            available_discount_amount = available_discount_amount + discount_amount
        WHERE id = referrer_uuid;
        
        RAISE NOTICE 'Referral conversion: ₹% discount added to referrer %', discount_amount, referrer_uuid;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function 2: Create referral discount record with correct calculation
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount of the actual transaction amount
    discount_amount := referred_amount * 0.20;
    
    -- Create referral discount record
    INSERT INTO referral_discounts (
        doctor_id,
        referral_analytics_id,
        discount_amount,
        original_amount,
        discount_percentage,
        valid_until,
        status
    ) VALUES (
        referrer_doctor_id,
        referral_analytics_id,
        discount_amount,
        referred_amount,
        20.00,
        NOW() + INTERVAL '12 months',
        'applied'
    ) RETURNING id INTO discount_id;
    
    -- Update referrer's available discount in doctors table
    UPDATE doctors 
    SET available_discount_amount = available_discount_amount + discount_amount,
        referral_discount_earned = referral_discount_earned + discount_amount
    WHERE id = referrer_doctor_id;
    
    RAISE NOTICE 'Created referral discount: ₹% for referrer %', discount_amount, referrer_doctor_id;
    
    RETURN discount_id;
END;
$$ LANGUAGE plpgsql;

-- Function 3: Complete payment with proper referral handling
CREATE OR REPLACE FUNCTION complete_payment(transaction_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    transaction_record RECORD;
    referral_analytics_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Get transaction details with referral info
    SELECT bt.*, d.referred_by INTO transaction_record
    FROM billing_transactions bt
    JOIN doctors d ON bt.doctor_id = d.id
    WHERE bt.id = transaction_id;
    
    IF NOT FOUND THEN
        RAISE NOTICE 'Transaction % not found', transaction_id;
        RETURN FALSE;
    END IF;
    
    -- Update transaction status
    UPDATE billing_transactions
    SET payment_status = 'paid',
        payment_date = NOW(),
        updated_at = NOW()
    WHERE id = transaction_id;
    
    -- Update doctor billing status
    UPDATE doctors
    SET billing_status = 'active',
        last_payment_date = NOW(),
        next_billing_date = transaction_record.billing_period_end
    WHERE id = transaction_record.doctor_id;
    
    RAISE NOTICE 'Payment completed for transaction %, doctor %', transaction_id, transaction_record.doctor_id;
    
    -- Handle referral conversion if this is a referred doctor's payment
    IF transaction_record.referred_by IS NOT NULL THEN
        -- Check if this is their first payment (for conversion)
        DECLARE
            is_first_payment BOOLEAN;
        BEGIN
            SELECT NOT EXISTS (
                SELECT 1 FROM billing_transactions 
                WHERE doctor_id = transaction_record.doctor_id 
                AND payment_status = 'paid' 
                AND id != transaction_id
            ) INTO is_first_payment;
            
            IF is_first_payment THEN
                RAISE NOTICE 'First payment for referred doctor %, processing referral conversion', transaction_record.doctor_id;
                
                -- Get referral analytics ID
                SELECT id INTO referral_analytics_id 
                FROM referral_analytics 
                WHERE referred_doctor_id = transaction_record.doctor_id 
                AND status = 'pending'
                LIMIT 1;
                
                -- Mark referral as converted and update referrer stats
                PERFORM handle_referral_conversion(transaction_record.doctor_id);
                
                -- Create discount record if referral analytics exists
                IF referral_analytics_id IS NOT NULL THEN
                    PERFORM create_referral_discount(
                        transaction_record.referred_by,
                        referral_analytics_id,
                        transaction_record.final_amount
                    );
                    
                    RAISE NOTICE 'Created referral discount for referrer % based on amount %', 
                        transaction_record.referred_by, transaction_record.final_amount;
                END IF;
            ELSE
                -- For subsequent payments, still give referral bonus to referrer
                discount_amount := transaction_record.final_amount * 0.20;
                
                UPDATE doctors
                SET referral_discount_earned = referral_discount_earned + discount_amount,
                    available_discount_amount = available_discount_amount + discount_amount
                WHERE id = transaction_record.referred_by;
                
                -- Create discount record for subsequent payments too
                INSERT INTO referral_discounts (
                    doctor_id,
                    discount_amount,
                    original_amount,
                    discount_percentage,
                    valid_until,
                    status,
                    applied_to_transaction_id
                ) VALUES (
                    transaction_record.referred_by,
                    discount_amount,
                    transaction_record.final_amount,
                    20.00,
                    NOW() + INTERVAL '12 months',
                    'applied',
                    transaction_id
                );
                
                RAISE NOTICE 'Subsequent payment bonus: ₹% added to referrer %', 
                    discount_amount, transaction_record.referred_by;
            END IF;
        END;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function 4: Fix existing doctor discount amounts
CREATE OR REPLACE FUNCTION fix_doctor_discount_amounts()
RETURNS VOID AS $$
DECLARE
    doctor_record RECORD;
    total_earned DECIMAL(10,2);
    total_used DECIMAL(10,2);
    correct_available DECIMAL(10,2);
BEGIN
    RAISE NOTICE 'Fixing doctor discount amounts based on actual applied discounts...';
    
    -- Loop through all doctors who should have discounts
    FOR doctor_record IN 
        SELECT DISTINCT d.id, d.name
        FROM doctors d
        JOIN referral_discounts rd ON rd.doctor_id = d.id
        WHERE rd.status = 'applied'
    LOOP
        -- Calculate total earned from applied discounts
        SELECT COALESCE(SUM(discount_amount), 0) INTO total_earned
        FROM referral_discounts
        WHERE doctor_id = doctor_record.id AND status = 'applied';
        
        -- Calculate total used from transactions
        SELECT COALESCE(SUM(bt.discount_amount), 0) INTO total_used
        FROM billing_transactions bt
        WHERE bt.doctor_id = doctor_record.id AND bt.discount_amount > 0;
        
        -- Calculate correct available amount
        correct_available := total_earned - total_used;
        
        -- Update doctor record
        UPDATE doctors
        SET referral_discount_earned = total_earned,
            available_discount_amount = correct_available
        WHERE id = doctor_record.id;
        
        RAISE NOTICE 'Fixed doctor %: earned ₹%, available ₹%', 
            doctor_record.name, total_earned, correct_available;
    END LOOP;
    
    RAISE NOTICE 'Doctor discount amounts fix completed';
END;
$$ LANGUAGE plpgsql;

-- Execute the fix for existing data
SELECT fix_doctor_discount_amounts();

-- Verification query
DO $$
BEGIN
    RAISE NOTICE 'Database functions updated successfully!';
    RAISE NOTICE 'Functions fixed:';
    RAISE NOTICE '- handle_referral_conversion: Now uses 20%% of actual payment amount';
    RAISE NOTICE '- create_referral_discount: Properly updates doctors table';
    RAISE NOTICE '- complete_payment: Handles both first and subsequent payments';
    RAISE NOTICE '- fix_doctor_discount_amounts: Corrected existing data';
    RAISE NOTICE '';
    RAISE NOTICE 'Doctor discount amounts should now be correct!';
END $$;