const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function executeSQL(sql, description) {
  console.log(`📝 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return false;
    }
    console.log(`✅ ${description} completed`);
    return true;
  } catch (err) {
    console.error(`❌ Unexpected error: ${err.message}`);
    return false;
  }
}

async function fixDiscountCalculation() {
  console.log('🔧 Fixing discount calculation to 20% via direct SQL execution...');

  // SQL to update create_referral_discount function
  const createReferralDiscountSQL = `
CREATE OR REPLACE FUNCTION create_referral_discount(referrer_doctor_id UUID, referral_analytics_id UUID, referred_amount DECIMAL)
RETURNS UUID AS $$
DECLARE
    discount_id UUID;
    discount_amount DECIMAL(10,2);
BEGIN
    -- Calculate 20% discount (updated from 10%)
    discount_amount := referred_amount * 0.20;
    
    INSERT INTO referral_discounts (
        doctor_id,
        referral_analytics_id,
        discount_amount,
        original_amount,
        valid_until,
        discount_percentage
    ) VALUES (
        referrer_doctor_id,
        referral_analytics_id,
        discount_amount,
        referred_amount,
        NOW() + INTERVAL '12 months',
        20.00
    ) RETURNING id INTO discount_id;
    
    UPDATE doctors 
    SET available_discount_amount = available_discount_amount + discount_amount,
        referral_discount_earned = referral_discount_earned + discount_amount
    WHERE id = referrer_doctor_id;
    
    RETURN discount_id;
END;
$$ LANGUAGE plpgsql;
  `;

  // SQL to update handle_referral_conversion function
  const handleReferralConversionSQL = `
CREATE OR REPLACE FUNCTION handle_referral_conversion(referred_doctor_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    referrer_uuid UUID;
    discount_amount DECIMAL(10,2);
    referred_amount DECIMAL(10,2);
BEGIN
    SELECT referred_by INTO referrer_uuid
    FROM doctors
    WHERE id = referred_doctor_uuid AND referred_by IS NOT NULL;
    
    IF referrer_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    SELECT final_amount INTO referred_amount
    FROM billing_transactions 
    WHERE doctor_id = referred_doctor_uuid 
      AND payment_status = 'paid'
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- Calculate 20% discount (updated from 10%)
    discount_amount := COALESCE(referred_amount * 0.20, 20.00);
    
    UPDATE doctors
    SET conversion_date = NOW()
    WHERE id = referred_doctor_uuid AND conversion_date IS NULL;
    
    UPDATE referral_analytics
    SET status = 'converted',
        conversion_date = NOW(),
        discount_earned = discount_amount
    WHERE referred_doctor_id = referred_doctor_uuid AND status = 'pending';
    
    UPDATE doctors
    SET successful_referrals = successful_referrals + 1,
        referral_discount_earned = referral_discount_earned + discount_amount,
        available_discount_amount = available_discount_amount + discount_amount
    WHERE id = referrer_uuid;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
  `;

  // Execute the SQL updates
  try {
    console.log('📋 Checking if exec_sql function exists...');
    const { data: functions } = await supabase.rpc('exec_sql', { 
      sql_query: "SELECT proname FROM pg_proc WHERE proname = 'exec_sql';" 
    }).catch(() => ({ data: null }));

    if (!functions || functions.length === 0) {
      console.log('🔨 Creating exec_sql helper function...');
      
      // Try alternative method - direct query execution
      const { error: createFuncError } = await supabase
        .from('_dummy_table_for_sql')
        .select('*')
        .limit(0)
        .single()
        .catch(() => ({ error: null }));

      console.log('⚠️  exec_sql function not available. Attempting alternative method...');
      
      // Alternative: Try to update via edge functions or direct database access
      console.log('🔧 Please run the following SQL directly in Supabase SQL Editor:');
      console.log('');
      console.log('-- Update create_referral_discount function');
      console.log(createReferralDiscountSQL);
      console.log('');
      console.log('-- Update handle_referral_conversion function');
      console.log(handleReferralConversionSQL);
      console.log('');
      console.log('-- Update default discount percentage');
      console.log("ALTER TABLE referral_discounts ALTER COLUMN discount_percentage SET DEFAULT 20.00;");
      
      return false;
    }

    // Execute SQL updates
    await executeSQL(createReferralDiscountSQL, 'Updating create_referral_discount function');
    await executeSQL(handleReferralConversionSQL, 'Updating handle_referral_conversion function');
    await executeSQL(
      "ALTER TABLE referral_discounts ALTER COLUMN discount_percentage SET DEFAULT 20.00;",
      'Updating default discount percentage'
    );

    console.log('');
    console.log('✅ All discount calculations updated to 20%!');
    console.log('');
    console.log('📊 Summary of changes:');
    console.log('- create_referral_discount: Now calculates 20% of referee last bill amount');
    console.log('- handle_referral_conversion: Uses 20% discount rate');
    console.log('- referral_discounts table: Default percentage set to 20%');
    console.log('');
    console.log('🔄 Total Discounts Given should now show correct values in admin dashboard');

  } catch (error) {
    console.error('❌ Error executing SQL updates:', error);
    return false;
  }
}

if (require.main === module) {
  fixDiscountCalculation().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { fixDiscountCalculation };