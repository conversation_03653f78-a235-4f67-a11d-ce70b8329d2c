#!/usr/bin/env node

/**
 * Check existing files in database for migration
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkExistingFiles() {
  console.log('🔍 Checking existing files in database...\n');

  try {
    const { data: consultations, error } = await supabase
      .from('consultations')
      .select('id, primary_audio_url, additional_audio_urls, image_urls, created_at')
      .not('primary_audio_url', 'is', null)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }

    console.log(`📊 Found ${consultations.length} consultations with files\n`);

    if (consultations.length === 0) {
      console.log('✅ No existing files to migrate - you can start using R2 immediately!');
      return;
    }

    consultations.forEach((consultation, index) => {
      console.log(`${index + 1}. Consultation: ${consultation.id}`);
      console.log(`   Created: ${consultation.created_at}`);
      console.log(`   Primary Audio: ${consultation.primary_audio_url ? 'Yes' : 'No'}`);
      console.log(`   Additional Audio: ${consultation.additional_audio_urls?.length || 0} files`);
      console.log(`   Images: ${consultation.image_urls?.length || 0} files`);
      console.log('');
    });

    console.log('📋 Migration Options:');
    console.log('   1. Run: node scripts/migrate-supabase-to-r2.js');
    console.log('   2. Or start fresh with R2 (existing files stay in Supabase)');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkExistingFiles();
