#!/usr/bin/env node

/**
 * Check what URLs are actually in the database - Supabase vs R2
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkUrlStatus() {
  console.log('🔍 Checking URL status in database...\n');

  try {
    const { data: consultations, error } = await supabase
      .from('consultations')
      .select('id, primary_audio_url, additional_audio_urls, image_urls, created_at')
      .not('primary_audio_url', 'is', null)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }

    let supabaseCount = 0;
    let r2Count = 0;

    consultations.forEach((consultation, index) => {
      console.log(`${index + 1}. Consultation: ${consultation.id}`);
      
      // Check primary audio URL
      if (consultation.primary_audio_url) {
        const isR2 = consultation.primary_audio_url.includes('celerai.tallyup.pro');
        const isSupabase = consultation.primary_audio_url.includes('supabase.co');
        
        if (isR2) {
          console.log(`   ✅ Primary Audio: R2 - ${consultation.primary_audio_url}`);
          r2Count++;
        } else if (isSupabase) {
          console.log(`   🔄 Primary Audio: Supabase - ${consultation.primary_audio_url}`);
          supabaseCount++;
        } else {
          console.log(`   ❓ Primary Audio: Unknown - ${consultation.primary_audio_url}`);
        }
      }

      // Check additional audio URLs
      if (consultation.additional_audio_urls && consultation.additional_audio_urls.length > 0) {
        consultation.additional_audio_urls.forEach((url, i) => {
          const isR2 = url.includes('celerai.tallyup.pro');
          const isSupabase = url.includes('supabase.co');
          
          if (isR2) {
            console.log(`   ✅ Additional Audio ${i+1}: R2`);
            r2Count++;
          } else if (isSupabase) {
            console.log(`   🔄 Additional Audio ${i+1}: Supabase`);
            supabaseCount++;
          } else {
            console.log(`   ❓ Additional Audio ${i+1}: Unknown`);
          }
        });
      }

      // Check image URLs
      if (consultation.image_urls && consultation.image_urls.length > 0) {
        consultation.image_urls.forEach((url, i) => {
          const isR2 = url.includes('celerai.tallyup.pro');
          const isSupabase = url.includes('supabase.co');
          
          if (isR2) {
            console.log(`   ✅ Image ${i+1}: R2`);
            r2Count++;
          } else if (isSupabase) {
            console.log(`   🔄 Image ${i+1}: Supabase`);
            supabaseCount++;
          } else {
            console.log(`   ❓ Image ${i+1}: Unknown`);
          }
        });
      }
      
      console.log('');
    });

    console.log('=' .repeat(60));
    console.log('📊 URL STATUS SUMMARY:');
    console.log(`   R2 URLs: ${r2Count}`);
    console.log(`   Supabase URLs: ${supabaseCount}`);
    console.log(`   Migration Status: ${r2Count > 0 ? 'Partial' : 'Not Started'}`);
    console.log('=' .repeat(60));

    if (supabaseCount > 0) {
      console.log('\n🔄 BACKWARD COMPATIBILITY NEEDED:');
      console.log('   - Some files still use Supabase URLs');
      console.log('   - Keep Supabase image patterns in next.config.js');
      console.log('   - Consider running full migration script');
    } else {
      console.log('\n✅ MIGRATION COMPLETE:');
      console.log('   - All files use R2 URLs');
      console.log('   - Can remove Supabase image patterns');
      console.log('   - No backward compatibility needed');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkUrlStatus();
