#!/usr/bin/env node

/**
 * Verify multi-tenant folder structure in R2 URLs
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function verifyMultiTenant() {
  console.log('🔍 Verifying Multi-Tenant Structure...\n');

  try {
    const { data: consultations, error } = await supabase
      .from('consultations')
      .select('id, doctor_id, primary_audio_url, additional_audio_urls, image_urls')
      .not('primary_audio_url', 'is', null)
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }

    let allCorrect = true;
    const doctorFolders = new Set();

    consultations.forEach((consultation, index) => {
      console.log(`${index + 1}. Consultation: ${consultation.id}`);
      console.log(`   Doctor ID: ${consultation.doctor_id}`);
      
      // Check primary audio URL structure
      if (consultation.primary_audio_url) {
        const url = consultation.primary_audio_url;
        const expectedPattern = `consultation-audio/${consultation.doctor_id}/${consultation.id}/`;
        
        if (url.includes(expectedPattern)) {
          console.log(`   ✅ Primary Audio: Correct multi-tenant structure`);
          doctorFolders.add(consultation.doctor_id);
        } else {
          console.log(`   ❌ Primary Audio: INCORRECT structure`);
          console.log(`      Expected: .../${expectedPattern}...`);
          console.log(`      Actual: ${url}`);
          allCorrect = false;
        }
      }

      // Check additional audio URLs
      if (consultation.additional_audio_urls && consultation.additional_audio_urls.length > 0) {
        consultation.additional_audio_urls.forEach((url, i) => {
          const expectedPattern = `consultation-audio/${consultation.doctor_id}/${consultation.id}/`;
          
          if (url.includes(expectedPattern)) {
            console.log(`   ✅ Additional Audio ${i+1}: Correct structure`);
          } else {
            console.log(`   ❌ Additional Audio ${i+1}: INCORRECT structure`);
            allCorrect = false;
          }
        });
      }

      // Check image URLs
      if (consultation.image_urls && consultation.image_urls.length > 0) {
        consultation.image_urls.forEach((url, i) => {
          const expectedPattern = `consultation-images/${consultation.doctor_id}/${consultation.id}/`;
          
          if (url.includes(expectedPattern)) {
            console.log(`   ✅ Image ${i+1}: Correct structure`);
          } else {
            console.log(`   ❌ Image ${i+1}: INCORRECT structure`);
            allCorrect = false;
          }
        });
      }
      
      console.log('');
    });

    console.log('=' .repeat(60));
    console.log('🏢 MULTI-TENANT VERIFICATION SUMMARY:');
    console.log(`   Total Doctors: ${doctorFolders.size}`);
    console.log(`   Doctor IDs: ${Array.from(doctorFolders).join(', ')}`);
    console.log(`   Structure Correct: ${allCorrect ? '✅ YES' : '❌ NO'}`);
    console.log('=' .repeat(60));

    if (allCorrect) {
      console.log('\n🎉 MULTI-TENANT STRUCTURE IS PERFECT!');
      console.log('   ✅ Each doctor has isolated folders');
      console.log('   ✅ Each consultation has isolated subfolders');
      console.log('   ✅ No data leakage between doctors');
      console.log('   ✅ Scalable folder structure');
    } else {
      console.log('\n🚨 MULTI-TENANT ISSUES FOUND!');
      console.log('   ❌ Some files have incorrect folder structure');
      console.log('   ❌ Potential data leakage risk');
      console.log('   ❌ Needs immediate attention');
    }

    // Show expected folder structure
    console.log('\n📁 EXPECTED FOLDER STRUCTURE:');
    console.log('   celerai-storage/');
    console.log('   ├── consultation-audio/');
    console.log('   │   └── {doctorId}/');
    console.log('   │       └── {consultationId}/');
    console.log('   │           └── {fileName}');
    console.log('   └── consultation-images/');
    console.log('       └── {doctorId}/');
    console.log('           └── {consultationId}/');
    console.log('               └── {fileName}');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

verifyMultiTenant();
