const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkDiscountStatuses() {
  console.log('🔍 Checking actual discount statuses in database...\n');

  try {
    // Get all discount records with their statuses
    const { data: allDiscounts, error } = await supabase
      .from('referral_discounts')
      .select('id, discount_amount, status, applied_at, created_at, doctor_id, discount_percentage');

    if (error) {
      console.error('❌ Error fetching discounts:', error);
      return;
    }

    if (!allDiscounts || allDiscounts.length === 0) {
      console.log('📊 No discount records found in database');
      return;
    }

    console.log(`📊 Total discount records: ${allDiscounts.length}\n`);

    // Group by status
    const statusGroups = allDiscounts.reduce((groups, discount) => {
      const status = discount.status || 'null';
      if (!groups[status]) {
        groups[status] = [];
      }
      groups[status].push(discount);
      return groups;
    }, {});

    // Display each status group
    Object.entries(statusGroups).forEach(([status, discounts]) => {
      const total = discounts.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
      console.log(`📋 STATUS: ${status.toUpperCase()}`);
      console.log(`   Records: ${discounts.length}`);
      console.log(`   Total Amount: ₹${total.toFixed(2)}`);
      
      discounts.forEach((discount, index) => {
        console.log(`   ${index + 1}. ₹${discount.discount_amount} (${discount.discount_percentage}%) - Created: ${discount.created_at}`);
        if (discount.applied_at) {
          console.log(`      Applied: ${discount.applied_at}`);
        }
      });
      console.log('');
    });

    // Check what getBillingStats would calculate
    const appliedDiscounts = allDiscounts.filter(d => d.status === 'applied');
    const appliedTotal = appliedDiscounts.reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
    
    console.log('🧮 BILLING STATS CALCULATION:');
    console.log(`   Looking for status = 'applied': ${appliedDiscounts.length} records`);
    console.log(`   Total from 'applied' status: ₹${appliedTotal.toFixed(2)}`);
    console.log('   ^ This is what "Total Discounts Given" shows in frontend\n');

    // Check if there are other statuses that should be counted
    const otherStatuses = Object.keys(statusGroups).filter(s => s !== 'applied');
    if (otherStatuses.length > 0) {
      console.log('⚠️  OTHER STATUSES FOUND:');
      otherStatuses.forEach(status => {
        const total = statusGroups[status].reduce((sum, d) => sum + parseFloat(d.discount_amount || 0), 0);
        console.log(`   ${status}: ₹${total.toFixed(2)} (${statusGroups[status].length} records)`);
      });
      console.log('   ^ These are NOT counted in "Total Discounts Given"\n');
    }

    // Check doctors' available_discount_amount
    console.log('👨‍⚕️ DOCTORS DISCOUNT AMOUNTS:');
    console.log('==============================');
    
    const { data: doctors } = await supabase
      .from('doctors')
      .select('id, name, available_discount_amount, referral_discount_earned')
      .or('available_discount_amount.gt.0,referral_discount_earned.gt.0');

    if (doctors && doctors.length > 0) {
      doctors.forEach((doc, index) => {
        console.log(`${index + 1}. ${doc.name}`);
        console.log(`   Available: ₹${doc.available_discount_amount || 0}`);
        console.log(`   Earned: ₹${doc.referral_discount_earned || 0}`);
      });
    } else {
      console.log('No doctors with discount amounts found');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  checkDiscountStatuses();
}